"""
Qdrant collection manager for handling multiple indexes
"""
import asyncio
from typing import List, Dict, Any, Optional
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.exceptions import ResponseHandlingException
from langchain_openai import OpenAIEmbeddings

from config import Config

class QdrantManager:
    """
    Manages Qdrant collections and handles vector operations
    """
    
    def __init__(self):
        self.config = Config
        self.client = None
        self.embeddings = None
        self.collections_info = {}
    
    async def initialize(self):
        """Initialize Qdrant client and verify collections"""
        try:
            # Initialize Qdrant client
            self.client = QdrantClient(
                url=self.config.QDRANT_URL,
                api_key=self.config.QDRANT_API_KEY
            )
            
            # Initialize embeddings
            self.embeddings = OpenAIEmbeddings(
                model=self.config.OPENAI_EMBEDDING_MODEL,
                api_key=self.config.OPENAI_API_KEY
            )
            
            # Verify and get collection information
            await self._verify_collections()
            
            print("QdrantManager initialized successfully")
            
        except Exception as e:
            raise Exception(f"Failed to initialize Qdrant Manager: {str(e)}")
    
    async def _verify_collections(self):
        """Verify that the required collections exist and get their configurations"""
        try:
            # Get all collections
            collections_response = self.client.get_collections()
            existing_collections = {col.name: col for col in collections_response.collections}
            
            # Check TAX-RAG-1 collection
            tax_rag_name = self.config.QDRANT_TAX_RAG_COLLECTION
            if tax_rag_name in existing_collections:
                tax_rag_info = self.client.get_collection(tax_rag_name)
                self.collections_info[tax_rag_name] = {
                    'exists': True,
                    'vector_size': tax_rag_info.config.params.vectors.size,
                    'distance': tax_rag_info.config.params.vectors.distance,
                    'points_count': tax_rag_info.points_count
                }
                print(f"Collection '{tax_rag_name}' found:")
                print(f"   - Vector size: {tax_rag_info.config.params.vectors.size}")
                print(f"   - Distance metric: {tax_rag_info.config.params.vectors.distance}")
                print(f"   - Points count: {tax_rag_info.points_count}")
            else:
                print(f"Collection '{tax_rag_name}' not found")
                self.collections_info[tax_rag_name] = {'exists': False}
            
            # Check tax_documents collection
            tax_docs_name = self.config.QDRANT_TAX_DOCUMENTS_COLLECTION
            if tax_docs_name in existing_collections:
                tax_docs_info = self.client.get_collection(tax_docs_name)
                self.collections_info[tax_docs_name] = {
                    'exists': True,
                    'vector_size': tax_docs_info.config.params.vectors.size,
                    'distance': tax_docs_info.config.params.vectors.distance,
                    'points_count': tax_docs_info.points_count
                }
                print(f"Collection '{tax_docs_name}' found:")
                print(f"   - Vector size: {tax_docs_info.config.params.vectors.size}")
                print(f"   - Distance metric: {tax_docs_info.config.params.vectors.distance}")
                print(f"   - Points count: {tax_docs_info.points_count}")
            else:
                print(f"Collection '{tax_docs_name}' not found")
                self.collections_info[tax_docs_name] = {'exists': False}
            
            # Verify vector dimensions match OpenAI embeddings
            await self._verify_vector_dimensions()
            
        except Exception as e:
            raise Exception(f"Error verifying collections: {str(e)}")
    
    async def _verify_vector_dimensions(self):
        """Verify that collection vector dimensions match OpenAI embeddings"""
        # Get OpenAI embedding dimension
        test_embedding = await asyncio.to_thread(
            self.embeddings.embed_query, "test"
        )
        openai_dimension = len(test_embedding)
        print(f"OpenAI embedding dimension: {openai_dimension}")
        
        # Check each collection
        for collection_name, info in self.collections_info.items():
            if info.get('exists'):
                collection_dimension = info.get('vector_size')
                if collection_dimension != openai_dimension:
                    print(f"WARNING: Collection '{collection_name}' has dimension {collection_dimension}, but OpenAI embeddings are {openai_dimension}")
                    print(f"This may cause retrieval issues!")
                else:
                    print(f"Collection '{collection_name}' dimension matches OpenAI embeddings")
    
    async def search_collection(
        self, 
        collection_name: str, 
        query: str, 
        limit: int = 5,
        score_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search in a specific collection
        """
        try:
            # Check if collection exists
            if not self.collections_info.get(collection_name, {}).get('exists'):
                print(f"Collection '{collection_name}' does not exist")
                return []
            
            # Generate query embedding
            query_embedding = await asyncio.to_thread(
                self.embeddings.embed_query, query
            )
            
            # Search in Qdrant
            search_results = self.client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                limit=limit,
                score_threshold=score_threshold,
                with_payload=True
            )
            
            # Format results
            formatted_results = []
            for result in search_results:
                formatted_results.append({
                    'id': result.id,
                    'score': result.score,
                    'payload': result.payload,
                    'text': result.payload.get('text', '') if result.payload else '',
                    'metadata': result.payload.get('metadata', {}) if result.payload else {}
                })
            
            return formatted_results
            
        except Exception as e:
            print(f"Error searching collection '{collection_name}': {str(e)}")
            return []
    
    async def search_both_collections(
        self, 
        query: str, 
        limit_per_collection: int = 3,
        score_threshold: float = 0.7
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Search in both collections and return combined results
        """
        results = {}
        
        # Search TAX-RAG-1 collection
        tax_rag_results = await self.search_collection(
            self.config.QDRANT_TAX_RAG_COLLECTION,
            query,
            limit_per_collection,
            score_threshold
        )
        results['tax_rag'] = tax_rag_results
        
        # Search tax_documents collection
        tax_docs_results = await self.search_collection(
            self.config.QDRANT_TAX_DOCUMENTS_COLLECTION,
            query,
            limit_per_collection,
            score_threshold
        )
        results['tax_documents'] = tax_docs_results
        
        return results
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics for all collections"""
        stats = {}
        
        for collection_name, info in self.collections_info.items():
            if info.get('exists'):
                try:
                    collection_info = self.client.get_collection(collection_name)
                    stats[collection_name] = {
                        'points_count': collection_info.points_count,
                        'vector_size': collection_info.config.params.vectors.size,
                        'distance_metric': collection_info.config.params.vectors.distance.value,
                        'status': collection_info.status.value
                    }
                except Exception as e:
                    stats[collection_name] = {'error': str(e)}
            else:
                stats[collection_name] = {'status': 'not_found'}
        
        return stats
    
    def get_collections_info(self) -> Dict[str, Any]:
        """Get cached collection information"""
        return self.collections_info