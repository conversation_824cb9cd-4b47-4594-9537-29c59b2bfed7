{"name": "multimodal-rag-frontend", "version": "1.0.0", "description": "Frontend for Multimodal RAG Chatbot using CopilotKit", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@copilotkit/react-core": "^0.32.0", "@copilotkit/react-ui": "^0.32.0", "@copilotkit/react-textarea": "^0.32.0", "next": "14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.0", "react-dropzone": "^14.2.3", "lucide-react": "^0.294.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.51.0", "eslint-config-next": "14.0.0", "typescript": "^5.2.0"}, "keywords": ["chatbot", "rag", "copilotkit", "ai", "multimodal"], "author": "Your Name", "license": "MIT"}