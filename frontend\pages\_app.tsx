import type { AppProps } from 'next/app'
import { CopilotKit } from '@copilotkit/react-core'
import '@/styles/globals.css'

export default function App({ Component, pageProps }: AppProps) {
  return (
    <CopilotKit
      runtimeUrl="/api/copilotkit"
      showDevConsole={process.env.NODE_ENV === 'development'}
      publicApiKey={process.env.NEXT_PUBLIC_COPILOT_CLOUD_API_KEY}
    >
      <Component {...pageProps} />
    </CopilotKit>
  )
}