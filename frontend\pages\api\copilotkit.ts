import { NextApiRequest, NextApiResponse } from 'next'

// CopilotKit runtime handler
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  // Handle different CopilotKit endpoints
  const { method, body, query } = req

  if (method === 'GET') {
    // Health check endpoint
    return res.status(200).json({
      status: 'ok',
      message: 'CopilotKit API is running',
      endpoints: ['/api/copilotkit']
    })
  }

  if (method === 'POST') {
    try {
      console.log('CopilotKit API Request:', { body, query })

      // Handle different types of CopilotKit requests
      if (body && body.messages) {
        // Chat completion request
        const { messages, model = 'gpt-4', stream = false } = body

        // Get the latest user message
        const userMessages = messages.filter((msg: any) => msg.role === 'user')
        const latestMessage = userMessages[userMessages.length - 1]

        if (!latestMessage || !latestMessage.content) {
          return res.status(400).json({ error: 'No user message found' })
        }

        // Forward to FastAPI backend
        const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

        const response = await fetch(`${backendUrl}/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: latestMessage.content,
            session_id: 'copilotkit-session',
          }),
        })

        if (!response.ok) {
          throw new Error(`Backend request failed: ${response.status} ${response.statusText}`)
        }

        const data = await response.json()

        // Return in OpenAI-compatible format for CopilotKit
        const result = {
          id: `chatcmpl-${Date.now()}`,
          object: 'chat.completion',
          created: Math.floor(Date.now() / 1000),
          model: model,
          choices: [{
            index: 0,
            message: {
              role: 'assistant',
              content: data.response || 'I apologize, but I could not process your request.',
            },
            finish_reason: 'stop'
          }],
          usage: {
            prompt_tokens: latestMessage.content.length,
            completion_tokens: (data.response || '').length,
            total_tokens: latestMessage.content.length + (data.response || '').length
          }
        }

        console.log('CopilotKit API Response:', result)
        return res.status(200).json(result)
      }

      // Handle other CopilotKit requests
      return res.status(200).json({
        message: 'CopilotKit endpoint ready',
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      console.error('CopilotKit API error:', error)
      return res.status(500).json({
        error: {
          message: `API Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          type: 'api_error',
          code: 'internal_server_error'
        }
      })
    }
  }

  return res.status(405).json({ error: 'Method not allowed' })
}