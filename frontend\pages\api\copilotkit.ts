import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { messages, model = 'gpt-4' } = req.body

    // Get the latest message
    const latestMessage = messages && messages.length > 0 ? messages[messages.length - 1] : null

    if (!latestMessage || !latestMessage.content) {
      return res.status(400).json({ error: 'No message content provided' })
    }

    // Forward request to FastAPI backend
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

    const response = await fetch(`${backendUrl}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: latestMessage.content,
        session_id: 'copilotkit-session',
      }),
    })

    if (!response.ok) {
      throw new Error(`Backend request failed: ${response.status}`)
    }

    const data = await response.json()

    // Return in CopilotKit expected format
    res.status(200).json({
      choices: [{
        message: {
          role: 'assistant',
          content: data.response || 'I apologize, but I could not process your request.',
        },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0
      }
    })
  } catch (error) {
    console.error('CopilotKit API error:', error)
    res.status(500).json({
      choices: [{
        message: {
          role: 'assistant',
          content: 'I apologize, but I encountered an error. Please ensure the backend server is running on http://localhost:8000 and try again.',
        },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0
      }
    })
  }
}