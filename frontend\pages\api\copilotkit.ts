import { NextApiRequest, NextApiResponse } from 'next'

// CopilotKit GraphQL runtime handler
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  if (req.method === 'GET') {
    // Health check endpoint
    return res.status(200).json({
      status: 'ok',
      message: 'CopilotKit GraphQL API is running',
      endpoints: ['/api/copilotkit']
    })
  }

  if (req.method === 'POST') {
    try {
      console.log('CopilotKit API Request:', { body: req.body, query: req.query })

      const { operationName, query, variables } = req.body

      // Handle GraphQL queries from CopilotKit
      if (operationName === 'availableAgents') {
        // Return available agents
        return res.status(200).json({
          data: {
            availableAgents: {
              agents: [],
              __typename: 'AvailableAgents'
            }
          }
        })
      }

      if (operationName === 'generateCopilotResponse') {
        // Handle chat generation
        const { data } = variables

        if (!data || !data.messages || data.messages.length === 0) {
          return res.status(400).json({
            errors: [{ message: 'No messages provided' }]
          })
        }

        // Get the latest user message
        const userMessages = data.messages.filter((msg: any) => msg.role === 'user')
        const latestMessage = userMessages[userMessages.length - 1]

        if (!latestMessage || !latestMessage.content) {
          return res.status(400).json({
            errors: [{ message: 'No user message found' }]
          })
        }

        // Forward to FastAPI backend
        const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

        try {
          const response = await fetch(`${backendUrl}/chat`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              message: latestMessage.content,
              session_id: 'copilotkit-session',
            }),
          })

          if (!response.ok) {
            throw new Error(`Backend request failed: ${response.status} ${response.statusText}`)
          }

          const backendData = await response.json()

          // Return in CopilotKit GraphQL format
          const result = {
            data: {
              generateCopilotResponse: {
                threadId: `thread-${Date.now()}`,
                runId: `run-${Date.now()}`,
                extensions: {
                  openaiAssistantAPI: null,
                  __typename: 'Extensions'
                },
                status: {
                  code: 'SUCCESS',
                  __typename: 'BaseResponseStatus'
                },
                messages: [{
                  __typename: 'TextMessageOutput',
                  id: `msg-${Date.now()}`,
                  createdAt: new Date().toISOString(),
                  content: backendData.response || 'I apologize, but I could not process your request.',
                  role: 'assistant',
                  parentMessageId: latestMessage.id || null,
                  status: {
                    code: 'SUCCESS',
                    __typename: 'SuccessMessageStatus'
                  }
                }],
                metaEvents: [],
                __typename: 'CopilotResponse'
              }
            }
          }

          console.log('CopilotKit GraphQL Response:', JSON.stringify(result, null, 2))
          return res.status(200).json(result)

        } catch (backendError) {
          console.error('Backend API error:', backendError)

          // Return error response in GraphQL format
          return res.status(200).json({
            data: {
              generateCopilotResponse: {
                threadId: `thread-${Date.now()}`,
                runId: `run-${Date.now()}`,
                extensions: {
                  openaiAssistantAPI: null,
                  __typename: 'Extensions'
                },
                status: {
                  code: 'FAILED',
                  reason: 'Backend connection failed',
                  details: 'Please ensure the backend server is running on http://localhost:8000',
                  __typename: 'FailedResponseStatus'
                },
                messages: [{
                  __typename: 'TextMessageOutput',
                  id: `msg-${Date.now()}`,
                  createdAt: new Date().toISOString(),
                  content: 'I apologize, but I encountered an error connecting to the backend. Please ensure the backend server is running and try again.',
                  role: 'assistant',
                  parentMessageId: latestMessage.id || null,
                  status: {
                    code: 'FAILED',
                    reason: 'Backend connection failed',
                    __typename: 'FailedMessageStatus'
                  }
                }],
                metaEvents: [],
                __typename: 'CopilotResponse'
              }
            }
          })
        }
      }

      // Handle unknown GraphQL operations
      return res.status(200).json({
        errors: [{ message: `Unknown operation: ${operationName}` }]
      })

    } catch (error) {
      console.error('CopilotKit API error:', error)
      return res.status(500).json({
        errors: [{
          message: `API Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          extensions: {
            code: 'INTERNAL_SERVER_ERROR'
          }
        }]
      })
    }
  }

  return res.status(405).json({ errors: [{ message: 'Method not allowed' }] })
}