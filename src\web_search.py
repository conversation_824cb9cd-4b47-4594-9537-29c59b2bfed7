"""
Web search module using Brave Search API
"""
import aiohttp
import asyncio
from typing import List, Dict, Any
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage

from config import Config
from src.models import WebSearchResult

class WebSearchPipeline:
    """
    Handles web search queries using Brave Search API and summarization
    """
    
    def __init__(self):
        self.config = Config
        self.llm = ChatOpenAI(
            model_name=self.config.OPENAI_MODEL,
            openai_api_key=self.config.OPENAI_API_KEY,
            temperature=0.2
        )
    
    async def search_and_summarize(self, query: str, conversation_history: List[Dict] = None) -> WebSearchResult:
        """
        Perform web search and generate summarized answer
        
        Args:
            query: Search query
            conversation_history: Previous conversation for context
            
        Returns:
            WebSearchResult with summarized answer and sources
        """
        try:
            # Perform web search
            search_results = await self._brave_search(query)
            
            if not search_results:
                return WebSearchResult(
                    answer="I couldn't find relevant information for your query. Please try rephrasing your question or check your internet connection.",
                    sources=[],
                    search_results=[]
                )
            
            # Generate summarized answer
            answer = await self._summarize_results(query, search_results, conversation_history)
            
            # Extract sources
            sources = [result.get('url', '') for result in search_results if result.get('url')]
            
            return WebSearchResult(
                answer=answer,
                sources=sources,
                search_results=search_results
            )
            
        except Exception as e:
            return WebSearchResult(
                answer=f"I encountered an error while searching: {str(e)}. Please try again later.",
                sources=[],
                search_results=[]
            )
    
    async def _brave_search(self, query: str) -> List[Dict[str, Any]]:
        """
        Perform search using Brave Search API
        """
        headers = {
            "X-Subscription-Token": self.config.BRAVE_API_KEY,
            "Accept": "application/json"
        }
        
        params = {
            "q": query,
            "count": self.config.WEB_SEARCH_COUNT,
            "search_lang": "en",
            "country": "US",
            "safesearch": "moderate",
            "freshness": "pw"  # Past week for more recent results
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.config.BRAVE_SEARCH_URL,
                    headers=headers,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        # Extract web results
                        web_results = data.get('web', {}).get('results', [])
                        
                        # Format results
                        formatted_results = []
                        for result in web_results:
                            formatted_results.append({
                                'title': result.get('title', ''),
                                'url': result.get('url', ''),
                                'description': result.get('description', ''),
                                'published': result.get('published', ''),
                                'snippet': result.get('description', '')
                            })
                        
                        return formatted_results
                    
                    else:
                        print(f"Brave Search API error: {response.status}")
                        return []
                        
        except asyncio.TimeoutError:
            print("Brave Search API timeout")
            return []
        except Exception as e:
            print(f"Error calling Brave Search API: {str(e)}")
            return []
    
    async def _summarize_results(self, query: str, search_results: List[Dict], conversation_history: List[Dict] = None) -> str:
        """
        Summarize search results using LLM
        """
        # Prepare search results text
        results_text = ""
        for i, result in enumerate(search_results, 1):
            results_text += f"""
Result {i}:
Title: {result.get('title', 'No title')}
URL: {result.get('url', 'No URL')}
Description: {result.get('description', 'No description')}
Published: {result.get('published', 'Unknown date')}

"""
        
        # Prepare conversation history
        history_text = ""
        if conversation_history:
            recent_history = conversation_history[-3:]  # Last 3 exchanges
            for msg in recent_history:
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                history_text += f"{role.capitalize()}: {content}\n"
        
        # Create system prompt
        system_prompt = """
        You are an AI assistant that summarizes web search results to answer user questions.
        
        Your task is to:
        1. Analyze the provided search results
        2. Extract relevant information that answers the user's question
        3. Provide a comprehensive, well-structured answer
        4. Include specific details, dates, and facts when available
        5. Mention if information is recent or time-sensitive
        6. Be objective and factual
        
        Guidelines:
        - Synthesize information from multiple sources when possible
        - Clearly indicate when information is uncertain or conflicting
        - Use professional language
        - Include relevant context and background information
        - If the search results don't adequately answer the question, acknowledge this limitation
        """
        
        # Create user prompt
        user_prompt = f"""
        Previous conversation context:
        {history_text}
        
        User's question: {query}
        
        Web search results:
        {results_text}
        
        Please provide a comprehensive answer based on the search results above.
        """
        
        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = self.llm(messages)
            return response.content.strip()
            
        except Exception as e:
            return f"I found some search results but encountered an error while summarizing them: {str(e)}"
    
    async def get_recent_news(self, topic: str, days: int = 7) -> List[Dict]:
        """
        Get recent news about a specific topic
        """
        query = f"{topic} news"
        
        headers = {
            "X-Subscription-Token": self.config.BRAVE_API_KEY,
            "Accept": "application/json"
        }
        
        params = {
            "q": query,
            "count": 10,
            "search_lang": "en",
            "country": "US",
            "safesearch": "moderate",
            "freshness": f"pd{days}"  # Past N days
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.config.BRAVE_SEARCH_URL,
                    headers=headers,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        return data.get('web', {}).get('results', [])
                    else:
                        return []
                        
        except Exception as e:
            print(f"Error getting recent news: {str(e)}")
            return []