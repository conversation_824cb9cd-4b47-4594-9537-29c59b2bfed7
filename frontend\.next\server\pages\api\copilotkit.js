"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/copilotkit";
exports.ids = ["pages/api/copilotkit"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\copilotkit.ts */ \"(api)/./pages/api/copilotkit.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/copilotkit\",\n        pathname: \"/api/copilotkit\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/copilotkit.ts":
/*!*********************************!*\
  !*** ./pages/api/copilotkit.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n// CopilotKit runtime handler\nasync function handler(req, res) {\n    // Handle CORS\n    res.setHeader(\"Access-Control-Allow-Origin\", \"*\");\n    res.setHeader(\"Access-Control-Allow-Methods\", \"GET, POST, PUT, DELETE, OPTIONS\");\n    res.setHeader(\"Access-Control-Allow-Headers\", \"Content-Type, Authorization, X-Requested-With\");\n    if (req.method === \"OPTIONS\") {\n        res.status(200).end();\n        return;\n    }\n    // Handle different CopilotKit endpoints\n    const { method, body, query } = req;\n    if (method === \"GET\") {\n        // Health check endpoint\n        return res.status(200).json({\n            status: \"ok\",\n            message: \"CopilotKit API is running\",\n            endpoints: [\n                \"/api/copilotkit\"\n            ]\n        });\n    }\n    if (method === \"POST\") {\n        try {\n            console.log(\"CopilotKit API Request:\", {\n                body,\n                query\n            });\n            // Handle different types of CopilotKit requests\n            if (body && body.messages) {\n                // Chat completion request\n                const { messages, model = \"gpt-4\", stream = false } = body;\n                // Get the latest user message\n                const userMessages = messages.filter((msg)=>msg.role === \"user\");\n                const latestMessage = userMessages[userMessages.length - 1];\n                if (!latestMessage || !latestMessage.content) {\n                    return res.status(400).json({\n                        error: \"No user message found\"\n                    });\n                }\n                // Forward to FastAPI backend\n                const backendUrl = \"http://localhost:8000\" || 0;\n                const response = await fetch(`${backendUrl}/chat`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        message: latestMessage.content,\n                        session_id: \"copilotkit-session\"\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(`Backend request failed: ${response.status} ${response.statusText}`);\n                }\n                const data = await response.json();\n                // Return in OpenAI-compatible format for CopilotKit\n                const result = {\n                    id: `chatcmpl-${Date.now()}`,\n                    object: \"chat.completion\",\n                    created: Math.floor(Date.now() / 1000),\n                    model: model,\n                    choices: [\n                        {\n                            index: 0,\n                            message: {\n                                role: \"assistant\",\n                                content: data.response || \"I apologize, but I could not process your request.\"\n                            },\n                            finish_reason: \"stop\"\n                        }\n                    ],\n                    usage: {\n                        prompt_tokens: latestMessage.content.length,\n                        completion_tokens: (data.response || \"\").length,\n                        total_tokens: latestMessage.content.length + (data.response || \"\").length\n                    }\n                };\n                console.log(\"CopilotKit API Response:\", result);\n                return res.status(200).json(result);\n            }\n            // Handle other CopilotKit requests\n            return res.status(200).json({\n                message: \"CopilotKit endpoint ready\",\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"CopilotKit API error:\", error);\n            return res.status(500).json({\n                error: {\n                    message: `API Error: ${error instanceof Error ? error.message : \"Unknown error\"}`,\n                    type: \"api_error\",\n                    code: \"internal_server_error\"\n                }\n            });\n        }\n    }\n    return res.status(405).json({\n        error: \"Method not allowed\"\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/copilotkit.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();