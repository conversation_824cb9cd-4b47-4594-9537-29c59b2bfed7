"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/copilotkit";
exports.ids = ["pages/api/copilotkit"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\copilotkit.ts */ \"(api)/./pages/api/copilotkit.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/copilotkit\",\n        pathname: \"/api/copilotkit\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/copilotkit.ts":
/*!*********************************!*\
  !*** ./pages/api/copilotkit.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\nasync function handler(req, res) {\n    // Handle CORS\n    res.setHeader(\"Access-Control-Allow-Origin\", \"*\");\n    res.setHeader(\"Access-Control-Allow-Methods\", \"GET, POST, PUT, DELETE, OPTIONS\");\n    res.setHeader(\"Access-Control-Allow-Headers\", \"Content-Type, Authorization\");\n    if (req.method === \"OPTIONS\") {\n        res.status(200).end();\n        return;\n    }\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const { messages, model = \"gpt-4\" } = req.body;\n        // Get the latest message\n        const latestMessage = messages && messages.length > 0 ? messages[messages.length - 1] : null;\n        if (!latestMessage || !latestMessage.content) {\n            return res.status(400).json({\n                error: \"No message content provided\"\n            });\n        }\n        // Forward request to FastAPI backend\n        const backendUrl = \"http://localhost:8000\" || 0;\n        const response = await fetch(`${backendUrl}/chat`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                message: latestMessage.content,\n                session_id: \"copilotkit-session\"\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`Backend request failed: ${response.status}`);\n        }\n        const data = await response.json();\n        // Return in CopilotKit expected format\n        res.status(200).json({\n            choices: [\n                {\n                    message: {\n                        role: \"assistant\",\n                        content: data.response || \"I apologize, but I could not process your request.\"\n                    },\n                    finish_reason: \"stop\"\n                }\n            ],\n            usage: {\n                prompt_tokens: 0,\n                completion_tokens: 0,\n                total_tokens: 0\n            }\n        });\n    } catch (error) {\n        console.error(\"CopilotKit API error:\", error);\n        res.status(500).json({\n            choices: [\n                {\n                    message: {\n                        role: \"assistant\",\n                        content: \"I apologize, but I encountered an error. Please ensure the backend server is running on http://localhost:8000 and try again.\"\n                    },\n                    finish_reason: \"stop\"\n                }\n            ],\n            usage: {\n                prompt_tokens: 0,\n                completion_tokens: 0,\n                total_tokens: 0\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/copilotkit.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();