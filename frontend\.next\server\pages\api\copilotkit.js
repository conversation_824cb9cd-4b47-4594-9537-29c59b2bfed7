"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/copilotkit";
exports.ids = ["pages/api/copilotkit"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\copilotkit.ts */ \"(api)/./pages/api/copilotkit.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/copilotkit\",\n        pathname: \"/api/copilotkit\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_copilotkit_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/copilotkit.ts":
/*!*********************************!*\
  !*** ./pages/api/copilotkit.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n// CopilotKit GraphQL runtime handler\nasync function handler(req, res) {\n    // Handle CORS\n    res.setHeader(\"Access-Control-Allow-Origin\", \"*\");\n    res.setHeader(\"Access-Control-Allow-Methods\", \"GET, POST, PUT, DELETE, OPTIONS\");\n    res.setHeader(\"Access-Control-Allow-Headers\", \"Content-Type, Authorization, X-Requested-With\");\n    if (req.method === \"OPTIONS\") {\n        res.status(200).end();\n        return;\n    }\n    if (req.method === \"GET\") {\n        // Health check endpoint\n        return res.status(200).json({\n            status: \"ok\",\n            message: \"CopilotKit GraphQL API is running\",\n            endpoints: [\n                \"/api/copilotkit\"\n            ]\n        });\n    }\n    if (req.method === \"POST\") {\n        try {\n            console.log(\"CopilotKit API Request:\", {\n                body: req.body,\n                query: req.query\n            });\n            const { operationName, query, variables } = req.body;\n            // Handle GraphQL queries from CopilotKit\n            if (operationName === \"availableAgents\") {\n                // Return available agents\n                return res.status(200).json({\n                    data: {\n                        availableAgents: {\n                            agents: [],\n                            __typename: \"AvailableAgents\"\n                        }\n                    }\n                });\n            }\n            if (operationName === \"generateCopilotResponse\") {\n                // Handle chat generation\n                const { data } = variables;\n                if (!data || !data.messages || data.messages.length === 0) {\n                    return res.status(400).json({\n                        errors: [\n                            {\n                                message: \"No messages provided\"\n                            }\n                        ]\n                    });\n                }\n                // Get the latest user message\n                const userMessages = data.messages.filter((msg)=>msg.role === \"user\");\n                const latestMessage = userMessages[userMessages.length - 1];\n                if (!latestMessage || !latestMessage.content) {\n                    return res.status(400).json({\n                        errors: [\n                            {\n                                message: \"No user message found\"\n                            }\n                        ]\n                    });\n                }\n                // Forward to FastAPI backend\n                const backendUrl = \"http://localhost:8000\" || 0;\n                try {\n                    const response = await fetch(`${backendUrl}/chat`, {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            message: latestMessage.content,\n                            session_id: \"copilotkit-session\"\n                        })\n                    });\n                    if (!response.ok) {\n                        throw new Error(`Backend request failed: ${response.status} ${response.statusText}`);\n                    }\n                    const backendData = await response.json();\n                    // Return in CopilotKit GraphQL format\n                    const result = {\n                        data: {\n                            generateCopilotResponse: {\n                                threadId: `thread-${Date.now()}`,\n                                runId: `run-${Date.now()}`,\n                                extensions: {\n                                    openaiAssistantAPI: null,\n                                    __typename: \"Extensions\"\n                                },\n                                status: {\n                                    code: \"SUCCESS\",\n                                    __typename: \"BaseResponseStatus\"\n                                },\n                                messages: [\n                                    {\n                                        __typename: \"TextMessageOutput\",\n                                        id: `msg-${Date.now()}`,\n                                        createdAt: new Date().toISOString(),\n                                        content: backendData.response || \"I apologize, but I could not process your request.\",\n                                        role: \"assistant\",\n                                        parentMessageId: latestMessage.id || null,\n                                        status: {\n                                            code: \"SUCCESS\",\n                                            __typename: \"SuccessMessageStatus\"\n                                        }\n                                    }\n                                ],\n                                metaEvents: [],\n                                __typename: \"CopilotResponse\"\n                            }\n                        }\n                    };\n                    console.log(\"CopilotKit GraphQL Response:\", JSON.stringify(result, null, 2));\n                    return res.status(200).json(result);\n                } catch (backendError) {\n                    console.error(\"Backend API error:\", backendError);\n                    // Return error response in GraphQL format\n                    return res.status(200).json({\n                        data: {\n                            generateCopilotResponse: {\n                                threadId: `thread-${Date.now()}`,\n                                runId: `run-${Date.now()}`,\n                                extensions: {\n                                    openaiAssistantAPI: null,\n                                    __typename: \"Extensions\"\n                                },\n                                status: {\n                                    code: \"FAILED\",\n                                    reason: \"Backend connection failed\",\n                                    details: \"Please ensure the backend server is running on http://localhost:8000\",\n                                    __typename: \"FailedResponseStatus\"\n                                },\n                                messages: [\n                                    {\n                                        __typename: \"TextMessageOutput\",\n                                        id: `msg-${Date.now()}`,\n                                        createdAt: new Date().toISOString(),\n                                        content: \"I apologize, but I encountered an error connecting to the backend. Please ensure the backend server is running and try again.\",\n                                        role: \"assistant\",\n                                        parentMessageId: latestMessage.id || null,\n                                        status: {\n                                            code: \"FAILED\",\n                                            reason: \"Backend connection failed\",\n                                            __typename: \"FailedMessageStatus\"\n                                        }\n                                    }\n                                ],\n                                metaEvents: [],\n                                __typename: \"CopilotResponse\"\n                            }\n                        }\n                    });\n                }\n            }\n            // Handle unknown GraphQL operations\n            return res.status(200).json({\n                errors: [\n                    {\n                        message: `Unknown operation: ${operationName}`\n                    }\n                ]\n            });\n        } catch (error) {\n            console.error(\"CopilotKit API error:\", error);\n            return res.status(500).json({\n                errors: [\n                    {\n                        message: `API Error: ${error instanceof Error ? error.message : \"Unknown error\"}`,\n                        extensions: {\n                            code: \"INTERNAL_SERVER_ERROR\"\n                        }\n                    }\n                ]\n            });\n        }\n    }\n    return res.status(405).json({\n        errors: [\n            {\n                message: \"Method not allowed\"\n            }\n        ]\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/copilotkit.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcopilotkit&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccopilotkit.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();