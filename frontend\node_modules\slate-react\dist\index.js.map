{"version": 3, "file": "index.js", "sources": ["../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/defineProperty.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/arrayWithHoles.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/iterableToArrayLimit.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/arrayLikeToArray.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/nonIterableRest.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/slicedToArray.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/objectWithoutProperties.js", "../src/hooks/use-slate-static.tsx", "../src/utils/environment.ts", "../src/utils/weak-maps.ts", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/arrayWithoutHoles.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/iterableToArray.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/nonIterableSpread.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/toConsumableArray.js", "../src/utils/dom.ts", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/classCallCheck.js", "../src/utils/key.ts", "../src/plugin/react-editor.ts", "../src/utils/diff-text.ts", "../src/hooks/android-input-manager/android-input-manager.ts", "../src/hooks/use-is-mounted.tsx", "../src/hooks/use-isomorphic-layout-effect.ts", "../src/hooks/use-mutation-observer.ts", "../src/hooks/android-input-manager/use-android-input-manager.ts", "../src/utils/range-list.ts", "../src/components/string.tsx", "../src/components/leaf.tsx", "../src/components/text.tsx", "../src/components/element.tsx", "../src/hooks/use-decorate.ts", "../src/hooks/use-selected.ts", "../src/hooks/use-children.tsx", "../src/hooks/use-read-only.ts", "../src/hooks/use-slate.tsx", "../src/hooks/use-track-user-input.ts", "../src/utils/constants.ts", "../src/utils/hotkeys.ts", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/createClass.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/setPrototypeOf.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/inherits.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/typeof.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/assertThisInitialized.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/possibleConstructorReturn.js", "../../../.yarn/cache/@babel-runtime-npm-7.15.3-6333c71362-2f0b8d2d4e.zip/node_modules/@babel/runtime/helpers/getPrototypeOf.js", "../src/components/restore-dom/restore-dom-manager.ts", "../src/components/restore-dom/restore-dom.tsx", "../src/components/editable.tsx", "../src/hooks/use-focused.ts", "../src/hooks/use-slate-selector.tsx", "../src/components/slate.tsx", "../src/hooks/use-editor.tsx", "../src/hooks/use-slate-selection.tsx", "../src/utils/lines.ts", "../src/plugin/with-react.ts"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nmodule.exports = _defineProperty;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nmodule.exports = _arrayWithHoles;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nmodule.exports = _iterableToArrayLimit;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n\n  return arr2;\n}\n\nmodule.exports = _arrayLikeToArray;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "var arrayLikeToArray = require(\"./arrayLikeToArray.js\");\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return arrayLikeToArray(o, minLen);\n}\n\nmodule.exports = _unsupportedIterableToArray;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nmodule.exports = _nonIterableRest;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "var arrayWithHoles = require(\"./arrayWithHoles.js\");\n\nvar iterableToArrayLimit = require(\"./iterableToArrayLimit.js\");\n\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray.js\");\n\nvar nonIterableRest = require(\"./nonIterableRest.js\");\n\nfunction _slicedToArray(arr, i) {\n  return arrayWithHoles(arr) || iterableToArrayLimit(arr, i) || unsupportedIterableToArray(arr, i) || nonIterableRest();\n}\n\nmodule.exports = _slicedToArray;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nmodule.exports = _objectWithoutPropertiesLoose;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "var objectWithoutPropertiesLoose = require(\"./objectWithoutPropertiesLoose.js\");\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = _objectWithoutProperties;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "import { createContext, useContext } from 'react'\nimport { Editor } from 'slate'\nimport { ReactEditor } from '../plugin/react-editor'\n\n/**\n * A React context for sharing the editor object.\n */\n\nexport const EditorContext = createContext<ReactEditor | null>(null)\n\n/**\n * Get the current editor object from the React context.\n */\n\nexport const useSlateStatic = (): Editor => {\n  const editor = useContext(EditorContext)\n\n  if (!editor) {\n    throw new Error(\n      `The \\`useSlateStatic\\` hook must be used inside the <Slate> component's context.`\n    )\n  }\n\n  return editor\n}\n", "import React from 'react'\n\nexport const REACT_MAJOR_VERSION = parseInt(React.version.split('.')[0], 10)\n\nexport const IS_IOS =\n  typeof navigator !== 'undefined' &&\n  typeof window !== 'undefined' &&\n  /iPad|iPhone|iPod/.test(navigator.userAgent) &&\n  !window.MSStream\n\nexport const IS_APPLE =\n  typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent)\n\nexport const IS_ANDROID =\n  typeof navigator !== 'undefined' && /Android/.test(navigator.userAgent)\n\nexport const IS_FIREFOX =\n  typeof navigator !== 'undefined' &&\n  /^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent)\n\nexport const IS_WEBKIT =\n  typeof navigator !== 'undefined' &&\n  /AppleWebKit(?!.*Chrome)/i.test(navigator.userAgent)\n\n// \"modern\" Edge was released at 79.x\nexport const IS_EDGE_LEGACY =\n  typeof navigator !== 'undefined' &&\n  /Edge?\\/(?:[0-6][0-9]|[0-7][0-8])(?:\\.)/i.test(navigator.userAgent)\n\nexport const IS_CHROME =\n  typeof navigator !== 'undefined' && /Chrome/i.test(navigator.userAgent)\n\n// Native `beforeInput` events don't work well with react on Chrome 75\n// and older, Chrome 76+ can use `beforeInput` though.\nexport const IS_CHROME_LEGACY =\n  typeof navigator !== 'undefined' &&\n  /Chrome?\\/(?:[0-7][0-5]|[0-6][0-9])(?:\\.)/i.test(navigator.userAgent)\n\nexport const IS_ANDROID_CHROME_LEGACY =\n  IS_ANDROID &&\n  typeof navigator !== 'undefined' &&\n  /Chrome?\\/(?:[0-5]?\\d)(?:\\.)/i.test(navigator.userAgent)\n\n// Firefox did not support `beforeInput` until `v87`.\nexport const IS_FIREFOX_LEGACY =\n  typeof navigator !== 'undefined' &&\n  /^(?!.*Seamonkey)(?=.*Firefox\\/(?:[0-7][0-9]|[0-8][0-6])(?:\\.)).*/i.test(\n    navigator.userAgent\n  )\n\n// UC mobile browser\nexport const IS_UC_MOBILE =\n  typeof navigator !== 'undefined' && /.*UCBrowser/.test(navigator.userAgent)\n\n// Wechat browser (not including mac wechat)\nexport const IS_WECHATBROWSER =\n  typeof navigator !== 'undefined' &&\n  /.*Wechat/.test(navigator.userAgent) &&\n  !/.*MacWechat/.test(navigator.userAgent) // avoid lookbehind (buggy in safari < 16.4)\n\n// Check if DOM is available as React does internally.\n// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nexport const CAN_USE_DOM = !!(\n  typeof window !== 'undefined' &&\n  typeof window.document !== 'undefined' &&\n  typeof window.document.createElement !== 'undefined'\n)\n\n// COMPAT: Firefox/Edge Legacy don't support the `beforeinput` event\n// Chrome Legacy doesn't support `beforeinput` correctly\nexport const HAS_BEFORE_INPUT_SUPPORT =\n  (!IS_CHROME_LEGACY || !IS_ANDROID_CHROME_LEGACY) &&\n  !IS_EDGE_LEGACY &&\n  // globalThis is undefined in older browsers\n  typeof globalThis !== 'undefined' &&\n  globalThis.InputEvent &&\n  // @ts-ignore The `getTargetRanges` property isn't recognized.\n  typeof globalThis.InputEvent.prototype.getTargetRanges === 'function'\n", "import { <PERSON><PERSON><PERSON>, Editor, Node, Range, RangeRef, Text } from 'slate'\nimport { Action } from '../hooks/android-input-manager/android-input-manager'\nimport { TextDiff } from './diff-text'\nimport { Key } from './key'\n\n/**\n * Two weak maps that allow us rebuild a path given a node. They are populated\n * at render time such that after a render occurs we can always backtrack.\n */\n\nexport const NODE_TO_INDEX: WeakMap<Node, number> = new WeakMap()\nexport const NODE_TO_PARENT: WeakMap<Node, Ancestor> = new WeakMap()\n\n/**\n * Weak maps that allow us to go between Slate nodes and DOM nodes. These\n * are used to resolve DOM event-related logic into Slate actions.\n */\nexport const EDITOR_TO_WINDOW: WeakMap<Editor, Window> = new WeakMap()\nexport const EDITOR_TO_ELEMENT: WeakMap<Editor, HTMLElement> = new WeakMap()\nexport const EDITOR_TO_PLACEHOLDER: WeakMap<Editor, string> = new WeakMap()\nexport const EDITOR_TO_PLACEHOLDER_ELEMENT: WeakMap<\n  Editor,\n  HTMLElement\n> = new WeakMap()\nexport const ELEMENT_TO_NODE: WeakMap<HTMLElement, Node> = new WeakMap()\nexport const NODE_TO_ELEMENT: WeakMap<Node, HTMLElement> = new WeakMap()\nexport const NODE_TO_KEY: WeakMap<Node, Key> = new WeakMap()\nexport const EDITOR_TO_KEY_TO_ELEMENT: WeakMap<\n  Editor,\n  WeakMap<Key, HTMLElement>\n> = new WeakMap()\n\n/**\n * Weak maps for storing editor-related state.\n */\n\nexport const IS_READ_ONLY: WeakMap<Editor, boolean> = new WeakMap()\nexport const IS_FOCUSED: WeakMap<Editor, boolean> = new WeakMap()\nexport const IS_COMPOSING: WeakMap<Editor, boolean> = new WeakMap()\n\nexport const EDITOR_TO_USER_SELECTION: WeakMap<\n  Editor,\n  RangeRef | null\n> = new WeakMap()\n\n/**\n * Weak map for associating the context `onChange` context with the plugin.\n */\n\nexport const EDITOR_TO_ON_CHANGE = new WeakMap<Editor, () => void>()\n\n/**\n * Weak maps for saving pending state on composition stage.\n */\n\nexport const EDITOR_TO_SCHEDULE_FLUSH: WeakMap<\n  Editor,\n  () => void\n> = new WeakMap()\n\nexport const EDITOR_TO_PENDING_INSERTION_MARKS: WeakMap<\n  Editor,\n  Partial<Text> | null\n> = new WeakMap()\n\nexport const EDITOR_TO_USER_MARKS: WeakMap<\n  Editor,\n  Partial<Text> | null\n> = new WeakMap()\n\n/**\n * Android input handling specific weak-maps\n */\n\nexport const EDITOR_TO_PENDING_DIFFS: WeakMap<\n  Editor,\n  TextDiff[]\n> = new WeakMap()\n\nexport const EDITOR_TO_PENDING_ACTION: WeakMap<\n  Editor,\n  Action | null\n> = new WeakMap()\n\nexport const EDITOR_TO_PENDING_SELECTION: WeakMap<\n  Editor,\n  Range | null\n> = new WeakMap()\n\nexport const EDITOR_TO_FORCE_RENDER: WeakMap<Editor, () => void> = new WeakMap()\n\n/**\n * Symbols.\n */\n\nexport const PLACEHOLDER_SYMBOL = (Symbol('placeholder') as unknown) as string\nexport const MARK_PLACEHOLDER_SYMBOL = (Symbol(\n  'mark-placeholder'\n) as unknown) as string\n", "var arrayLikeToArray = require(\"./arrayLikeToArray.js\");\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return arrayLikeToArray(arr);\n}\n\nmodule.exports = _arrayWithoutHoles;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nmodule.exports = _iterableToArray;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nmodule.exports = _nonIterableSpread;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "var arrayWithoutHoles = require(\"./arrayWithoutHoles.js\");\n\nvar iterableToArray = require(\"./iterableToArray.js\");\n\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray.js\");\n\nvar nonIterableSpread = require(\"./nonIterableSpread.js\");\n\nfunction _toConsumableArray(arr) {\n  return arrayWithoutHoles(arr) || iterableToArray(arr) || unsupportedIterableToArray(arr) || nonIterableSpread();\n}\n\nmodule.exports = _toConsumableArray;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "/**\n * Types.\n */\n\n// COMPAT: This is required to prevent TypeScript aliases from doing some very\n// weird things for <PERSON>late's types with the same name as globals. (2019/11/27)\n// https://github.com/microsoft/TypeScript/issues/35002\nimport DOMNode = globalThis.Node\nimport DOMComment = globalThis.Comment\nimport DOMElement = globalThis.Element\nimport DOMText = globalThis.Text\nimport DOMRange = globalThis.Range\nimport DOMSelection = globalThis.Selection\nimport DOMStaticRange = globalThis.StaticRange\nimport { ReactEditor } from '../plugin/react-editor'\n\nexport {\n  DOMNode,\n  DOMComment,\n  DOMElement,\n  DOMText,\n  DOMRange,\n  DOMSelection,\n  DOMStaticRange,\n}\n\ndeclare global {\n  interface Window {\n    Selection: typeof Selection['constructor']\n    DataTransfer: typeof DataTransfer['constructor']\n    Node: typeof Node['constructor']\n  }\n}\n\nexport type DOMPoint = [Node, number]\n\n/**\n * Returns the host window of a DOM node\n */\n\nexport const getDefaultView = (value: any): Window | null => {\n  return (\n    (value && value.ownerDocument && value.ownerDocument.defaultView) || null\n  )\n}\n\n/**\n * Check if a DOM node is a comment node.\n */\n\nexport const isDOMComment = (value: any): value is DOMComment => {\n  return isDOMNode(value) && value.nodeType === 8\n}\n\n/**\n * Check if a DOM node is an element node.\n */\n\nexport const isDOMElement = (value: any): value is DOMElement => {\n  return isDOMNode(value) && value.nodeType === 1\n}\n\n/**\n * Check if a value is a DOM node.\n */\n\nexport const isDOMNode = (value: any): value is DOMNode => {\n  const window = getDefaultView(value)\n  return !!window && value instanceof window.Node\n}\n\n/**\n * Check if a value is a DOM selection.\n */\n\nexport const isDOMSelection = (value: any): value is DOMSelection => {\n  const window = value && value.anchorNode && getDefaultView(value.anchorNode)\n  return !!window && value instanceof window.Selection\n}\n\n/**\n * Check if a DOM node is an element node.\n */\n\nexport const isDOMText = (value: any): value is DOMText => {\n  return isDOMNode(value) && value.nodeType === 3\n}\n\n/**\n * Checks whether a paste event is a plaintext-only event.\n */\n\nexport const isPlainTextOnlyPaste = (event: ClipboardEvent) => {\n  return (\n    event.clipboardData &&\n    event.clipboardData.getData('text/plain') !== '' &&\n    event.clipboardData.types.length === 1\n  )\n}\n\n/**\n * Normalize a DOM point so that it always refers to a text node.\n */\n\nexport const normalizeDOMPoint = (domPoint: DOMPoint): DOMPoint => {\n  let [node, offset] = domPoint\n\n  // If it's an element node, its offset refers to the index of its children\n  // including comment nodes, so try to find the right text child node.\n  if (isDOMElement(node) && node.childNodes.length) {\n    let isLast = offset === node.childNodes.length\n    let index = isLast ? offset - 1 : offset\n    ;[node, index] = getEditableChildAndIndex(\n      node,\n      index,\n      isLast ? 'backward' : 'forward'\n    )\n    // If the editable child found is in front of input offset, we instead seek to its end\n    isLast = index < offset\n\n    // If the node has children, traverse until we have a leaf node. Leaf nodes\n    // can be either text nodes, or other void DOM nodes.\n    while (isDOMElement(node) && node.childNodes.length) {\n      const i = isLast ? node.childNodes.length - 1 : 0\n      node = getEditableChild(node, i, isLast ? 'backward' : 'forward')\n    }\n\n    // Determine the new offset inside the text node.\n    offset = isLast && node.textContent != null ? node.textContent.length : 0\n  }\n\n  // Return the node and offset.\n  return [node, offset]\n}\n\n/**\n * Determines whether the active element is nested within a shadowRoot\n */\n\nexport const hasShadowRoot = (node: Node | null) => {\n  let parent = node && node.parentNode\n  while (parent) {\n    if (parent.toString() === '[object ShadowRoot]') {\n      return true\n    }\n    parent = parent.parentNode\n  }\n  return false\n}\n\n/**\n * Get the nearest editable child and index at `index` in a `parent`, preferring\n * `direction`.\n */\n\nexport const getEditableChildAndIndex = (\n  parent: DOMElement,\n  index: number,\n  direction: 'forward' | 'backward'\n): [DOMNode, number] => {\n  const { childNodes } = parent\n  let child = childNodes[index]\n  let i = index\n  let triedForward = false\n  let triedBackward = false\n\n  // While the child is a comment node, or an element node with no children,\n  // keep iterating to find a sibling non-void, non-comment node.\n  while (\n    isDOMComment(child) ||\n    (isDOMElement(child) && child.childNodes.length === 0) ||\n    (isDOMElement(child) && child.getAttribute('contenteditable') === 'false')\n  ) {\n    if (triedForward && triedBackward) {\n      break\n    }\n\n    if (i >= childNodes.length) {\n      triedForward = true\n      i = index - 1\n      direction = 'backward'\n      continue\n    }\n\n    if (i < 0) {\n      triedBackward = true\n      i = index + 1\n      direction = 'forward'\n      continue\n    }\n\n    child = childNodes[i]\n    index = i\n    i += direction === 'forward' ? 1 : -1\n  }\n\n  return [child, index]\n}\n\n/**\n * Get the nearest editable child at `index` in a `parent`, preferring\n * `direction`.\n */\n\nexport const getEditableChild = (\n  parent: DOMElement,\n  index: number,\n  direction: 'forward' | 'backward'\n): DOMNode => {\n  const [child] = getEditableChildAndIndex(parent, index, direction)\n  return child\n}\n\n/**\n * Get a plaintext representation of the content of a node, accounting for block\n * elements which get a newline appended.\n *\n * The domNode must be attached to the DOM.\n */\n\nexport const getPlainText = (domNode: DOMNode) => {\n  let text = ''\n\n  if (isDOMText(domNode) && domNode.nodeValue) {\n    return domNode.nodeValue\n  }\n\n  if (isDOMElement(domNode)) {\n    for (const childNode of Array.from(domNode.childNodes)) {\n      text += getPlainText(childNode)\n    }\n\n    const display = getComputedStyle(domNode).getPropertyValue('display')\n\n    if (display === 'block' || display === 'list' || domNode.tagName === 'BR') {\n      text += '\\n'\n    }\n  }\n\n  return text\n}\n\n/**\n * Get x-slate-fragment attribute from data-slate-fragment\n */\nconst catchSlateFragment = /data-slate-fragment=\"(.+?)\"/m\nexport const getSlateFragmentAttribute = (\n  dataTransfer: DataTransfer\n): string | void => {\n  const htmlData = dataTransfer.getData('text/html')\n  const [, fragment] = htmlData.match(catchSlateFragment) || []\n  return fragment\n}\n\n/**\n * Get the x-slate-fragment attribute that exist in text/html data\n * and append it to the DataTransfer object\n */\nexport const getClipboardData = (\n  dataTransfer: DataTransfer,\n  clipboardFormatKey = 'x-slate-fragment'\n): DataTransfer => {\n  if (!dataTransfer.getData(`application/${clipboardFormatKey}`)) {\n    const fragment = getSlateFragmentAttribute(dataTransfer)\n    if (fragment) {\n      const clipboardData = new DataTransfer()\n      dataTransfer.types.forEach(type => {\n        clipboardData.setData(type, dataTransfer.getData(type))\n      })\n      clipboardData.setData(`application/${clipboardFormatKey}`, fragment)\n      return clipboardData\n    }\n  }\n  return dataTransfer\n}\n\n/**\n * Check whether a mutation originates from a editable element inside the editor.\n */\n\nexport const isTrackedMutation = (\n  editor: ReactEditor,\n  mutation: MutationRecord,\n  batch: MutationRecord[]\n): boolean => {\n  const { target } = mutation\n  if (isDOMElement(target) && target.matches('[contentEditable=\"false\"]')) {\n    return false\n  }\n\n  const { document } = ReactEditor.getWindow(editor)\n  if (document.contains(target)) {\n    return ReactEditor.hasDOMNode(editor, target, { editable: true })\n  }\n\n  const parentMutation = batch.find(({ addedNodes, removedNodes }) => {\n    for (const node of addedNodes) {\n      if (node === target || node.contains(target)) {\n        return true\n      }\n    }\n\n    for (const node of removedNodes) {\n      if (node === target || node.contains(target)) {\n        return true\n      }\n    }\n  })\n\n  if (!parentMutation || parentMutation === mutation) {\n    return false\n  }\n\n  // Target add/remove is tracked. Track the mutation if we track the parent mutation.\n  return isTrackedMutation(editor, parentMutation, batch)\n}\n", "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nmodule.exports = _classCallCheck;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "/**\n * An auto-incrementing identifier for keys.\n */\n\nlet n = 0\n\n/**\n * A class that keeps track of a key string. We use a full class here because we\n * want to be able to use them as keys in `WeakMap` objects.\n */\n\nexport class Key {\n  id: string\n\n  constructor() {\n    this.id = `${n++}`\n  }\n}\n", "import {\n  <PERSON>E<PERSON><PERSON>,\n  Editor,\n  Element,\n  Node,\n  Path,\n  Point,\n  Range,\n  <PERSON>rubber,\n  Transforms,\n} from 'slate'\nimport { TextDiff } from '../utils/diff-text'\nimport {\n  DOMElement,\n  DOMNode,\n  DOMPoint,\n  DOMRange,\n  DOMSelection,\n  D<PERSON>StaticRange,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  hasShadowRoot,\n  isDOMElement,\n  isDOMNode,\n  isDOMSelection,\n  normalizeD<PERSON>Point,\n} from '../utils/dom'\nimport { IS_ANDROID, IS_CHROME, IS_FIREFOX } from '../utils/environment'\n\nimport { Key } from '../utils/key'\nimport {\n  EDITOR_TO_ELEMENT,\n  EDITOR_TO_KEY_TO_ELEMENT,\n  EDITOR_TO_PENDING_DIFFS,\n  EDITOR_TO_SCHEDULE_FLUSH,\n  EDITOR_TO_WINDOW,\n  ELEMENT_TO_NODE,\n  IS_COMPOSING,\n  IS_FOCUSED,\n  IS_READ_ONLY,\n  NODE_TO_INDEX,\n  NODE_TO_KEY,\n  NODE_TO_PARENT,\n} from '../utils/weak-maps'\n\n/**\n * A React and DOM-specific version of the `Editor` interface.\n */\n\nexport interface ReactEditor extends BaseEditor {\n  hasEditableTarget: (\n    editor: ReactEditor,\n    target: EventTarget | null\n  ) => target is DOMNode\n  hasRange: (editor: ReactEditor, range: Range) => boolean\n  hasSelectableTarget: (\n    editor: ReactEditor,\n    target: EventTarget | null\n  ) => boolean\n  hasTarget: (\n    editor: ReactEditor,\n    target: EventTarget | null\n  ) => target is DOMNode\n  insertData: (data: DataTransfer) => void\n  insertFragmentData: (data: DataTransfer) => boolean\n  insertTextData: (data: DataTransfer) => boolean\n  isTargetInsideNonReadonlyVoid: (\n    editor: ReactEditor,\n    target: EventTarget | null\n  ) => boolean\n  setFragmentData: (\n    data: DataTransfer,\n    originEvent?: 'drag' | 'copy' | 'cut'\n  ) => void\n}\n\nexport interface ReactEditorInterface {\n  /**\n   * Experimental and android specific: Get pending diffs\n   */\n  androidPendingDiffs: (editor: Editor) => TextDiff[] | undefined\n\n  /**\n   * Experimental and android specific: Flush all pending diffs and cancel composition at the next possible time.\n   */\n  androidScheduleFlush: (editor: Editor) => void\n\n  /**\n   * Blur the editor.\n   */\n  blur: (editor: ReactEditor) => void\n\n  /**\n   * Deselect the editor.\n   */\n  deselect: (editor: ReactEditor) => void\n\n  /**\n   * Find the DOM node that implements DocumentOrShadowRoot for the editor.\n   */\n  findDocumentOrShadowRoot: (editor: ReactEditor) => Document | ShadowRoot\n\n  /**\n   * Get the target range from a DOM `event`.\n   */\n  findEventRange: (editor: ReactEditor, event: any) => Range\n\n  /**\n   * Find a key for a Slate node.\n   */\n  findKey: (editor: ReactEditor, node: Node) => Key\n\n  /**\n   * Find the path of Slate node.\n   */\n  findPath: (editor: ReactEditor, node: Node) => Path\n\n  /**\n   * Focus the editor.\n   */\n  focus: (editor: ReactEditor) => void\n\n  /**\n   * Return the host window of the current editor.\n   */\n  getWindow: (editor: ReactEditor) => Window\n\n  /**\n   * Check if a DOM node is within the editor.\n   */\n  hasDOMNode: (\n    editor: ReactEditor,\n    target: DOMNode,\n    options?: { editable?: boolean }\n  ) => boolean\n\n  /**\n   * Check if the target is editable and in the editor.\n   */\n  hasEditableTarget: (\n    editor: ReactEditor,\n    target: EventTarget | null\n  ) => target is DOMNode\n\n  /**\n   *\n   */\n  hasRange: (editor: ReactEditor, range: Range) => boolean\n\n  /**\n   * Check if the target can be selectable\n   */\n  hasSelectableTarget: (\n    editor: ReactEditor,\n    target: EventTarget | null\n  ) => boolean\n\n  /**\n   * Check if the target is in the editor.\n   */\n  hasTarget: (\n    editor: ReactEditor,\n    target: EventTarget | null\n  ) => target is DOMNode\n\n  /**\n   * Insert data from a `DataTransfer` into the editor.\n   */\n  insertData: (editor: ReactEditor, data: DataTransfer) => void\n\n  /**\n   * Insert fragment data from a `DataTransfer` into the editor.\n   */\n  insertFragmentData: (editor: ReactEditor, data: DataTransfer) => boolean\n\n  /**\n   * Insert text data from a `DataTransfer` into the editor.\n   */\n  insertTextData: (editor: ReactEditor, data: DataTransfer) => boolean\n\n  /**\n   * Check if the user is currently composing inside the editor.\n   */\n  isComposing: (editor: ReactEditor) => boolean\n\n  /**\n   * Check if the editor is focused.\n   */\n  isFocused: (editor: ReactEditor) => boolean\n\n  /**\n   * Check if the editor is in read-only mode.\n   */\n  isReadOnly: (editor: ReactEditor) => boolean\n\n  /**\n   * Check if the target is inside void and in an non-readonly editor.\n   */\n  isTargetInsideNonReadonlyVoid: (\n    editor: ReactEditor,\n    target: EventTarget | null\n  ) => boolean\n\n  /**\n   * Sets data from the currently selected fragment on a `DataTransfer`.\n   */\n  setFragmentData: (\n    editor: ReactEditor,\n    data: DataTransfer,\n    originEvent?: 'drag' | 'copy' | 'cut'\n  ) => void\n\n  /**\n   * Find the native DOM element from a Slate node.\n   */\n  toDOMNode: (editor: ReactEditor, node: Node) => HTMLElement\n\n  /**\n   * Find a native DOM selection point from a Slate point.\n   */\n  toDOMPoint: (editor: ReactEditor, point: Point) => DOMPoint\n\n  /**\n   * Find a native DOM range from a Slate `range`.\n   *\n   * Notice: the returned range will always be ordinal regardless of the direction of Slate `range` due to DOM API limit.\n   *\n   * there is no way to create a reverse DOM Range using Range.setStart/setEnd\n   * according to https://dom.spec.whatwg.org/#concept-range-bp-set.\n   */\n  toDOMRange: (editor: ReactEditor, range: Range) => DOMRange\n\n  /**\n   * Find a Slate node from a native DOM `element`.\n   */\n  toSlateNode: (editor: ReactEditor, domNode: DOMNode) => Node\n\n  /**\n   * Find a Slate point from a DOM selection's `domNode` and `domOffset`.\n   */\n  toSlatePoint: <T extends boolean>(\n    editor: ReactEditor,\n    domPoint: DOMPoint,\n    options: {\n      exactMatch: boolean\n      suppressThrow: T\n    }\n  ) => T extends true ? Point | null : Point\n\n  /**\n   * Find a Slate range from a DOM range or selection.\n   */\n  toSlateRange: <T extends boolean>(\n    editor: ReactEditor,\n    domRange: DOMRange | DOMStaticRange | DOMSelection,\n    options: {\n      exactMatch: boolean\n      suppressThrow: T\n    }\n  ) => T extends true ? Range | null : Range\n}\n\n// eslint-disable-next-line no-redeclare\nexport const ReactEditor: ReactEditorInterface = {\n  androidPendingDiffs: editor => EDITOR_TO_PENDING_DIFFS.get(editor),\n\n  androidScheduleFlush: editor => {\n    EDITOR_TO_SCHEDULE_FLUSH.get(editor)?.()\n  },\n\n  blur: editor => {\n    const el = ReactEditor.toDOMNode(editor, editor)\n    const root = ReactEditor.findDocumentOrShadowRoot(editor)\n    IS_FOCUSED.set(editor, false)\n\n    if (root.activeElement === el) {\n      el.blur()\n    }\n  },\n\n  deselect: editor => {\n    const { selection } = editor\n    const root = ReactEditor.findDocumentOrShadowRoot(editor)\n    const domSelection = root.getSelection()\n\n    if (domSelection && domSelection.rangeCount > 0) {\n      domSelection.removeAllRanges()\n    }\n\n    if (selection) {\n      Transforms.deselect(editor)\n    }\n  },\n\n  findDocumentOrShadowRoot: editor => {\n    const el = ReactEditor.toDOMNode(editor, editor)\n    const root = el.getRootNode()\n\n    if (\n      (root instanceof Document || root instanceof ShadowRoot) &&\n      root.getSelection != null\n    ) {\n      return root\n    }\n\n    return el.ownerDocument\n  },\n\n  findEventRange: (editor, event) => {\n    if ('nativeEvent' in event) {\n      event = event.nativeEvent\n    }\n\n    const { clientX: x, clientY: y, target } = event\n\n    if (x == null || y == null) {\n      throw new Error(`Cannot resolve a Slate range from a DOM event: ${event}`)\n    }\n\n    const node = ReactEditor.toSlateNode(editor, event.target)\n    const path = ReactEditor.findPath(editor, node)\n\n    // If the drop target is inside a void node, move it into either the\n    // next or previous node, depending on which side the `x` and `y`\n    // coordinates are closest to.\n    if (Element.isElement(node) && Editor.isVoid(editor, node)) {\n      const rect = target.getBoundingClientRect()\n      const isPrev = editor.isInline(node)\n        ? x - rect.left < rect.left + rect.width - x\n        : y - rect.top < rect.top + rect.height - y\n\n      const edge = Editor.point(editor, path, {\n        edge: isPrev ? 'start' : 'end',\n      })\n      const point = isPrev\n        ? Editor.before(editor, edge)\n        : Editor.after(editor, edge)\n\n      if (point) {\n        const range = Editor.range(editor, point)\n        return range\n      }\n    }\n\n    // Else resolve a range from the caret position where the drop occured.\n    let domRange\n    const { document } = ReactEditor.getWindow(editor)\n\n    // COMPAT: In Firefox, `caretRangeFromPoint` doesn't exist. (2016/07/25)\n    if (document.caretRangeFromPoint) {\n      domRange = document.caretRangeFromPoint(x, y)\n    } else {\n      const position = document.caretPositionFromPoint(x, y)\n\n      if (position) {\n        domRange = document.createRange()\n        domRange.setStart(position.offsetNode, position.offset)\n        domRange.setEnd(position.offsetNode, position.offset)\n      }\n    }\n\n    if (!domRange) {\n      throw new Error(`Cannot resolve a Slate range from a DOM event: ${event}`)\n    }\n\n    // Resolve a Slate range from the DOM range.\n    const range = ReactEditor.toSlateRange(editor, domRange, {\n      exactMatch: false,\n      suppressThrow: false,\n    })\n    return range\n  },\n\n  findKey: (editor, node) => {\n    let key = NODE_TO_KEY.get(node)\n\n    if (!key) {\n      key = new Key()\n      NODE_TO_KEY.set(node, key)\n    }\n\n    return key\n  },\n\n  findPath: (editor, node) => {\n    const path: Path = []\n    let child = node\n\n    while (true) {\n      const parent = NODE_TO_PARENT.get(child)\n\n      if (parent == null) {\n        if (Editor.isEditor(child)) {\n          return path\n        } else {\n          break\n        }\n      }\n\n      const i = NODE_TO_INDEX.get(child)\n\n      if (i == null) {\n        break\n      }\n\n      path.unshift(i)\n      child = parent\n    }\n\n    throw new Error(\n      `Unable to find the path for Slate node: ${Scrubber.stringify(node)}`\n    )\n  },\n\n  focus: editor => {\n    const el = ReactEditor.toDOMNode(editor, editor)\n    const root = ReactEditor.findDocumentOrShadowRoot(editor)\n    IS_FOCUSED.set(editor, true)\n\n    if (root.activeElement !== el) {\n      el.focus({ preventScroll: true })\n    }\n  },\n\n  getWindow: editor => {\n    const window = EDITOR_TO_WINDOW.get(editor)\n    if (!window) {\n      throw new Error('Unable to find a host window element for this editor')\n    }\n    return window\n  },\n\n  hasDOMNode: (editor, target, options = {}) => {\n    const { editable = false } = options\n    const editorEl = ReactEditor.toDOMNode(editor, editor)\n    let targetEl\n\n    // COMPAT: In Firefox, reading `target.nodeType` will throw an error if\n    // target is originating from an internal \"restricted\" element (e.g. a\n    // stepper arrow on a number input). (2018/05/04)\n    // https://github.com/ianstormtaylor/slate/issues/1819\n    try {\n      targetEl = (isDOMElement(target)\n        ? target\n        : target.parentElement) as HTMLElement\n    } catch (err) {\n      if (\n        !err.message.includes('Permission denied to access property \"nodeType\"')\n      ) {\n        throw err\n      }\n    }\n\n    if (!targetEl) {\n      return false\n    }\n\n    return (\n      targetEl.closest(`[data-slate-editor]`) === editorEl &&\n      (!editable || targetEl.isContentEditable\n        ? true\n        : (typeof targetEl.isContentEditable === 'boolean' && // isContentEditable exists only on HTMLElement, and on other nodes it will be undefined\n            // this is the core logic that lets you know you got the right editor.selection instead of null when editor is contenteditable=\"false\"(readOnly)\n            targetEl.closest('[contenteditable=\"false\"]') === editorEl) ||\n          !!targetEl.getAttribute('data-slate-zero-width'))\n    )\n  },\n\n  hasEditableTarget: (editor, target): target is DOMNode =>\n    isDOMNode(target) &&\n    ReactEditor.hasDOMNode(editor, target, { editable: true }),\n\n  hasRange: (editor, range) => {\n    const { anchor, focus } = range\n    return (\n      Editor.hasPath(editor, anchor.path) && Editor.hasPath(editor, focus.path)\n    )\n  },\n\n  hasSelectableTarget: (editor, target) =>\n    ReactEditor.hasEditableTarget(editor, target) ||\n    ReactEditor.isTargetInsideNonReadonlyVoid(editor, target),\n\n  hasTarget: (editor, target): target is DOMNode =>\n    isDOMNode(target) && ReactEditor.hasDOMNode(editor, target),\n\n  insertData: (editor, data) => {\n    editor.insertData(data)\n  },\n\n  insertFragmentData: (editor, data) => editor.insertFragmentData(data),\n\n  insertTextData: (editor, data) => editor.insertTextData(data),\n\n  isComposing: editor => {\n    return !!IS_COMPOSING.get(editor)\n  },\n\n  isFocused: editor => !!IS_FOCUSED.get(editor),\n\n  isReadOnly: editor => !!IS_READ_ONLY.get(editor),\n\n  isTargetInsideNonReadonlyVoid: (editor, target) => {\n    if (IS_READ_ONLY.get(editor)) return false\n\n    const slateNode =\n      ReactEditor.hasTarget(editor, target) &&\n      ReactEditor.toSlateNode(editor, target)\n    return Element.isElement(slateNode) && Editor.isVoid(editor, slateNode)\n  },\n\n  setFragmentData: (editor, data, originEvent) =>\n    editor.setFragmentData(data, originEvent),\n\n  toDOMNode: (editor, node) => {\n    const KEY_TO_ELEMENT = EDITOR_TO_KEY_TO_ELEMENT.get(editor)\n    const domNode = Editor.isEditor(node)\n      ? EDITOR_TO_ELEMENT.get(editor)\n      : KEY_TO_ELEMENT?.get(ReactEditor.findKey(editor, node))\n\n    if (!domNode) {\n      throw new Error(\n        `Cannot resolve a DOM node from Slate node: ${Scrubber.stringify(node)}`\n      )\n    }\n\n    return domNode\n  },\n\n  toDOMPoint: (editor, point) => {\n    const [node] = Editor.node(editor, point.path)\n    const el = ReactEditor.toDOMNode(editor, node)\n    let domPoint: DOMPoint | undefined\n\n    // If we're inside a void node, force the offset to 0, otherwise the zero\n    // width spacing character will result in an incorrect offset of 1\n    if (Editor.void(editor, { at: point })) {\n      point = { path: point.path, offset: 0 }\n    }\n\n    // For each leaf, we need to isolate its content, which means filtering\n    // to its direct text and zero-width spans. (We have to filter out any\n    // other siblings that may have been rendered alongside them.)\n    const selector = `[data-slate-string], [data-slate-zero-width]`\n    const texts = Array.from(el.querySelectorAll(selector))\n    let start = 0\n\n    for (let i = 0; i < texts.length; i++) {\n      const text = texts[i]\n      const domNode = text.childNodes[0] as HTMLElement\n\n      if (domNode == null || domNode.textContent == null) {\n        continue\n      }\n\n      const { length } = domNode.textContent\n      const attr = text.getAttribute('data-slate-length')\n      const trueLength = attr == null ? length : parseInt(attr, 10)\n      const end = start + trueLength\n\n      // Prefer putting the selection inside the mark placeholder to ensure\n      // composed text is displayed with the correct marks.\n      const nextText = texts[i + 1]\n      if (\n        point.offset === end &&\n        nextText?.hasAttribute('data-slate-mark-placeholder')\n      ) {\n        const domText = nextText.childNodes[0]\n\n        domPoint = [\n          // COMPAT: If we don't explicity set the dom point to be on the actual\n          // dom text element, chrome will put the selection behind the actual dom\n          // text element, causing domRange.getBoundingClientRect() calls on a collapsed\n          // selection to return incorrect zero values (https://bugs.chromium.org/p/chromium/issues/detail?id=435438)\n          // which will cause issues when scrolling to it.\n          domText instanceof DOMText ? domText : nextText,\n          nextText.textContent?.startsWith('\\uFEFF') ? 1 : 0,\n        ]\n        break\n      }\n\n      if (point.offset <= end) {\n        const offset = Math.min(length, Math.max(0, point.offset - start))\n        domPoint = [domNode, offset]\n        break\n      }\n\n      start = end\n    }\n\n    if (!domPoint) {\n      throw new Error(\n        `Cannot resolve a DOM point from Slate point: ${Scrubber.stringify(\n          point\n        )}`\n      )\n    }\n\n    return domPoint\n  },\n\n  toDOMRange: (editor, range) => {\n    const { anchor, focus } = range\n    const isBackward = Range.isBackward(range)\n    const domAnchor = ReactEditor.toDOMPoint(editor, anchor)\n    const domFocus = Range.isCollapsed(range)\n      ? domAnchor\n      : ReactEditor.toDOMPoint(editor, focus)\n\n    const window = ReactEditor.getWindow(editor)\n    const domRange = window.document.createRange()\n    const [startNode, startOffset] = isBackward ? domFocus : domAnchor\n    const [endNode, endOffset] = isBackward ? domAnchor : domFocus\n\n    // A slate Point at zero-width Leaf always has an offset of 0 but a native DOM selection at\n    // zero-width node has an offset of 1 so we have to check if we are in a zero-width node and\n    // adjust the offset accordingly.\n    const startEl = (isDOMElement(startNode)\n      ? startNode\n      : startNode.parentElement) as HTMLElement\n    const isStartAtZeroWidth = !!startEl.getAttribute('data-slate-zero-width')\n    const endEl = (isDOMElement(endNode)\n      ? endNode\n      : endNode.parentElement) as HTMLElement\n    const isEndAtZeroWidth = !!endEl.getAttribute('data-slate-zero-width')\n\n    domRange.setStart(startNode, isStartAtZeroWidth ? 1 : startOffset)\n    domRange.setEnd(endNode, isEndAtZeroWidth ? 1 : endOffset)\n    return domRange\n  },\n\n  toSlateNode: (editor, domNode) => {\n    let domEl = isDOMElement(domNode) ? domNode : domNode.parentElement\n\n    if (domEl && !domEl.hasAttribute('data-slate-node')) {\n      domEl = domEl.closest(`[data-slate-node]`)\n    }\n\n    const node = domEl ? ELEMENT_TO_NODE.get(domEl as HTMLElement) : null\n\n    if (!node) {\n      throw new Error(`Cannot resolve a Slate node from DOM node: ${domEl}`)\n    }\n\n    return node\n  },\n\n  toSlatePoint: <T extends boolean>(\n    editor: ReactEditor,\n    domPoint: DOMPoint,\n    options: {\n      exactMatch: boolean\n      suppressThrow: T\n    }\n  ): T extends true ? Point | null : Point => {\n    const { exactMatch, suppressThrow } = options\n    const [nearestNode, nearestOffset] = exactMatch\n      ? domPoint\n      : normalizeDOMPoint(domPoint)\n    const parentNode = nearestNode.parentNode as DOMElement\n    let textNode: DOMElement | null = null\n    let offset = 0\n\n    if (parentNode) {\n      const editorEl = ReactEditor.toDOMNode(editor, editor)\n      const potentialVoidNode = parentNode.closest('[data-slate-void=\"true\"]')\n      // Need to ensure that the closest void node is actually a void node\n      // within this editor, and not a void node within some parent editor. This can happen\n      // if this editor is within a void node of another editor (\"nested editors\", like in\n      // the \"Editable Voids\" example on the docs site).\n      const voidNode =\n        potentialVoidNode && editorEl.contains(potentialVoidNode)\n          ? potentialVoidNode\n          : null\n      let leafNode = parentNode.closest('[data-slate-leaf]')\n      let domNode: DOMElement | null = null\n\n      // Calculate how far into the text node the `nearestNode` is, so that we\n      // can determine what the offset relative to the text node is.\n      if (leafNode) {\n        textNode = leafNode.closest('[data-slate-node=\"text\"]')\n\n        if (textNode) {\n          const window = ReactEditor.getWindow(editor)\n          const range = window.document.createRange()\n          range.setStart(textNode, 0)\n          range.setEnd(nearestNode, nearestOffset)\n\n          const contents = range.cloneContents()\n          const removals = [\n            ...Array.prototype.slice.call(\n              contents.querySelectorAll('[data-slate-zero-width]')\n            ),\n            ...Array.prototype.slice.call(\n              contents.querySelectorAll('[contenteditable=false]')\n            ),\n          ]\n\n          removals.forEach(el => {\n            // COMPAT: While composing at the start of a text node, some keyboards put\n            // the text content inside the zero width space.\n            if (\n              IS_ANDROID &&\n              !exactMatch &&\n              el.hasAttribute('data-slate-zero-width') &&\n              el.textContent.length > 0 &&\n              el.textContext !== '\\uFEFF'\n            ) {\n              if (el.textContent.startsWith('\\uFEFF')) {\n                el.textContent = el.textContent.slice(1)\n              }\n\n              return\n            }\n\n            el!.parentNode!.removeChild(el)\n          })\n\n          // COMPAT: Edge has a bug where Range.prototype.toString() will\n          // convert \\n into \\r\\n. The bug causes a loop when slate-react\n          // attempts to reposition its cursor to match the native position. Use\n          // textContent.length instead.\n          // https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/10291116/\n          offset = contents.textContent!.length\n          domNode = textNode\n        }\n      } else if (voidNode) {\n        // For void nodes, the element with the offset key will be a cousin, not an\n        // ancestor, so find it by going down from the nearest void parent and taking the\n        // first one that isn't inside a nested editor.\n        const leafNodes = voidNode.querySelectorAll('[data-slate-leaf]')\n        for (let index = 0; index < leafNodes.length; index++) {\n          const current = leafNodes[index]\n          if (ReactEditor.hasDOMNode(editor, current)) {\n            leafNode = current\n            break\n          }\n        }\n\n        // COMPAT: In read-only editors the leaf is not rendered.\n        if (!leafNode) {\n          offset = 1\n        } else {\n          textNode = leafNode.closest('[data-slate-node=\"text\"]')!\n          domNode = leafNode\n          offset = domNode.textContent!.length\n          domNode.querySelectorAll('[data-slate-zero-width]').forEach(el => {\n            offset -= el.textContent!.length\n          })\n        }\n      }\n\n      if (\n        domNode &&\n        offset === domNode.textContent!.length &&\n        // COMPAT: Android IMEs might remove the zero width space while composing,\n        // and we don't add it for line-breaks.\n        IS_ANDROID &&\n        domNode.getAttribute('data-slate-zero-width') === 'z' &&\n        domNode.textContent?.startsWith('\\uFEFF') &&\n        // COMPAT: If the parent node is a Slate zero-width space, editor is\n        // because the text node should have no characters. However, during IME\n        // composition the ASCII characters will be prepended to the zero-width\n        // space, so subtract 1 from the offset to account for the zero-width\n        // space character.\n        (parentNode.hasAttribute('data-slate-zero-width') ||\n          // COMPAT: In Firefox, `range.cloneContents()` returns an extra trailing '\\n'\n          // when the document ends with a new-line character. This results in the offset\n          // length being off by one, so we need to subtract one to account for this.\n          (IS_FIREFOX && domNode.textContent?.endsWith('\\n\\n')))\n      ) {\n        offset--\n      }\n    }\n\n    if (IS_ANDROID && !textNode && !exactMatch) {\n      const node = parentNode.hasAttribute('data-slate-node')\n        ? parentNode\n        : parentNode.closest('[data-slate-node]')\n\n      if (node && ReactEditor.hasDOMNode(editor, node, { editable: true })) {\n        const slateNode = ReactEditor.toSlateNode(editor, node)\n        let { path, offset } = Editor.start(\n          editor,\n          ReactEditor.findPath(editor, slateNode)\n        )\n\n        if (!node.querySelector('[data-slate-leaf]')) {\n          offset = nearestOffset\n        }\n\n        return { path, offset } as T extends true ? Point | null : Point\n      }\n    }\n\n    if (!textNode) {\n      if (suppressThrow) {\n        return null as T extends true ? Point | null : Point\n      }\n      throw new Error(\n        `Cannot resolve a Slate point from DOM point: ${domPoint}`\n      )\n    }\n\n    // COMPAT: If someone is clicking from one Slate editor into another,\n    // the select event fires twice, once for the old editor's `element`\n    // first, and then afterwards for the correct `element`. (2017/03/03)\n    const slateNode = ReactEditor.toSlateNode(editor, textNode!)\n    const path = ReactEditor.findPath(editor, slateNode)\n    return { path, offset } as T extends true ? Point | null : Point\n  },\n\n  toSlateRange: <T extends boolean>(\n    editor: ReactEditor,\n    domRange: DOMRange | DOMStaticRange | DOMSelection,\n    options: {\n      exactMatch: boolean\n      suppressThrow: T\n    }\n  ): T extends true ? Range | null : Range => {\n    const { exactMatch, suppressThrow } = options\n    const el = isDOMSelection(domRange)\n      ? domRange.anchorNode\n      : domRange.startContainer\n    let anchorNode\n    let anchorOffset\n    let focusNode\n    let focusOffset\n    let isCollapsed\n\n    if (el) {\n      if (isDOMSelection(domRange)) {\n        // COMPAT: In firefox the normal seletion way does not work\n        // (https://github.com/ianstormtaylor/slate/pull/5486#issue-1820720223)\n        if (IS_FIREFOX && domRange.rangeCount > 1) {\n          focusNode = domRange.focusNode // Focus node works fine\n          const firstRange = domRange.getRangeAt(0)\n          const lastRange = domRange.getRangeAt(domRange.rangeCount - 1)\n\n          // Here we are in the contenteditable mode of a table in firefox\n          if (\n            focusNode instanceof HTMLTableRowElement &&\n            firstRange.startContainer instanceof HTMLTableRowElement &&\n            lastRange.startContainer instanceof HTMLTableRowElement\n          ) {\n            // HTMLElement, becouse Element is a slate element\n            function getLastChildren(element: HTMLElement): HTMLElement {\n              if (element.childElementCount > 0) {\n                return getLastChildren(<HTMLElement>element.children[0])\n              } else {\n                return element\n              }\n            }\n\n            const firstNodeRow = <HTMLTableRowElement>firstRange.startContainer\n            const lastNodeRow = <HTMLTableRowElement>lastRange.startContainer\n\n            // This should never fail as \"The HTMLElement interface represents any HTML element.\"\n            const firstNode = getLastChildren(\n              <HTMLElement>firstNodeRow.children[firstRange.startOffset]\n            )\n            const lastNode = getLastChildren(\n              <HTMLElement>lastNodeRow.children[lastRange.startOffset]\n            )\n\n            // Zero, as we allways take the right one as the anchor point\n            focusOffset = 0\n\n            if (lastNode.childNodes.length > 0) {\n              anchorNode = lastNode.childNodes[0]\n            } else {\n              anchorNode = lastNode\n            }\n\n            if (firstNode.childNodes.length > 0) {\n              focusNode = firstNode.childNodes[0]\n            } else {\n              focusNode = firstNode\n            }\n\n            if (lastNode instanceof HTMLElement) {\n              anchorOffset = (<HTMLElement>lastNode).innerHTML.length\n            } else {\n              // Fallback option\n              anchorOffset = 0\n            }\n          } else {\n            // This is the read only mode of a firefox table\n            // Right to left\n            if (firstRange.startContainer === focusNode) {\n              anchorNode = lastRange.endContainer\n              anchorOffset = lastRange.endOffset\n              focusOffset = firstRange.startOffset\n            } else {\n              // Left to right\n              anchorNode = firstRange.startContainer\n              anchorOffset = firstRange.endOffset\n              focusOffset = lastRange.startOffset\n            }\n          }\n        } else {\n          anchorNode = domRange.anchorNode\n          anchorOffset = domRange.anchorOffset\n          focusNode = domRange.focusNode\n          focusOffset = domRange.focusOffset\n        }\n\n        // COMPAT: There's a bug in chrome that always returns `true` for\n        // `isCollapsed` for a Selection that comes from a ShadowRoot.\n        // (2020/08/08)\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=447523\n        // IsCollapsed might not work in firefox, but this will\n        if ((IS_CHROME && hasShadowRoot(anchorNode)) || IS_FIREFOX) {\n          isCollapsed =\n            domRange.anchorNode === domRange.focusNode &&\n            domRange.anchorOffset === domRange.focusOffset\n        } else {\n          isCollapsed = domRange.isCollapsed\n        }\n      } else {\n        anchorNode = domRange.startContainer\n        anchorOffset = domRange.startOffset\n        focusNode = domRange.endContainer\n        focusOffset = domRange.endOffset\n        isCollapsed = domRange.collapsed\n      }\n    }\n\n    if (\n      anchorNode == null ||\n      focusNode == null ||\n      anchorOffset == null ||\n      focusOffset == null\n    ) {\n      throw new Error(\n        `Cannot resolve a Slate range from DOM range: ${domRange}`\n      )\n    }\n\n    // COMPAT: Triple-clicking a word in chrome will sometimes place the focus\n    // inside a `contenteditable=\"false\"` DOM node following the word, which\n    // will cause `toSlatePoint` to throw an error. (2023/03/07)\n    if (\n      'getAttribute' in focusNode &&\n      (focusNode as HTMLElement).getAttribute('contenteditable') === 'false' &&\n      (focusNode as HTMLElement).getAttribute('data-slate-void') !== 'true'\n    ) {\n      focusNode = anchorNode\n      focusOffset = anchorNode.textContent?.length || 0\n    }\n\n    const anchor = ReactEditor.toSlatePoint(\n      editor,\n      [anchorNode, anchorOffset],\n      {\n        exactMatch,\n        suppressThrow,\n      }\n    )\n    if (!anchor) {\n      return null as T extends true ? Range | null : Range\n    }\n\n    const focus = isCollapsed\n      ? anchor\n      : ReactEditor.toSlatePoint(editor, [focusNode, focusOffset], {\n          exactMatch,\n          suppressThrow,\n        })\n    if (!focus) {\n      return null as T extends true ? Range | null : Range\n    }\n\n    let range: Range = { anchor: anchor as Point, focus: focus as Point }\n    // if the selection is a hanging range that ends in a void\n    // and the DOM focus is an Element\n    // (meaning that the selection ends before the element)\n    // unhang the range to avoid mistakenly including the void\n    if (\n      Range.isExpanded(range) &&\n      Range.isForward(range) &&\n      isDOMElement(focusNode) &&\n      Editor.void(editor, { at: range.focus, mode: 'highest' })\n    ) {\n      range = Editor.unhangRange(editor, range, { voids: true })\n    }\n\n    return (range as unknown) as T extends true ? Range | null : Range\n  },\n}\n", "import {\n  Editor,\n  Node,\n  Operation,\n  Path,\n  Point,\n  Range,\n  Text,\n  Element,\n} from 'slate'\nimport { EDITOR_TO_PENDING_DIFFS } from './weak-maps'\n\nexport type StringDiff = {\n  start: number\n  end: number\n  text: string\n}\n\nexport type TextDiff = {\n  id: number\n  path: Path\n  diff: StringDiff\n}\n\n/**\n * Check whether a text diff was applied in a way we can perform the pending action on /\n * recover the pending selection.\n */\nexport function verifyDiffState(editor: Editor, textDiff: TextDiff): boolean {\n  const { path, diff } = textDiff\n  if (!Editor.hasPath(editor, path)) {\n    return false\n  }\n\n  const node = Node.get(editor, path)\n  if (!Text.isText(node)) {\n    return false\n  }\n\n  if (diff.start !== node.text.length || diff.text.length === 0) {\n    return (\n      node.text.slice(diff.start, diff.start + diff.text.length) === diff.text\n    )\n  }\n\n  const nextPath = Path.next(path)\n  if (!Editor.hasPath(editor, nextPath)) {\n    return false\n  }\n\n  const nextNode = Node.get(editor, nextPath)\n  return Text.isText(nextNode) && nextNode.text.startsWith(diff.text)\n}\n\nexport function applyStringDiff(text: string, ...diffs: StringDiff[]) {\n  return diffs.reduce(\n    (text, diff) =>\n      text.slice(0, diff.start) + diff.text + text.slice(diff.end),\n    text\n  )\n}\n\nfunction longestCommonPrefixLength(str: string, another: string) {\n  const length = Math.min(str.length, another.length)\n\n  for (let i = 0; i < length; i++) {\n    if (str.charAt(i) !== another.charAt(i)) {\n      return i\n    }\n  }\n\n  return length\n}\n\nfunction longestCommonSuffixLength(\n  str: string,\n  another: string,\n  max: number\n): number {\n  const length = Math.min(str.length, another.length, max)\n\n  for (let i = 0; i < length; i++) {\n    if (\n      str.charAt(str.length - i - 1) !== another.charAt(another.length - i - 1)\n    ) {\n      return i\n    }\n  }\n\n  return length\n}\n\n/**\n * Remove redundant changes from the diff so that it spans the minimal possible range\n */\nexport function normalizeStringDiff(targetText: string, diff: StringDiff) {\n  const { start, end, text } = diff\n  const removedText = targetText.slice(start, end)\n\n  const prefixLength = longestCommonPrefixLength(removedText, text)\n  const max = Math.min(\n    removedText.length - prefixLength,\n    text.length - prefixLength\n  )\n  const suffixLength = longestCommonSuffixLength(removedText, text, max)\n\n  const normalized: StringDiff = {\n    start: start + prefixLength,\n    end: end - suffixLength,\n    text: text.slice(prefixLength, text.length - suffixLength),\n  }\n\n  if (normalized.start === normalized.end && normalized.text.length === 0) {\n    return null\n  }\n\n  return normalized\n}\n\n/**\n * Return a string diff that is equivalent to applying b after a spanning the range of\n * both changes\n */\nexport function mergeStringDiffs(\n  targetText: string,\n  a: StringDiff,\n  b: StringDiff\n): StringDiff | null {\n  const start = Math.min(a.start, b.start)\n  const overlap = Math.max(\n    0,\n    Math.min(a.start + a.text.length, b.end) - b.start\n  )\n\n  const applied = applyStringDiff(targetText, a, b)\n  const sliceEnd = Math.max(\n    b.start + b.text.length,\n    a.start +\n      a.text.length +\n      (a.start + a.text.length > b.start ? b.text.length : 0) -\n      overlap\n  )\n\n  const text = applied.slice(start, sliceEnd)\n  const end = Math.max(a.end, b.end - a.text.length + (a.end - a.start))\n  return normalizeStringDiff(targetText, { start, end, text })\n}\n\n/**\n * Get the slate range the text diff spans.\n */\nexport function targetRange(textDiff: TextDiff): Range {\n  const { path, diff } = textDiff\n  return {\n    anchor: { path, offset: diff.start },\n    focus: { path, offset: diff.end },\n  }\n}\n\n/**\n * Normalize a 'pending point' a.k.a a point based on the dom state before applying\n * the pending diffs. Since the pending diffs might have been inserted with different\n * marks we have to 'walk' the offset from the starting position to ensure we still\n * have a valid point inside the document\n */\nexport function normalizePoint(editor: Editor, point: Point): Point | null {\n  let { path, offset } = point\n  if (!Editor.hasPath(editor, path)) {\n    return null\n  }\n\n  let leaf = Node.get(editor, path)\n  if (!Text.isText(leaf)) {\n    return null\n  }\n\n  const parentBlock = Editor.above(editor, {\n    match: n => Element.isElement(n) && Editor.isBlock(editor, n),\n    at: path,\n  })\n\n  if (!parentBlock) {\n    return null\n  }\n\n  while (offset > leaf.text.length) {\n    const entry = Editor.next(editor, { at: path, match: Text.isText })\n    if (!entry || !Path.isDescendant(entry[1], parentBlock[1])) {\n      return null\n    }\n\n    offset -= leaf.text.length\n    leaf = entry[0]\n    path = entry[1]\n  }\n\n  return { path, offset }\n}\n\n/**\n * Normalize a 'pending selection' to ensure it's valid in the current document state.\n */\nexport function normalizeRange(editor: Editor, range: Range): Range | null {\n  const anchor = normalizePoint(editor, range.anchor)\n  if (!anchor) {\n    return null\n  }\n\n  if (Range.isCollapsed(range)) {\n    return { anchor, focus: anchor }\n  }\n\n  const focus = normalizePoint(editor, range.focus)\n  if (!focus) {\n    return null\n  }\n\n  return { anchor, focus }\n}\n\nexport function transformPendingPoint(\n  editor: Editor,\n  point: Point,\n  op: Operation\n): Point | null {\n  const pendingDiffs = EDITOR_TO_PENDING_DIFFS.get(editor)\n  const textDiff = pendingDiffs?.find(({ path }) =>\n    Path.equals(path, point.path)\n  )\n\n  if (!textDiff || point.offset <= textDiff.diff.start) {\n    return Point.transform(point, op, { affinity: 'backward' })\n  }\n\n  const { diff } = textDiff\n  // Point references location inside the diff => transform the point based on the location\n  // the diff will be applied to and add the offset inside the diff.\n  if (point.offset <= diff.start + diff.text.length) {\n    const anchor = { path: point.path, offset: diff.start }\n    const transformed = Point.transform(anchor, op, {\n      affinity: 'backward',\n    })\n\n    if (!transformed) {\n      return null\n    }\n\n    return {\n      path: transformed.path,\n      offset: transformed.offset + point.offset - diff.start,\n    }\n  }\n\n  // Point references location after the diff\n  const anchor = {\n    path: point.path,\n    offset: point.offset - diff.text.length + diff.end - diff.start,\n  }\n  const transformed = Point.transform(anchor, op, {\n    affinity: 'backward',\n  })\n  if (!transformed) {\n    return null\n  }\n\n  if (\n    op.type === 'split_node' &&\n    Path.equals(op.path, point.path) &&\n    anchor.offset < op.position &&\n    diff.start < op.position\n  ) {\n    return transformed\n  }\n\n  return {\n    path: transformed.path,\n    offset: transformed.offset + diff.text.length - diff.end + diff.start,\n  }\n}\n\nexport function transformPendingRange(\n  editor: Editor,\n  range: Range,\n  op: Operation\n): Range | null {\n  const anchor = transformPendingPoint(editor, range.anchor, op)\n  if (!anchor) {\n    return null\n  }\n\n  if (Range.isCollapsed(range)) {\n    return { anchor, focus: anchor }\n  }\n\n  const focus = transformPendingPoint(editor, range.focus, op)\n  if (!focus) {\n    return null\n  }\n\n  return { anchor, focus }\n}\n\nexport function transformTextDiff(\n  textDiff: TextDiff,\n  op: Operation\n): TextDiff | null {\n  const { path, diff, id } = textDiff\n\n  switch (op.type) {\n    case 'insert_text': {\n      if (!Path.equals(op.path, path) || op.offset >= diff.end) {\n        return textDiff\n      }\n\n      if (op.offset <= diff.start) {\n        return {\n          diff: {\n            start: op.text.length + diff.start,\n            end: op.text.length + diff.end,\n            text: diff.text,\n          },\n          id,\n          path,\n        }\n      }\n\n      return {\n        diff: {\n          start: diff.start,\n          end: diff.end + op.text.length,\n          text: diff.text,\n        },\n        id,\n        path,\n      }\n    }\n    case 'remove_text': {\n      if (!Path.equals(op.path, path) || op.offset >= diff.end) {\n        return textDiff\n      }\n\n      if (op.offset + op.text.length <= diff.start) {\n        return {\n          diff: {\n            start: diff.start - op.text.length,\n            end: diff.end - op.text.length,\n            text: diff.text,\n          },\n          id,\n          path,\n        }\n      }\n\n      return {\n        diff: {\n          start: diff.start,\n          end: diff.end - op.text.length,\n          text: diff.text,\n        },\n        id,\n        path,\n      }\n    }\n    case 'split_node': {\n      if (!Path.equals(op.path, path) || op.position >= diff.end) {\n        return {\n          diff,\n          id,\n          path: Path.transform(path, op, { affinity: 'backward' })!,\n        }\n      }\n\n      if (op.position > diff.start) {\n        return {\n          diff: {\n            start: diff.start,\n            end: Math.min(op.position, diff.end),\n            text: diff.text,\n          },\n          id,\n          path,\n        }\n      }\n\n      return {\n        diff: {\n          start: diff.start - op.position,\n          end: diff.end - op.position,\n          text: diff.text,\n        },\n        id,\n        path: Path.transform(path, op, { affinity: 'forward' })!,\n      }\n    }\n    case 'merge_node': {\n      if (!Path.equals(op.path, path)) {\n        return {\n          diff,\n          id,\n          path: Path.transform(path, op)!,\n        }\n      }\n\n      return {\n        diff: {\n          start: diff.start + op.position,\n          end: diff.end + op.position,\n          text: diff.text,\n        },\n        id,\n        path: Path.transform(path, op)!,\n      }\n    }\n  }\n\n  const newPath = Path.transform(path, op)\n  if (!newPath) {\n    return null\n  }\n\n  return {\n    diff,\n    path: newPath,\n    id,\n  }\n}\n", "import { DebouncedFunc } from 'lodash'\nimport { Editor, Node, Path, Point, Range, Text, Transforms } from 'slate'\nimport { ReactEditor } from '../../plugin/react-editor'\nimport {\n  applyStringDiff,\n  mergeStringDiffs,\n  normalizePoint,\n  normalizeRange,\n  normalizeStringDiff,\n  StringDiff,\n  targetRange,\n  TextDiff,\n  verifyDiffState,\n} from '../../utils/diff-text'\nimport { isDOMSelection, isTrackedMutation } from '../../utils/dom'\nimport {\n  EDITOR_TO_FORCE_RENDER,\n  EDITOR_TO_PENDING_ACTION,\n  EDITOR_TO_PENDING_DIFFS,\n  EDITOR_TO_PENDING_INSERTION_MARKS,\n  EDITOR_TO_PENDING_SELECTION,\n  EDITOR_TO_PLACEHOLDER_ELEMENT,\n  EDITOR_TO_USER_MARKS,\n  IS_COMPOSING,\n} from '../../utils/weak-maps'\n\nexport type Action = { at?: Point | Range; run: () => void }\n\n// https://github.com/facebook/draft-js/blob/main/src/component/handlers/composition/DraftEditorCompositionHandler.js#L41\n// When using keyboard English association function, conpositionEnd triggered too fast, resulting in after `insertText` still maintain association state.\nconst RESOLVE_DELAY = 25\n\n// Time with no user interaction before the current user action is considered as done.\nconst FLUSH_DELAY = 200\n\n// Replace with `const debug = console.log` to debug\nconst debug = (..._: unknown[]) => {}\n\n// Type guard to check if a value is a DataTransfer\nconst isDataTransfer = (value: any): value is DataTransfer =>\n  value?.constructor.name === 'DataTransfer'\n\nexport type CreateAndroidInputManagerOptions = {\n  editor: ReactEditor\n\n  scheduleOnDOMSelectionChange: DebouncedFunc<() => void>\n  onDOMSelectionChange: DebouncedFunc<() => void>\n}\n\nexport type AndroidInputManager = {\n  flush: () => void\n  scheduleFlush: () => void\n\n  hasPendingDiffs: () => boolean\n  hasPendingAction: () => boolean\n  hasPendingChanges: () => boolean\n  isFlushing: () => boolean | 'action'\n\n  handleUserSelect: (range: Range | null) => void\n  handleCompositionEnd: (event: React.CompositionEvent<HTMLDivElement>) => void\n  handleCompositionStart: (\n    event: React.CompositionEvent<HTMLDivElement>\n  ) => void\n  handleDOMBeforeInput: (event: InputEvent) => void\n  handleKeyDown: (event: React.KeyboardEvent<HTMLDivElement>) => void\n\n  handleDomMutations: (mutations: MutationRecord[]) => void\n  handleInput: () => void\n}\n\nexport function createAndroidInputManager({\n  editor,\n  scheduleOnDOMSelectionChange,\n  onDOMSelectionChange,\n}: CreateAndroidInputManagerOptions): AndroidInputManager {\n  let flushing: 'action' | boolean = false\n  let compositionEndTimeoutId: ReturnType<typeof setTimeout> | null = null\n  let flushTimeoutId: ReturnType<typeof setTimeout> | null = null\n  let actionTimeoutId: ReturnType<typeof setTimeout> | null = null\n\n  let idCounter = 0\n  let insertPositionHint: StringDiff | null | false = false\n\n  const applyPendingSelection = () => {\n    const pendingSelection = EDITOR_TO_PENDING_SELECTION.get(editor)\n    EDITOR_TO_PENDING_SELECTION.delete(editor)\n\n    if (pendingSelection) {\n      const { selection } = editor\n      const normalized = normalizeRange(editor, pendingSelection)\n\n      debug('apply pending selection', pendingSelection, normalized)\n\n      if (normalized && (!selection || !Range.equals(normalized, selection))) {\n        Transforms.select(editor, normalized)\n      }\n    }\n  }\n\n  const performAction = () => {\n    const action = EDITOR_TO_PENDING_ACTION.get(editor)\n    EDITOR_TO_PENDING_ACTION.delete(editor)\n    if (!action) {\n      return\n    }\n\n    if (action.at) {\n      const target = Point.isPoint(action.at)\n        ? normalizePoint(editor, action.at)\n        : normalizeRange(editor, action.at)\n\n      if (!target) {\n        return\n      }\n\n      const targetRange = Editor.range(editor, target)\n      if (!editor.selection || !Range.equals(editor.selection, targetRange)) {\n        Transforms.select(editor, target)\n      }\n    }\n\n    action.run()\n  }\n\n  const flush = () => {\n    if (flushTimeoutId) {\n      clearTimeout(flushTimeoutId)\n      flushTimeoutId = null\n    }\n\n    if (actionTimeoutId) {\n      clearTimeout(actionTimeoutId)\n      actionTimeoutId = null\n    }\n\n    if (!hasPendingDiffs() && !hasPendingAction()) {\n      applyPendingSelection()\n      return\n    }\n\n    if (!flushing) {\n      flushing = true\n      setTimeout(() => (flushing = false))\n    }\n\n    if (hasPendingAction()) {\n      flushing = 'action'\n    }\n\n    const selectionRef =\n      editor.selection &&\n      Editor.rangeRef(editor, editor.selection, { affinity: 'forward' })\n    EDITOR_TO_USER_MARKS.set(editor, editor.marks)\n\n    debug(\n      'flush',\n      EDITOR_TO_PENDING_ACTION.get(editor),\n      EDITOR_TO_PENDING_DIFFS.get(editor)\n    )\n\n    let scheduleSelectionChange = hasPendingDiffs()\n\n    let diff: TextDiff | undefined\n    while ((diff = EDITOR_TO_PENDING_DIFFS.get(editor)?.[0])) {\n      const pendingMarks = EDITOR_TO_PENDING_INSERTION_MARKS.get(editor)\n\n      if (pendingMarks !== undefined) {\n        EDITOR_TO_PENDING_INSERTION_MARKS.delete(editor)\n        editor.marks = pendingMarks\n      }\n\n      if (pendingMarks && insertPositionHint === false) {\n        insertPositionHint = null\n        debug('insert after mark placeholder')\n      }\n\n      const range = targetRange(diff)\n      if (!editor.selection || !Range.equals(editor.selection, range)) {\n        Transforms.select(editor, range)\n      }\n\n      if (diff.diff.text) {\n        Editor.insertText(editor, diff.diff.text)\n      } else {\n        Editor.deleteFragment(editor)\n      }\n\n      // Remove diff only after we have applied it to account for it when transforming\n      // pending ranges.\n      EDITOR_TO_PENDING_DIFFS.set(\n        editor,\n        EDITOR_TO_PENDING_DIFFS.get(editor)?.filter(\n          ({ id }) => id !== diff!.id\n        )!\n      )\n\n      if (!verifyDiffState(editor, diff)) {\n        debug('invalid diff state')\n        scheduleSelectionChange = false\n        EDITOR_TO_PENDING_ACTION.delete(editor)\n        EDITOR_TO_USER_MARKS.delete(editor)\n        flushing = 'action'\n\n        // Ensure we don't restore the pending user (dom) selection\n        // since the document and dom state do not match.\n        EDITOR_TO_PENDING_SELECTION.delete(editor)\n        scheduleOnDOMSelectionChange.cancel()\n        onDOMSelectionChange.cancel()\n        selectionRef?.unref()\n      }\n    }\n\n    const selection = selectionRef?.unref()\n    if (\n      selection &&\n      !EDITOR_TO_PENDING_SELECTION.get(editor) &&\n      (!editor.selection || !Range.equals(selection, editor.selection))\n    ) {\n      Transforms.select(editor, selection)\n    }\n\n    if (hasPendingAction()) {\n      performAction()\n      return\n    }\n\n    // COMPAT: The selectionChange event is fired after the action is performed,\n    // so we have to manually schedule it to ensure we don't 'throw away' the selection\n    // while rendering if we have pending changes.\n    if (scheduleSelectionChange) {\n      debug('scheduleOnDOMSelectionChange pending changes')\n      scheduleOnDOMSelectionChange()\n    }\n\n    scheduleOnDOMSelectionChange.flush()\n    onDOMSelectionChange.flush()\n\n    applyPendingSelection()\n\n    const userMarks = EDITOR_TO_USER_MARKS.get(editor)\n    EDITOR_TO_USER_MARKS.delete(editor)\n    if (userMarks !== undefined) {\n      editor.marks = userMarks\n      editor.onChange()\n    }\n  }\n\n  const handleCompositionEnd = (\n    _event: React.CompositionEvent<HTMLDivElement>\n  ) => {\n    if (compositionEndTimeoutId) {\n      clearTimeout(compositionEndTimeoutId)\n    }\n\n    compositionEndTimeoutId = setTimeout(() => {\n      IS_COMPOSING.set(editor, false)\n      flush()\n    }, RESOLVE_DELAY)\n  }\n\n  const handleCompositionStart = (\n    _event: React.CompositionEvent<HTMLDivElement>\n  ) => {\n    debug('composition start')\n\n    IS_COMPOSING.set(editor, true)\n\n    if (compositionEndTimeoutId) {\n      clearTimeout(compositionEndTimeoutId)\n      compositionEndTimeoutId = null\n    }\n  }\n\n  const updatePlaceholderVisibility = (forceHide = false) => {\n    const placeholderElement = EDITOR_TO_PLACEHOLDER_ELEMENT.get(editor)\n    if (!placeholderElement) {\n      return\n    }\n\n    if (hasPendingDiffs() || forceHide) {\n      placeholderElement.style.display = 'none'\n      return\n    }\n\n    placeholderElement.style.removeProperty('display')\n  }\n\n  const storeDiff = (path: Path, diff: StringDiff) => {\n    debug('storeDiff', path, diff)\n\n    const pendingDiffs = EDITOR_TO_PENDING_DIFFS.get(editor) ?? []\n    EDITOR_TO_PENDING_DIFFS.set(editor, pendingDiffs)\n\n    const target = Node.leaf(editor, path)\n    const idx = pendingDiffs.findIndex(change => Path.equals(change.path, path))\n    if (idx < 0) {\n      const normalized = normalizeStringDiff(target.text, diff)\n      if (normalized) {\n        pendingDiffs.push({ path, diff, id: idCounter++ })\n      }\n\n      updatePlaceholderVisibility()\n      return\n    }\n\n    const merged = mergeStringDiffs(target.text, pendingDiffs[idx].diff, diff)\n    if (!merged) {\n      pendingDiffs.splice(idx, 1)\n      updatePlaceholderVisibility()\n      return\n    }\n\n    pendingDiffs[idx] = {\n      ...pendingDiffs[idx],\n      diff: merged,\n    }\n  }\n\n  const scheduleAction = (\n    run: () => void,\n    { at }: { at?: Point | Range } = {}\n  ): void => {\n    insertPositionHint = false\n    debug('scheduleAction', { at, run })\n\n    EDITOR_TO_PENDING_SELECTION.delete(editor)\n    scheduleOnDOMSelectionChange.cancel()\n    onDOMSelectionChange.cancel()\n\n    if (hasPendingAction()) {\n      flush()\n    }\n\n    EDITOR_TO_PENDING_ACTION.set(editor, { at, run })\n\n    // COMPAT: When deleting before a non-contenteditable element chrome only fires a beforeinput,\n    // (no input) and doesn't perform any dom mutations. Without a flush timeout we would never flush\n    // in this case and thus never actually perform the action.\n    actionTimeoutId = setTimeout(flush)\n  }\n\n  const handleDOMBeforeInput = (event: InputEvent): void => {\n    if (flushTimeoutId) {\n      clearTimeout(flushTimeoutId)\n      flushTimeoutId = null\n    }\n\n    const { inputType: type } = event\n    let targetRange: Range | null = null\n    const data: DataTransfer | string | undefined =\n      (event as any).dataTransfer || event.data || undefined\n\n    if (\n      insertPositionHint !== false &&\n      type !== 'insertText' &&\n      type !== 'insertCompositionText'\n    ) {\n      insertPositionHint = false\n    }\n\n    let [nativeTargetRange] = (event as any).getTargetRanges()\n    if (nativeTargetRange) {\n      targetRange = ReactEditor.toSlateRange(editor, nativeTargetRange, {\n        exactMatch: false,\n        suppressThrow: true,\n      })\n    }\n\n    // COMPAT: SelectionChange event is fired after the action is performed, so we\n    // have to manually get the selection here to ensure it's up-to-date.\n    const window = ReactEditor.getWindow(editor)\n    const domSelection = window.getSelection()\n    if (!targetRange && domSelection) {\n      nativeTargetRange = domSelection\n      targetRange = ReactEditor.toSlateRange(editor, domSelection, {\n        exactMatch: false,\n        suppressThrow: true,\n      })\n    }\n\n    targetRange = targetRange ?? editor.selection\n    if (!targetRange) {\n      return\n    }\n\n    // By default, the input manager tries to store text diffs so that we can\n    // defer flushing them at a later point in time. We don't want to flush\n    // for every input event as this can be expensive. However, there are some\n    // scenarios where we cannot safely store the text diff and must instead\n    // schedule an action to let Slate normalize the editor state.\n    let canStoreDiff = true\n\n    if (type.startsWith('delete')) {\n      if (Range.isExpanded(targetRange)) {\n        const [start, end] = Range.edges(targetRange)\n        const leaf = Node.leaf(editor, start.path)\n\n        if (leaf.text.length === start.offset && end.offset === 0) {\n          const next = Editor.next(editor, {\n            at: start.path,\n            match: Text.isText,\n          })\n          if (next && Path.equals(next[1], end.path)) {\n            targetRange = { anchor: end, focus: end }\n          }\n        }\n      }\n\n      const direction = type.endsWith('Backward') ? 'backward' : 'forward'\n      const [start, end] = Range.edges(targetRange)\n      const [leaf, path] = Editor.leaf(editor, start.path)\n\n      const diff = {\n        text: '',\n        start: start.offset,\n        end: end.offset,\n      }\n      const pendingDiffs = EDITOR_TO_PENDING_DIFFS.get(editor)\n      const relevantPendingDiffs = pendingDiffs?.find(change =>\n        Path.equals(change.path, path)\n      )\n      const diffs = relevantPendingDiffs\n        ? [relevantPendingDiffs.diff, diff]\n        : [diff]\n      const text = applyStringDiff(leaf.text, ...diffs)\n\n      if (text.length === 0) {\n        // Text leaf will be removed, so we need to schedule an\n        // action to remove it so that Slate can normalize instead\n        // of storing as a diff\n        canStoreDiff = false\n      }\n\n      if (Range.isExpanded(targetRange)) {\n        if (\n          canStoreDiff &&\n          Path.equals(targetRange.anchor.path, targetRange.focus.path)\n        ) {\n          const point = { path: targetRange.anchor.path, offset: start.offset }\n          const range = Editor.range(editor, point, point)\n          handleUserSelect(range)\n\n          return storeDiff(targetRange.anchor.path, {\n            text: '',\n            end: end.offset,\n            start: start.offset,\n          })\n        }\n\n        return scheduleAction(\n          () => Editor.deleteFragment(editor, { direction }),\n          { at: targetRange }\n        )\n      }\n    }\n\n    switch (type) {\n      case 'deleteByComposition':\n      case 'deleteByCut':\n      case 'deleteByDrag': {\n        return scheduleAction(() => Editor.deleteFragment(editor), {\n          at: targetRange,\n        })\n      }\n\n      case 'deleteContent':\n      case 'deleteContentForward': {\n        const { anchor } = targetRange\n        if (canStoreDiff && Range.isCollapsed(targetRange)) {\n          const targetNode = Node.leaf(editor, anchor.path)\n\n          if (anchor.offset < targetNode.text.length) {\n            return storeDiff(anchor.path, {\n              text: '',\n              start: anchor.offset,\n              end: anchor.offset + 1,\n            })\n          }\n        }\n\n        return scheduleAction(() => Editor.deleteForward(editor), {\n          at: targetRange,\n        })\n      }\n\n      case 'deleteContentBackward': {\n        const { anchor } = targetRange\n\n        // If we have a mismatch between the native and slate selection being collapsed\n        // we are most likely deleting a zero-width placeholder and thus should perform it\n        // as an action to ensure correct behavior (mostly happens with mark placeholders)\n        const nativeCollapsed = isDOMSelection(nativeTargetRange)\n          ? nativeTargetRange.isCollapsed\n          : !!nativeTargetRange?.collapsed\n\n        if (\n          canStoreDiff &&\n          nativeCollapsed &&\n          Range.isCollapsed(targetRange) &&\n          anchor.offset > 0\n        ) {\n          return storeDiff(anchor.path, {\n            text: '',\n            start: anchor.offset - 1,\n            end: anchor.offset,\n          })\n        }\n\n        return scheduleAction(() => Editor.deleteBackward(editor), {\n          at: targetRange,\n        })\n      }\n\n      case 'deleteEntireSoftLine': {\n        return scheduleAction(\n          () => {\n            Editor.deleteBackward(editor, { unit: 'line' })\n            Editor.deleteForward(editor, { unit: 'line' })\n          },\n          { at: targetRange }\n        )\n      }\n\n      case 'deleteHardLineBackward': {\n        return scheduleAction(\n          () => Editor.deleteBackward(editor, { unit: 'block' }),\n          { at: targetRange }\n        )\n      }\n\n      case 'deleteSoftLineBackward': {\n        return scheduleAction(\n          () => Editor.deleteBackward(editor, { unit: 'line' }),\n          { at: targetRange }\n        )\n      }\n\n      case 'deleteHardLineForward': {\n        return scheduleAction(\n          () => Editor.deleteForward(editor, { unit: 'block' }),\n          { at: targetRange }\n        )\n      }\n\n      case 'deleteSoftLineForward': {\n        return scheduleAction(\n          () => Editor.deleteForward(editor, { unit: 'line' }),\n          { at: targetRange }\n        )\n      }\n\n      case 'deleteWordBackward': {\n        return scheduleAction(\n          () => Editor.deleteBackward(editor, { unit: 'word' }),\n          { at: targetRange }\n        )\n      }\n\n      case 'deleteWordForward': {\n        return scheduleAction(\n          () => Editor.deleteForward(editor, { unit: 'word' }),\n          { at: targetRange }\n        )\n      }\n\n      case 'insertLineBreak': {\n        return scheduleAction(() => Editor.insertSoftBreak(editor), {\n          at: targetRange,\n        })\n      }\n\n      case 'insertParagraph': {\n        return scheduleAction(() => Editor.insertBreak(editor), {\n          at: targetRange,\n        })\n      }\n      case 'insertCompositionText':\n      case 'deleteCompositionText':\n      case 'insertFromComposition':\n      case 'insertFromDrop':\n      case 'insertFromPaste':\n      case 'insertFromYank':\n      case 'insertReplacementText':\n      case 'insertText': {\n        if (isDataTransfer(data)) {\n          return scheduleAction(() => ReactEditor.insertData(editor, data), {\n            at: targetRange,\n          })\n        }\n\n        let text = data ?? ''\n\n        // COMPAT: If we are writing inside a placeholder, the ime inserts the text inside\n        // the placeholder itself and thus includes the zero-width space inside edit events.\n        if (EDITOR_TO_PENDING_INSERTION_MARKS.get(editor)) {\n          text = text.replace('\\uFEFF', '')\n        }\n\n        // Pastes from the Android clipboard will generate `insertText` events.\n        // If the copied text contains any newlines, Android will append an\n        // extra newline to the end of the copied text.\n        if (type === 'insertText' && /.*\\n.*\\n$/.test(text)) {\n          text = text.slice(0, -1)\n        }\n\n        // If the text includes a newline, split it at newlines and paste each component\n        // string, with soft breaks in between each.\n        if (text.includes('\\n')) {\n          return scheduleAction(\n            () => {\n              const parts = text.split('\\n')\n              parts.forEach((line, i) => {\n                if (line) {\n                  Editor.insertText(editor, line)\n                }\n                if (i !== parts.length - 1) {\n                  Editor.insertSoftBreak(editor)\n                }\n              })\n            },\n            {\n              at: targetRange,\n            }\n          )\n        }\n\n        if (Path.equals(targetRange.anchor.path, targetRange.focus.path)) {\n          const [start, end] = Range.edges(targetRange)\n\n          const diff = {\n            start: start.offset,\n            end: end.offset,\n            text,\n          }\n\n          // COMPAT: Swiftkey has a weird bug where the target range of the 2nd word\n          // inserted after a mark placeholder is inserted with an anchor offset off by 1.\n          // So writing 'some text' will result in 'some ttext'. Luckily all 'normal' insert\n          // text events are fired with the correct target ranges, only the final 'insertComposition'\n          // isn't, so we can adjust the target range start offset if we are confident this is the\n          // swiftkey insert causing the issue.\n          if (text && insertPositionHint && type === 'insertCompositionText') {\n            const hintPosition =\n              insertPositionHint.start + insertPositionHint.text.search(/\\S|$/)\n            const diffPosition = diff.start + diff.text.search(/\\S|$/)\n\n            if (\n              diffPosition === hintPosition + 1 &&\n              diff.end ===\n                insertPositionHint.start + insertPositionHint.text.length\n            ) {\n              debug('adjusting swiftKey insert position using hint')\n              diff.start -= 1\n              insertPositionHint = null\n              scheduleFlush()\n            } else {\n              insertPositionHint = false\n            }\n          } else if (type === 'insertText') {\n            if (insertPositionHint === null) {\n              insertPositionHint = diff\n            } else if (\n              insertPositionHint &&\n              Range.isCollapsed(targetRange) &&\n              insertPositionHint.end + insertPositionHint.text.length ===\n                start.offset\n            ) {\n              insertPositionHint = {\n                ...insertPositionHint,\n                text: insertPositionHint.text + text,\n              }\n            } else {\n              insertPositionHint = false\n            }\n          } else {\n            insertPositionHint = false\n          }\n\n          if (canStoreDiff) {\n            storeDiff(start.path, diff)\n            return\n          }\n        }\n\n        return scheduleAction(() => Editor.insertText(editor, text), {\n          at: targetRange,\n        })\n      }\n    }\n  }\n\n  const hasPendingAction = () => {\n    return !!EDITOR_TO_PENDING_ACTION.get(editor)\n  }\n\n  const hasPendingDiffs = () => {\n    return !!EDITOR_TO_PENDING_DIFFS.get(editor)?.length\n  }\n\n  const hasPendingChanges = () => {\n    return hasPendingAction() || hasPendingDiffs()\n  }\n\n  const isFlushing = () => {\n    return flushing\n  }\n\n  const handleUserSelect = (range: Range | null) => {\n    EDITOR_TO_PENDING_SELECTION.set(editor, range)\n\n    if (flushTimeoutId) {\n      clearTimeout(flushTimeoutId)\n      flushTimeoutId = null\n    }\n\n    const { selection } = editor\n    if (!range) {\n      return\n    }\n\n    const pathChanged =\n      !selection || !Path.equals(selection.anchor.path, range.anchor.path)\n    const parentPathChanged =\n      !selection ||\n      !Path.equals(\n        selection.anchor.path.slice(0, -1),\n        range.anchor.path.slice(0, -1)\n      )\n\n    if ((pathChanged && insertPositionHint) || parentPathChanged) {\n      insertPositionHint = false\n    }\n\n    if (pathChanged || hasPendingDiffs()) {\n      flushTimeoutId = setTimeout(flush, FLUSH_DELAY)\n    }\n  }\n\n  const handleInput = () => {\n    if (hasPendingAction() || !hasPendingDiffs()) {\n      debug('flush input')\n      flush()\n    }\n  }\n\n  const handleKeyDown = (_: React.KeyboardEvent) => {\n    // COMPAT: Swiftkey closes the keyboard when typing inside a empty node\n    // directly next to a non-contenteditable element (= the placeholder).\n    // The only event fired soon enough for us to allow hiding the placeholder\n    // without swiftkey picking it up is the keydown event, so we have to hide it\n    // here. See https://github.com/ianstormtaylor/slate/pull/4988#issuecomment-1201050535\n    if (!hasPendingDiffs()) {\n      updatePlaceholderVisibility(true)\n      setTimeout(updatePlaceholderVisibility)\n    }\n  }\n\n  const scheduleFlush = () => {\n    if (!hasPendingAction()) {\n      actionTimeoutId = setTimeout(flush)\n    }\n  }\n\n  const handleDomMutations = (mutations: MutationRecord[]) => {\n    if (hasPendingDiffs() || hasPendingAction()) {\n      return\n    }\n\n    if (\n      mutations.some(mutation => isTrackedMutation(editor, mutation, mutations))\n    ) {\n      // Cause a re-render to restore the dom state if we encounter tracked mutations without\n      // a corresponding pending action.\n      EDITOR_TO_FORCE_RENDER.get(editor)?.()\n    }\n  }\n\n  return {\n    flush,\n    scheduleFlush,\n\n    hasPendingDiffs,\n    hasPendingAction,\n    hasPendingChanges,\n\n    isFlushing,\n\n    handleUserSelect,\n    handleCompositionEnd,\n    handleCompositionStart,\n    handleDOMBeforeInput,\n    handleKeyDown,\n\n    handleDomMutations,\n    handleInput,\n  }\n}\n", "import { useEffect, useRef } from 'react'\n\nexport function useIsMounted() {\n  const isMountedRef = useRef(false)\n\n  useEffect(() => {\n    isMountedRef.current = true\n    return () => {\n      isMountedRef.current = false\n    }\n  }, [])\n\n  return isMountedRef.current\n}\n", "import { useLayoutEffect, useEffect } from 'react'\nimport { CAN_USE_DOM } from '../utils/environment'\n\n/**\n * Prevent warning on SSR by falling back to useEffect when DOM isn't available\n */\n\nexport const useIsomorphicLayoutEffect = CAN_USE_DOM\n  ? useLayoutEffect\n  : useEffect\n", "import { RefObject, useEffect, useState } from 'react'\nimport { useIsomorphicLayoutEffect } from './use-isomorphic-layout-effect'\n\nexport function useMutationObserver(\n  node: RefObject<HTMLElement>,\n  callback: MutationCallback,\n  options: MutationObserverInit\n) {\n  const [mutationObserver] = useState(() => new MutationObserver(callback))\n\n  useIsomorphicLayoutEffect(() => {\n    // Discard mutations caused during render phase. This works due to react calling\n    // useLayoutEffect synchronously after the render phase before the next tick.\n    mutationObserver.takeRecords()\n  })\n\n  useEffect(() => {\n    if (!node.current) {\n      throw new Error('Failed to attach MutationObserver, `node` is undefined')\n    }\n\n    mutationObserver.observe(node.current, options)\n    return () => mutationObserver.disconnect()\n  }, [mutationObserver, node, options])\n}\n", "import { RefObject, useState } from 'react'\nimport { useSlateStatic } from '../use-slate-static'\nimport { IS_ANDROID } from '../../utils/environment'\nimport { EDITOR_TO_SCHEDULE_FLUSH } from '../../utils/weak-maps'\nimport {\n  createAndroidInputManager,\n  CreateAndroidInputManagerOptions,\n} from './android-input-manager'\nimport { useIsMounted } from '../use-is-mounted'\nimport { useMutationObserver } from '../use-mutation-observer'\n\ntype UseAndroidInputManagerOptions = {\n  node: RefObject<HTMLElement>\n} & Omit<\n  CreateAndroidInputManagerOptions,\n  'editor' | 'onUserInput' | 'receivedUserInput'\n>\n\nconst MUTATION_OBSERVER_CONFIG: MutationObserverInit = {\n  subtree: true,\n  childList: true,\n  characterData: true,\n}\n\nexport const useAndroidInputManager = !IS_ANDROID\n  ? () => null\n  : ({ node, ...options }: UseAndroidInputManagerOptions) => {\n      if (!IS_ANDROID) {\n        return null\n      }\n\n      const editor = useSlateStatic()\n      const isMounted = useIsMounted()\n\n      const [inputManager] = useState(() =>\n        createAndroidInputManager({\n          editor,\n          ...options,\n        })\n      )\n\n      useMutationObserver(\n        node,\n        inputManager.handleDomMutations,\n        MUTATION_OBSERVER_CONFIG\n      )\n\n      EDITOR_TO_SCHEDULE_FLUSH.set(editor, inputManager.scheduleFlush)\n      if (isMounted) {\n        inputManager.flush()\n      }\n\n      return inputManager\n    }\n", "import { Range } from 'slate'\nimport { PLACEHOLDER_SYMBOL } from './weak-maps'\n\nexport const shallowCompare = (obj1: {}, obj2: {}) =>\n  Object.keys(obj1).length === Object.keys(obj2).length &&\n  Object.keys(obj1).every(\n    key => obj2.hasOwnProperty(key) && obj1[key] === obj2[key]\n  )\n\nconst isDecorationFlagsEqual = (range: Range, other: Range) => {\n  const { anchor: rangeAnchor, focus: rangeFocus, ...rangeOwnProps } = range\n  const { anchor: otherAnchor, focus: otherFocus, ...otherOwnProps } = other\n\n  return (\n    range[PLACEHOLDER_SYMBOL] === other[PLACEHOLDER_SYMBOL] &&\n    shallowCompare(rangeOwnProps, otherOwnProps)\n  )\n}\n\n/**\n * Check if a list of decorator ranges are equal to another.\n *\n * PERF: this requires the two lists to also have the ranges inside them in the\n * same order, but this is an okay constraint for us since decorations are\n * kept in order, and the odd case where they aren't is okay to re-render for.\n */\n\nexport const isElementDecorationsEqual = (\n  list: Range[],\n  another: Range[]\n): boolean => {\n  if (list.length !== another.length) {\n    return false\n  }\n\n  for (let i = 0; i < list.length; i++) {\n    const range = list[i]\n    const other = another[i]\n\n    if (!Range.equals(range, other) || !isDecorationFlagsEqual(range, other)) {\n      return false\n    }\n  }\n\n  return true\n}\n\n/**\n * Check if a list of decorator ranges are equal to another.\n *\n * PERF: this requires the two lists to also have the ranges inside them in the\n * same order, but this is an okay constraint for us since decorations are\n * kept in order, and the odd case where they aren't is okay to re-render for.\n */\n\nexport const isTextDecorationsEqual = (\n  list: Range[],\n  another: Range[]\n): boolean => {\n  if (list.length !== another.length) {\n    return false\n  }\n\n  for (let i = 0; i < list.length; i++) {\n    const range = list[i]\n    const other = another[i]\n\n    // compare only offsets because paths doesn't matter for text\n    if (\n      range.anchor.offset !== other.anchor.offset ||\n      range.focus.offset !== other.focus.offset ||\n      !isDecorationFlagsEqual(range, other)\n    ) {\n      return false\n    }\n  }\n\n  return true\n}\n", "import React, { forwardRef, memo, useRef, useState } from 'react'\nimport { Editor, Text, Path, Element, Node } from 'slate'\n\nimport { ReactEditor, useSlateStatic } from '..'\nimport { useIsomorphicLayoutEffect } from '../hooks/use-isomorphic-layout-effect'\nimport { IS_ANDROID } from '../utils/environment'\nimport { MARK_PLACEHOLDER_SYMBOL } from '../utils/weak-maps'\n\n/**\n * Leaf content strings.\n */\n\nconst String = (props: {\n  isLast: boolean\n  leaf: Text\n  parent: Element\n  text: Text\n}) => {\n  const { isLast, leaf, parent, text } = props\n  const editor = useSlateStatic()\n  const path = ReactEditor.findPath(editor, text)\n  const parentPath = Path.parent(path)\n  const isMarkPlaceholder = leaf[MARK_PLACEHOLDER_SYMBOL] === true\n\n  // COMPAT: Render text inside void nodes with a zero-width space.\n  // So the node can contain selection but the text is not visible.\n  if (editor.isVoid(parent)) {\n    return <ZeroWidthString length={Node.string(parent).length} />\n  }\n\n  // COMPAT: If this is the last text node in an empty block, render a zero-\n  // width space that will convert into a line break when copying and pasting\n  // to support expected plain text.\n  if (\n    leaf.text === '' &&\n    parent.children[parent.children.length - 1] === text &&\n    !editor.isInline(parent) &&\n    Editor.string(editor, parentPath) === ''\n  ) {\n    return <ZeroWidthString isLineBreak isMarkPlaceholder={isMarkPlaceholder} />\n  }\n\n  // COMPAT: If the text is empty, it's because it's on the edge of an inline\n  // node, so we render a zero-width space so that the selection can be\n  // inserted next to it still.\n  if (leaf.text === '') {\n    return <ZeroWidthString isMarkPlaceholder={isMarkPlaceholder} />\n  }\n\n  // COMPAT: Browsers will collapse trailing new lines at the end of blocks,\n  // so we need to add an extra trailing new lines to prevent that.\n  if (isLast && leaf.text.slice(-1) === '\\n') {\n    return <TextString isTrailing text={leaf.text} />\n  }\n\n  return <TextString text={leaf.text} />\n}\n\n/**\n * Leaf strings with text in them.\n */\nconst TextString = (props: { text: string; isTrailing?: boolean }) => {\n  const { text, isTrailing = false } = props\n  const ref = useRef<HTMLSpanElement>(null)\n  const getTextContent = () => {\n    return `${text ?? ''}${isTrailing ? '\\n' : ''}`\n  }\n  const [initialText] = useState(getTextContent)\n\n  // This is the actual text rendering boundary where we interface with the DOM\n  // The text is not rendered as part of the virtual DOM, as since we handle basic character insertions natively,\n  // updating the DOM is not a one way dataflow anymore. What we need here is not reconciliation and diffing\n  // with previous version of the virtual DOM, but rather diffing with the actual DOM element, and replace the DOM <span> content\n  // exactly if and only if its current content does not match our current virtual DOM.\n  // Otherwise the DOM TextNode would always be replaced by React as the user types, which interferes with native text features,\n  // eg makes native spellcheck opt out from checking the text node.\n\n  // useLayoutEffect: updating our span before browser paint\n  useIsomorphicLayoutEffect(() => {\n    // null coalescing text to make sure we're not outputing \"null\" as a string in the extreme case it is nullish at runtime\n    const textWithTrailing = getTextContent()\n\n    if (ref.current && ref.current.textContent !== textWithTrailing) {\n      ref.current.textContent = textWithTrailing\n    }\n\n    // intentionally not specifying dependencies, so that this effect runs on every render\n    // as this effectively replaces \"specifying the text in the virtual DOM under the <span> below\" on each render\n  })\n\n  // We intentionally render a memoized <span> that only receives the initial text content when the component is mounted.\n  // We defer to the layout effect above to update the `textContent` of the span element when needed.\n  return <MemoizedText ref={ref}>{initialText}</MemoizedText>\n}\n\nconst MemoizedText = memo(\n  forwardRef<HTMLSpanElement, { children: string }>((props, ref) => {\n    return (\n      <span data-slate-string ref={ref}>\n        {props.children}\n      </span>\n    )\n  })\n)\n\n/**\n * Leaf strings without text, render as zero-width strings.\n */\n\nexport const ZeroWidthString = (props: {\n  length?: number\n  isLineBreak?: boolean\n  isMarkPlaceholder?: boolean\n}) => {\n  const { length = 0, isLineBreak = false, isMarkPlaceholder = false } = props\n\n  const attributes = {\n    'data-slate-zero-width': isLineBreak ? 'n' : 'z',\n    'data-slate-length': length,\n  }\n\n  if (isMarkPlaceholder) {\n    attributes['data-slate-mark-placeholder'] = true\n  }\n\n  return (\n    <span {...attributes}>\n      {!IS_ANDROID || !isLineBreak ? '\\uFEFF' : null}\n      {isLineBreak ? <br /> : null}\n    </span>\n  )\n}\n\nexport default String\n", "import React, {\n  useRef,\n  useCallback,\n  MutableRefObject,\n  useState,\n  useEffect,\n} from 'react'\nimport { Element, Text } from 'slate'\nimport { ResizeObserver as ResizeObserverPolyfill } from '@juggle/resize-observer'\nimport String from './string'\nimport {\n  PLACEHOLDER_SYMBOL,\n  EDITOR_TO_PLACEHOLDER_ELEMENT,\n  EDITOR_TO_FORCE_RENDER,\n} from '../utils/weak-maps'\nimport { RenderLeafProps, RenderPlaceholderProps } from './editable'\nimport { useSlateStatic } from '../hooks/use-slate-static'\nimport { IS_WEBKIT } from '../utils/environment'\n\nfunction disconnectPlaceholderResizeObserver(\n  placeholderResizeObserver: MutableRefObject<ResizeObserver | null>,\n  releaseObserver: boolean\n) {\n  if (placeholderResizeObserver.current) {\n    placeholderResizeObserver.current.disconnect()\n    if (releaseObserver) {\n      placeholderResizeObserver.current = null\n    }\n  }\n}\n\ntype TimerId = ReturnType<typeof setTimeout> | null\n\nfunction clearTimeoutRef(timeoutRef: MutableRefObject<TimerId>) {\n  if (timeoutRef.current) {\n    clearTimeout(timeoutRef.current)\n    timeoutRef.current = null\n  }\n}\n\n/**\n * Individual leaves in a text node with unique formatting.\n */\nconst Leaf = (props: {\n  isLast: boolean\n  leaf: Text\n  parent: Element\n  renderPlaceholder: (props: RenderPlaceholderProps) => JSX.Element\n  renderLeaf?: (props: RenderLeafProps) => JSX.Element\n  text: Text\n}) => {\n  const {\n    leaf,\n    isLast,\n    text,\n    parent,\n    renderPlaceholder,\n    renderLeaf = (props: RenderLeafProps) => <DefaultLeaf {...props} />,\n  } = props\n\n  const editor = useSlateStatic()\n  const placeholderResizeObserver = useRef<ResizeObserver | null>(null)\n  const placeholderRef = useRef<HTMLElement | null>(null)\n  const [showPlaceholder, setShowPlaceholder] = useState(false)\n  const showPlaceholderTimeoutRef = useRef<TimerId>(null)\n\n  const callbackPlaceholderRef = useCallback(\n    (placeholderEl: HTMLElement | null) => {\n      disconnectPlaceholderResizeObserver(\n        placeholderResizeObserver,\n        placeholderEl == null\n      )\n\n      if (placeholderEl == null) {\n        EDITOR_TO_PLACEHOLDER_ELEMENT.delete(editor)\n        leaf.onPlaceholderResize?.(null)\n      } else {\n        EDITOR_TO_PLACEHOLDER_ELEMENT.set(editor, placeholderEl)\n\n        if (!placeholderResizeObserver.current) {\n          // Create a new observer and observe the placeholder element.\n          const ResizeObserver = window.ResizeObserver || ResizeObserverPolyfill\n          placeholderResizeObserver.current = new ResizeObserver(() => {\n            leaf.onPlaceholderResize?.(placeholderEl)\n          })\n        }\n        placeholderResizeObserver.current.observe(placeholderEl)\n        placeholderRef.current = placeholderEl\n      }\n    },\n    [placeholderRef, leaf, editor]\n  )\n\n  let children = (\n    <String isLast={isLast} leaf={leaf} parent={parent} text={text} />\n  )\n\n  const leafIsPlaceholder = leaf[PLACEHOLDER_SYMBOL]\n  useEffect(() => {\n    if (leafIsPlaceholder) {\n      if (!showPlaceholderTimeoutRef.current) {\n        // Delay the placeholder so it will not render in a selection\n        showPlaceholderTimeoutRef.current = setTimeout(() => {\n          setShowPlaceholder(true)\n          showPlaceholderTimeoutRef.current = null\n        }, 300)\n      }\n    } else {\n      clearTimeoutRef(showPlaceholderTimeoutRef)\n      setShowPlaceholder(false)\n    }\n    return () => clearTimeoutRef(showPlaceholderTimeoutRef)\n  }, [leafIsPlaceholder, setShowPlaceholder])\n\n  if (leafIsPlaceholder && showPlaceholder) {\n    const placeholderProps: RenderPlaceholderProps = {\n      children: leaf.placeholder,\n      attributes: {\n        'data-slate-placeholder': true,\n        style: {\n          position: 'absolute',\n          top: 0,\n          pointerEvents: 'none',\n          width: '100%',\n          maxWidth: '100%',\n          display: 'block',\n          opacity: '0.333',\n          userSelect: 'none',\n          textDecoration: 'none',\n          // Fixes https://github.com/udecode/plate/issues/2315\n          WebkitUserModify: IS_WEBKIT ? 'inherit' : undefined,\n        },\n        contentEditable: false,\n        ref: callbackPlaceholderRef,\n      },\n    }\n\n    children = (\n      <React.Fragment>\n        {renderPlaceholder(placeholderProps)}\n        {children}\n      </React.Fragment>\n    )\n  }\n\n  // COMPAT: Having the `data-` attributes on these leaf elements ensures that\n  // in certain misbehaving browsers they aren't weirdly cloned/destroyed by\n  // contenteditable behaviors. (2019/05/08)\n  const attributes: {\n    'data-slate-leaf': true\n  } = {\n    'data-slate-leaf': true,\n  }\n\n  return renderLeaf({ attributes, children, leaf, text })\n}\n\nconst MemoizedLeaf = React.memo(Leaf, (prev, next) => {\n  return (\n    next.parent === prev.parent &&\n    next.isLast === prev.isLast &&\n    next.renderLeaf === prev.renderLeaf &&\n    next.renderPlaceholder === prev.renderPlaceholder &&\n    next.text === prev.text &&\n    Text.equals(next.leaf, prev.leaf) &&\n    next.leaf[PLACEHOLDER_SYMBOL] === prev.leaf[PLACEHOLDER_SYMBOL]\n  )\n})\n\nexport const DefaultLeaf = (props: RenderLeafProps) => {\n  const { attributes, children } = props\n  return <span {...attributes}>{children}</span>\n}\n\nexport default MemoizedLeaf\n", "import React, { useCallback, useRef } from 'react'\nimport { Element, Range, Text as SlateText } from 'slate'\nimport { ReactEditor, useSlateStatic } from '..'\nimport { isTextDecorationsEqual } from '../utils/range-list'\nimport {\n  EDITOR_TO_KEY_TO_ELEMENT,\n  ELEMENT_TO_NODE,\n  NODE_TO_ELEMENT,\n} from '../utils/weak-maps'\nimport { RenderLeafProps, RenderPlaceholderProps } from './editable'\nimport Leaf from './leaf'\n\n/**\n * Text.\n */\n\nconst Text = (props: {\n  decorations: Range[]\n  isLast: boolean\n  parent: Element\n  renderPlaceholder: (props: RenderPlaceholderProps) => JSX.Element\n  renderLeaf?: (props: RenderLeafProps) => JSX.Element\n  text: SlateText\n}) => {\n  const {\n    decorations,\n    isLast,\n    parent,\n    renderPlaceholder,\n    renderLeaf,\n    text,\n  } = props\n  const editor = useSlateStatic()\n  const ref = useRef<HTMLSpanElement | null>(null)\n  const leaves = SlateText.decorations(text, decorations)\n  const key = ReactEditor.findKey(editor, text)\n  const children = []\n\n  for (let i = 0; i < leaves.length; i++) {\n    const leaf = leaves[i]\n\n    children.push(\n      <Leaf\n        isLast={isLast && i === leaves.length - 1}\n        key={`${key.id}-${i}`}\n        renderPlaceholder={renderPlaceholder}\n        leaf={leaf}\n        text={text}\n        parent={parent}\n        renderLeaf={renderLeaf}\n      />\n    )\n  }\n\n  // Update element-related weak maps with the DOM element ref.\n  const callbackRef = useCallback(\n    (span: HTMLSpanElement | null) => {\n      const KEY_TO_ELEMENT = EDITOR_TO_KEY_TO_ELEMENT.get(editor)\n      if (span) {\n        KEY_TO_ELEMENT?.set(key, span)\n        NODE_TO_ELEMENT.set(text, span)\n        ELEMENT_TO_NODE.set(span, text)\n      } else {\n        KEY_TO_ELEMENT?.delete(key)\n        NODE_TO_ELEMENT.delete(text)\n        if (ref.current) {\n          ELEMENT_TO_NODE.delete(ref.current)\n        }\n      }\n      ref.current = span\n    },\n    [ref, editor, key, text]\n  )\n  return (\n    <span data-slate-node=\"text\" ref={callbackRef}>\n      {children}\n    </span>\n  )\n}\n\nconst MemoizedText = React.memo(Text, (prev, next) => {\n  return (\n    next.parent === prev.parent &&\n    next.isLast === prev.isLast &&\n    next.renderLeaf === prev.renderLeaf &&\n    next.renderPlaceholder === prev.renderPlaceholder &&\n    next.text === prev.text &&\n    isTextDecorationsEqual(next.decorations, prev.decorations)\n  )\n})\n\nexport default MemoizedText\n", "import getDirection from 'direction'\nimport React, { useCallback } from 'react'\nimport { Editor, Element as SlateElement, Node, Range } from 'slate'\nimport { ReactEditor, useReadOnly, useSlateStatic } from '..'\nimport useChildren from '../hooks/use-children'\nimport { isElementDecorationsEqual } from '../utils/range-list'\nimport {\n  EDITOR_TO_KEY_TO_ELEMENT,\n  ELEMENT_TO_NODE,\n  NODE_TO_ELEMENT,\n  NODE_TO_INDEX,\n  NODE_TO_PARENT,\n} from '../utils/weak-maps'\nimport {\n  RenderElementProps,\n  RenderLeafProps,\n  RenderPlaceholderProps,\n} from './editable'\n\nimport Text from './text'\n\n/**\n * Element.\n */\n\nconst Element = (props: {\n  decorations: Range[]\n  element: SlateElement\n  renderElement?: (props: RenderElementProps) => JSX.Element\n  renderPlaceholder: (props: RenderPlaceholderProps) => JSX.Element\n  renderLeaf?: (props: RenderLeafProps) => JSX.Element\n  selection: Range | null\n}) => {\n  const {\n    decorations,\n    element,\n    renderElement = (p: RenderElementProps) => <DefaultElement {...p} />,\n    renderPlaceholder,\n    renderLeaf,\n    selection,\n  } = props\n  const editor = useSlateStatic()\n  const readOnly = useReadOnly()\n  const isInline = editor.isInline(element)\n  const key = ReactEditor.findKey(editor, element)\n  const ref = useCallback(\n    (ref: HTMLElement | null) => {\n      // Update element-related weak maps with the DOM element ref.\n      const KEY_TO_ELEMENT = EDITOR_TO_KEY_TO_ELEMENT.get(editor)\n      if (ref) {\n        KEY_TO_ELEMENT?.set(key, ref)\n        NODE_TO_ELEMENT.set(element, ref)\n        ELEMENT_TO_NODE.set(ref, element)\n      } else {\n        KEY_TO_ELEMENT?.delete(key)\n        NODE_TO_ELEMENT.delete(element)\n      }\n    },\n    [editor, key, element]\n  )\n  let children: React.ReactNode = useChildren({\n    decorations,\n    node: element,\n    renderElement,\n    renderPlaceholder,\n    renderLeaf,\n    selection,\n  })\n\n  // Attributes that the developer must mix into the element in their\n  // custom node renderer component.\n  const attributes: {\n    'data-slate-node': 'element'\n    'data-slate-void'?: true\n    'data-slate-inline'?: true\n    contentEditable?: false\n    dir?: 'rtl'\n    ref: any\n  } = {\n    'data-slate-node': 'element',\n    ref,\n  }\n\n  if (isInline) {\n    attributes['data-slate-inline'] = true\n  }\n\n  // If it's a block node with inline children, add the proper `dir` attribute\n  // for text direction.\n  if (!isInline && Editor.hasInlines(editor, element)) {\n    const text = Node.string(element)\n    const dir = getDirection(text)\n\n    if (dir === 'rtl') {\n      attributes.dir = dir\n    }\n  }\n\n  // If it's a void node, wrap the children in extra void-specific elements.\n  if (Editor.isVoid(editor, element)) {\n    attributes['data-slate-void'] = true\n\n    if (!readOnly && isInline) {\n      attributes.contentEditable = false\n    }\n\n    const Tag = isInline ? 'span' : 'div'\n    const [[text]] = Node.texts(element)\n\n    children = (\n      <Tag\n        data-slate-spacer\n        style={{\n          height: '0',\n          color: 'transparent',\n          outline: 'none',\n          position: 'absolute',\n        }}\n      >\n        <Text\n          renderPlaceholder={renderPlaceholder}\n          decorations={[]}\n          isLast={false}\n          parent={element}\n          text={text}\n        />\n      </Tag>\n    )\n\n    NODE_TO_INDEX.set(text, 0)\n    NODE_TO_PARENT.set(text, element)\n  }\n\n  return renderElement({ attributes, children, element })\n}\n\nconst MemoizedElement = React.memo(Element, (prev, next) => {\n  return (\n    prev.element === next.element &&\n    prev.renderElement === next.renderElement &&\n    prev.renderLeaf === next.renderLeaf &&\n    prev.renderPlaceholder === next.renderPlaceholder &&\n    isElementDecorationsEqual(prev.decorations, next.decorations) &&\n    (prev.selection === next.selection ||\n      (!!prev.selection &&\n        !!next.selection &&\n        Range.equals(prev.selection, next.selection)))\n  )\n})\n\n/**\n * The default element renderer.\n */\n\nexport const DefaultElement = (props: RenderElementProps) => {\n  const { attributes, children, element } = props\n  const editor = useSlateStatic()\n  const Tag = editor.isInline(element) ? 'span' : 'div'\n  return (\n    <Tag {...attributes} style={{ position: 'relative' }}>\n      {children}\n    </Tag>\n  )\n}\n\nexport default MemoizedElement\n", "import { createContext, useContext } from 'react'\nimport { Range, NodeEntry } from 'slate'\n\n/**\n * A React context for sharing the `decorate` prop of the editable.\n */\n\nexport const DecorateContext = createContext<(entry: NodeEntry) => Range[]>(\n  () => []\n)\n\n/**\n * Get the current `decorate` prop of the editable.\n */\n\nexport const useDecorate = (): ((entry: NodeEntry) => Range[]) => {\n  return useContext(DecorateContext)\n}\n", "import { createContext, useContext } from 'react'\n\n/**\n * A React context for sharing the `selected` state of an element.\n */\n\nexport const SelectedContext = createContext(false)\n\n/**\n * Get the current `selected` state of an element.\n */\n\nexport const useSelected = (): boolean => {\n  return useContext(SelectedContext)\n}\n", "import React from 'react'\nimport { <PERSON><PERSON><PERSON>, Descendant, Editor, Element, Range } from 'slate'\nimport {\n  RenderElementProps,\n  RenderLeafProps,\n  RenderPlaceholderProps,\n} from '../components/editable'\n\nimport ElementComponent from '../components/element'\nimport TextComponent from '../components/text'\nimport { ReactEditor } from '../plugin/react-editor'\nimport { NODE_TO_INDEX, NODE_TO_PARENT } from '../utils/weak-maps'\nimport { useDecorate } from './use-decorate'\nimport { SelectedContext } from './use-selected'\nimport { useSlateStatic } from './use-slate-static'\n\n/**\n * Children.\n */\n\nconst useChildren = (props: {\n  decorations: Range[]\n  node: Ancestor\n  renderElement?: (props: RenderElementProps) => JSX.Element\n  renderPlaceholder: (props: RenderPlaceholderProps) => JSX.Element\n  renderLeaf?: (props: RenderLeafProps) => JSX.Element\n  selection: Range | null\n}) => {\n  const {\n    decorations,\n    node,\n    renderElement,\n    renderPlaceholder,\n    renderLeaf,\n    selection,\n  } = props\n  const decorate = useDecorate()\n  const editor = useSlateStatic()\n  const path = ReactEditor.findPath(editor, node)\n  const children = []\n  const isLeafBlock =\n    Element.isElement(node) &&\n    !editor.isInline(node) &&\n    Editor.hasInlines(editor, node)\n\n  for (let i = 0; i < node.children.length; i++) {\n    const p = path.concat(i)\n    const n = node.children[i] as Descendant\n    const key = ReactEditor.findKey(editor, n)\n    const range = Editor.range(editor, p)\n    const sel = selection && Range.intersection(range, selection)\n    const ds = decorate([n, p])\n\n    for (const dec of decorations) {\n      const d = Range.intersection(dec, range)\n\n      if (d) {\n        ds.push(d)\n      }\n    }\n\n    if (Element.isElement(n)) {\n      children.push(\n        <SelectedContext.Provider key={`provider-${key.id}`} value={!!sel}>\n          <ElementComponent\n            decorations={ds}\n            element={n}\n            key={key.id}\n            renderElement={renderElement}\n            renderPlaceholder={renderPlaceholder}\n            renderLeaf={renderLeaf}\n            selection={sel}\n          />\n        </SelectedContext.Provider>\n      )\n    } else {\n      children.push(\n        <TextComponent\n          decorations={ds}\n          key={key.id}\n          isLast={isLeafBlock && i === node.children.length - 1}\n          parent={node}\n          renderPlaceholder={renderPlaceholder}\n          renderLeaf={renderLeaf}\n          text={n}\n        />\n      )\n    }\n\n    NODE_TO_INDEX.set(n, i)\n    NODE_TO_PARENT.set(n, node)\n  }\n\n  return children\n}\n\nexport default useChildren\n", "import { createContext, useContext } from 'react'\n\n/**\n * A React context for sharing the `readOnly` state of the editor.\n */\n\nexport const ReadOnlyContext = createContext(false)\n\n/**\n * Get the current `readOnly` state of the editor.\n */\n\nexport const useReadOnly = (): boolean => {\n  return useContext(ReadOnlyContext)\n}\n", "import { createContext, useContext } from 'react'\nimport { Editor } from 'slate'\nimport { ReactEditor } from '../plugin/react-editor'\n\n/**\n * A React context for sharing the editor object, in a way that re-renders the\n * context whenever changes occur.\n */\n\nexport interface SlateContextValue {\n  v: number\n  editor: ReactEditor\n}\n\nexport const SlateContext = createContext<{\n  v: number\n  editor: ReactEditor\n} | null>(null)\n\n/**\n * Get the current editor object from the React context.\n */\n\nexport const useSlate = (): Editor => {\n  const context = useContext(SlateContext)\n\n  if (!context) {\n    throw new Error(\n      `The \\`useSlate\\` hook must be used inside the <Slate> component's context.`\n    )\n  }\n\n  const { editor } = context\n  return editor\n}\n\nexport const useSlateWithV = () => {\n  const context = useContext(SlateContext)\n\n  if (!context) {\n    throw new Error(\n      `The \\`useSlate\\` hook must be used inside the <Slate> component's context.`\n    )\n  }\n\n  return context\n}\n", "import { useCallback, useEffect, useRef } from 'react'\nimport { ReactEditor } from '../plugin/react-editor'\nimport { useSlateStatic } from './use-slate-static'\n\nexport function useTrackUserInput() {\n  const editor = useSlateStatic()\n\n  const receivedUserInput = useRef<boolean>(false)\n  const animationFrameIdRef = useRef<number>(0)\n\n  const onUserInput = useCallback(() => {\n    if (receivedUserInput.current) {\n      return\n    }\n\n    receivedUserInput.current = true\n\n    const window = ReactEditor.getWindow(editor)\n    window.cancelAnimationFrame(animationFrameIdRef.current)\n\n    animationFrameIdRef.current = window.requestAnimationFrame(() => {\n      receivedUserInput.current = false\n    })\n  }, [editor])\n\n  useEffect(() => () => cancelAnimationFrame(animationFrameIdRef.current), [])\n\n  return {\n    receivedUserInput,\n    onUserInput,\n  }\n}\n", "export const TRIPLE_CLICK = 3\n", "import { isHotkey } from 'is-hotkey'\nimport { IS_APPLE } from './environment'\n\n/**\n * Hotkey mappings for each platform.\n */\n\nconst HOTKEYS = {\n  bold: 'mod+b',\n  compose: ['down', 'left', 'right', 'up', 'backspace', 'enter'],\n  moveBackward: 'left',\n  moveForward: 'right',\n  moveWordBackward: 'ctrl+left',\n  moveWordForward: 'ctrl+right',\n  deleteBackward: 'shift?+backspace',\n  deleteForward: 'shift?+delete',\n  extendBackward: 'shift+left',\n  extendForward: 'shift+right',\n  italic: 'mod+i',\n  insertSoftBreak: 'shift+enter',\n  splitBlock: 'enter',\n  undo: 'mod+z',\n}\n\nconst APPLE_HOTKEYS = {\n  moveLineBackward: 'opt+up',\n  moveLineForward: 'opt+down',\n  moveWordBackward: 'opt+left',\n  moveWordForward: 'opt+right',\n  deleteBackward: ['ctrl+backspace', 'ctrl+h'],\n  deleteForward: ['ctrl+delete', 'ctrl+d'],\n  deleteLineBackward: 'cmd+shift?+backspace',\n  deleteLineForward: ['cmd+shift?+delete', 'ctrl+k'],\n  deleteWordBackward: 'opt+shift?+backspace',\n  deleteWordForward: 'opt+shift?+delete',\n  extendLineBackward: 'opt+shift+up',\n  extendLineForward: 'opt+shift+down',\n  redo: 'cmd+shift+z',\n  transposeCharacter: 'ctrl+t',\n}\n\nconst WINDOWS_HOTKEYS = {\n  deleteWordBackward: 'ctrl+shift?+backspace',\n  deleteWordForward: 'ctrl+shift?+delete',\n  redo: ['ctrl+y', 'ctrl+shift+z'],\n}\n\n/**\n * Create a platform-aware hotkey checker.\n */\n\nconst create = (key: string) => {\n  const generic = HOTKEYS[key]\n  const apple = APPLE_HOTKEYS[key]\n  const windows = WINDOWS_HOTKEYS[key]\n  const isGeneric = generic && isHotkey(generic)\n  const isApple = apple && isHotkey(apple)\n  const isWindows = windows && isHotkey(windows)\n\n  return (event: KeyboardEvent) => {\n    if (isGeneric && isGeneric(event)) return true\n    if (IS_APPLE && isApple && isApple(event)) return true\n    if (!IS_APPLE && isWindows && isWindows(event)) return true\n    return false\n  }\n}\n\n/**\n * Hotkeys.\n */\n\nexport default {\n  isBold: create('bold'),\n  isCompose: create('compose'),\n  isMoveBackward: create('moveBackward'),\n  isMoveForward: create('moveForward'),\n  isDeleteBackward: create('deleteBackward'),\n  isDeleteForward: create('deleteForward'),\n  isDeleteLineBackward: create('deleteLineBackward'),\n  isDeleteLineForward: create('deleteLineForward'),\n  isDeleteWordBackward: create('deleteWordBackward'),\n  isDeleteWordForward: create('deleteWordForward'),\n  isExtendBackward: create('extendBackward'),\n  isExtendForward: create('extendForward'),\n  isExtendLineBackward: create('extendLineBackward'),\n  isExtendLineForward: create('extendLineForward'),\n  isItalic: create('italic'),\n  isMoveLineBackward: create('moveLineBackward'),\n  isMoveLineForward: create('moveLineForward'),\n  isMoveWordBackward: create('moveWordBackward'),\n  isMoveWordForward: create('moveWordForward'),\n  isRedo: create('redo'),\n  isSoftBreak: create('insertSoftBreak'),\n  isSplitBlock: create('splitBlock'),\n  isTransposeCharacter: create('transposeCharacter'),\n  isUndo: create('undo'),\n}\n", "function _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nmodule.exports = _createClass;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _setPrototypeOf(o, p) {\n  module.exports = _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  module.exports[\"default\"] = module.exports, module.exports.__esModule = true;\n  return _setPrototypeOf(o, p);\n}\n\nmodule.exports = _setPrototypeOf;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "var setPrototypeOf = require(\"./setPrototypeOf.js\");\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) setPrototypeOf(subClass, superClass);\n}\n\nmodule.exports = _inherits;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    module.exports = _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n\n    module.exports[\"default\"] = module.exports, module.exports.__esModule = true;\n  } else {\n    module.exports = _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n\n    module.exports[\"default\"] = module.exports, module.exports.__esModule = true;\n  }\n\n  return _typeof(obj);\n}\n\nmodule.exports = _typeof;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nmodule.exports = _assertThisInitialized;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "var _typeof = require(\"@babel/runtime/helpers/typeof\")[\"default\"];\n\nvar assertThisInitialized = require(\"./assertThisInitialized.js\");\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return assertThisInitialized(self);\n}\n\nmodule.exports = _possibleConstructorReturn;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "function _getPrototypeOf(o) {\n  module.exports = _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  module.exports[\"default\"] = module.exports, module.exports.__esModule = true;\n  return _getPrototypeOf(o);\n}\n\nmodule.exports = _getPrototypeOf;\nmodule.exports[\"default\"] = module.exports, module.exports.__esModule = true;", "import { RefObject } from 'react'\nimport { ReactEditor } from '../../plugin/react-editor'\nimport { isTrackedMutation } from '../../utils/dom'\n\nexport type RestoreDOMManager = {\n  registerMutations: (mutations: MutationRecord[]) => void\n  restoreDOM: () => void\n  clear: () => void\n}\n\nexport const createRestoreDomManager = (\n  editor: ReactEditor,\n  receivedUserInput: RefObject<boolean>\n): RestoreDOMManager => {\n  let bufferedMutations: MutationRecord[] = []\n\n  const clear = () => {\n    bufferedMutations = []\n  }\n\n  const registerMutations = (mutations: MutationRecord[]) => {\n    if (!receivedUserInput.current) {\n      return\n    }\n\n    const trackedMutations = mutations.filter(mutation =>\n      isTrackedMutation(editor, mutation, mutations)\n    )\n\n    bufferedMutations.push(...trackedMutations)\n  }\n\n  function restoreDOM() {\n    if (bufferedMutations.length > 0) {\n      bufferedMutations.reverse().forEach(mutation => {\n        if (mutation.type === 'characterData') {\n          // We don't want to restore the DOM for characterData mutations\n          // because this interrupts the composition.\n          return\n        }\n\n        mutation.removedNodes.forEach(node => {\n          mutation.target.insertBefore(node, mutation.nextSibling)\n        })\n\n        mutation.addedNodes.forEach(node => {\n          mutation.target.removeChild(node)\n        })\n      })\n\n      // Clear buffered mutations to ensure we don't undo them twice\n      clear()\n    }\n  }\n\n  return {\n    registerMutations,\n    restoreDOM,\n    clear,\n  }\n}\n", "import React, { Component, ComponentType, ContextType, RefObject } from 'react'\nimport { EditorContext } from '../../hooks/use-slate-static'\nimport { IS_ANDROID } from '../../utils/environment'\nimport {\n  createRestoreDom<PERSON>anager,\n  RestoreDOMManager,\n} from './restore-dom-manager'\n\nconst MUTATION_OBSERVER_CONFIG: MutationObserverInit = {\n  subtree: true,\n  childList: true,\n  characterData: true,\n  characterDataOldValue: true,\n}\n\ntype RestoreDOMProps = {\n  receivedUserInput: RefObject<boolean>\n  node: RefObject<HTMLDivElement>\n}\n\n// We have to use a class component here since we rely on `getSnapshotBeforeUpdate` which has no FC equivalent\n// to run code synchronously immediately before react commits the component update to the DOM.\nclass RestoreDOMComponent extends Component<RestoreDOMProps> {\n  static contextType = EditorContext\n  context: ContextType<typeof EditorContext> = null\n\n  private manager: RestoreDOMManager | null = null\n  private mutationObserver: MutationObserver | null = null\n\n  observe() {\n    const { node } = this.props\n    if (!node.current) {\n      throw new Error('Failed to attach MutationObserver, `node` is undefined')\n    }\n\n    this.mutationObserver?.observe(node.current, MUTATION_OBSERVER_CONFIG)\n  }\n\n  componentDidMount() {\n    const { receivedUserInput } = this.props\n    const editor = this.context!\n\n    this.manager = createRestoreDomManager(editor, receivedUserInput)\n    this.mutationObserver = new MutationObserver(this.manager.registerMutations)\n\n    this.observe()\n  }\n\n  getSnapshotBeforeUpdate() {\n    const pendingMutations = this.mutationObserver?.takeRecords()\n    if (pendingMutations?.length) {\n      this.manager?.registerMutations(pendingMutations)\n    }\n\n    this.mutationObserver?.disconnect()\n    this.manager?.restoreDOM()\n\n    return null\n  }\n\n  componentDidUpdate() {\n    this.manager?.clear()\n    this.observe()\n  }\n\n  componentWillUnmount() {\n    this.mutationObserver?.disconnect()\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nexport const RestoreDOM: ComponentType<RestoreDOMProps> = IS_ANDROID\n  ? RestoreDOMComponent\n  : ({ children }) => <>{children}</>\n", "import getDirection from 'direction'\nimport debounce from 'lodash/debounce'\nimport throttle from 'lodash/throttle'\nimport React, {\n  useCallback,\n  useEffect,\n  useMemo,\n  useReducer,\n  useRef,\n  useState,\n} from 'react'\nimport scrollIntoView from 'scroll-into-view-if-needed'\nimport {\n  Editor,\n  Element,\n  Node,\n  NodeEntry,\n  Path,\n  Range,\n  Text,\n  Transforms,\n} from 'slate'\nimport { useAndroidInputManager } from '../hooks/android-input-manager/use-android-input-manager'\nimport useChildren from '../hooks/use-children'\nimport { DecorateContext } from '../hooks/use-decorate'\nimport { useIsomorphicLayoutEffect } from '../hooks/use-isomorphic-layout-effect'\nimport { ReadOnlyContext } from '../hooks/use-read-only'\nimport { useSlate } from '../hooks/use-slate'\nimport { useTrackUserInput } from '../hooks/use-track-user-input'\nimport { ReactEditor } from '../plugin/react-editor'\nimport { TRIPLE_CLICK } from '../utils/constants'\nimport {\n  DOMElement,\n  DOMRange,\n  DOMText,\n  getDefaultView,\n  isDOMElement,\n  isDOMNode,\n  isPlainTextOnlyPaste,\n} from '../utils/dom'\nimport {\n  CAN_USE_DOM,\n  HAS_BEFORE_INPUT_SUPPORT,\n  IS_ANDROID,\n  IS_CHROME,\n  IS_FIREFOX,\n  IS_FIREFOX_LEGACY,\n  IS_IOS,\n  IS_WEBKIT,\n  IS_UC_MOBILE,\n  IS_WECHATBROWSER,\n} from '../utils/environment'\nimport Hotkeys from '../utils/hotkeys'\nimport {\n  EDITOR_TO_ELEMENT,\n  EDITOR_TO_FORCE_RENDER,\n  EDITOR_TO_PENDING_INSERTION_MARKS,\n  EDITOR_TO_USER_MARKS,\n  EDITOR_TO_USER_SELECTION,\n  EDITOR_TO_WINDOW,\n  ELEMENT_TO_NODE,\n  IS_COMPOSING,\n  IS_FOCUSED,\n  IS_READ_ONLY,\n  MARK_PLACEHOLDER_SYMBOL,\n  NODE_TO_ELEMENT,\n  PLACEHOLDER_SYMBOL,\n} from '../utils/weak-maps'\nimport { RestoreDOM } from './restore-dom/restore-dom'\nimport { AndroidInputManager } from '../hooks/android-input-manager/android-input-manager'\n\ntype DeferredOperation = () => void\n\nconst Children = (props: Parameters<typeof useChildren>[0]) => (\n  <React.Fragment>{useChildren(props)}</React.Fragment>\n)\n\n/**\n * `RenderElementProps` are passed to the `renderElement` handler.\n */\n\nexport interface RenderElementProps {\n  children: any\n  element: Element\n  attributes: {\n    'data-slate-node': 'element'\n    'data-slate-inline'?: true\n    'data-slate-void'?: true\n    dir?: 'rtl'\n    ref: any\n  }\n}\n\n/**\n * `RenderLeafProps` are passed to the `renderLeaf` handler.\n */\n\nexport interface RenderLeafProps {\n  children: any\n  leaf: Text\n  text: Text\n  attributes: {\n    'data-slate-leaf': true\n  }\n}\n\n/**\n * `EditableProps` are passed to the `<Editable>` component.\n */\n\nexport type EditableProps = {\n  decorate?: (entry: NodeEntry) => Range[]\n  onDOMBeforeInput?: (event: InputEvent) => void\n  placeholder?: string\n  readOnly?: boolean\n  role?: string\n  style?: React.CSSProperties\n  renderElement?: (props: RenderElementProps) => JSX.Element\n  renderLeaf?: (props: RenderLeafProps) => JSX.Element\n  renderPlaceholder?: (props: RenderPlaceholderProps) => JSX.Element\n  scrollSelectionIntoView?: (editor: ReactEditor, domRange: DOMRange) => void\n  as?: React.ElementType\n  disableDefaultStyles?: boolean\n} & React.TextareaHTMLAttributes<HTMLDivElement>\n\n/**\n * Editable.\n */\n\nexport const Editable = (props: EditableProps) => {\n  const defaultRenderPlaceholder = useCallback(\n    (props: RenderPlaceholderProps) => <DefaultPlaceholder {...props} />,\n    []\n  )\n  const {\n    autoFocus,\n    decorate = defaultDecorate,\n    onDOMBeforeInput: propsOnDOMBeforeInput,\n    placeholder,\n    readOnly = false,\n    renderElement,\n    renderLeaf,\n    renderPlaceholder = defaultRenderPlaceholder,\n    scrollSelectionIntoView = defaultScrollSelectionIntoView,\n    style: userStyle = {},\n    as: Component = 'div',\n    disableDefaultStyles = false,\n    ...attributes\n  } = props\n  const editor = useSlate()\n  // Rerender editor when composition status changed\n  const [isComposing, setIsComposing] = useState(false)\n  const ref = useRef<HTMLDivElement | null>(null)\n  const deferredOperations = useRef<DeferredOperation[]>([])\n  const [placeholderHeight, setPlaceholderHeight] = useState<\n    number | undefined\n  >()\n\n  const { onUserInput, receivedUserInput } = useTrackUserInput()\n\n  const [, forceRender] = useReducer(s => s + 1, 0)\n  EDITOR_TO_FORCE_RENDER.set(editor, forceRender)\n\n  // Update internal state on each render.\n  IS_READ_ONLY.set(editor, readOnly)\n\n  // Keep track of some state for the event handler logic.\n  const state = useMemo(\n    () => ({\n      isDraggingInternally: false,\n      isUpdatingSelection: false,\n      latestElement: null as DOMElement | null,\n      hasMarkPlaceholder: false,\n    }),\n    []\n  )\n\n  // The autoFocus TextareaHTMLAttribute doesn't do anything on a div, so it\n  // needs to be manually focused.\n  useEffect(() => {\n    if (ref.current && autoFocus) {\n      ref.current.focus()\n    }\n  }, [autoFocus])\n\n  /**\n   * The AndroidInputManager object has a cyclical dependency on onDOMSelectionChange\n   *\n   * It is defined as a reference to simplify hook dependencies and clarify that\n   * it needs to be initialized.\n   */\n  const androidInputManagerRef = useRef<\n    AndroidInputManager | null | undefined\n  >()\n\n  // Listen on the native `selectionchange` event to be able to update any time\n  // the selection changes. This is required because React's `onSelect` is leaky\n  // and non-standard so it doesn't fire until after a selection has been\n  // released. This causes issues in situations where another change happens\n  // while a selection is being dragged.\n  const onDOMSelectionChange = useMemo(\n    () =>\n      throttle(() => {\n        const androidInputManager = androidInputManagerRef.current\n        if (\n          (IS_ANDROID || !ReactEditor.isComposing(editor)) &&\n          (!state.isUpdatingSelection || androidInputManager?.isFlushing()) &&\n          !state.isDraggingInternally\n        ) {\n          const root = ReactEditor.findDocumentOrShadowRoot(editor)\n          const { activeElement } = root\n          const el = ReactEditor.toDOMNode(editor, editor)\n          const domSelection = root.getSelection()\n\n          if (activeElement === el) {\n            state.latestElement = activeElement\n            IS_FOCUSED.set(editor, true)\n          } else {\n            IS_FOCUSED.delete(editor)\n          }\n\n          if (!domSelection) {\n            return Transforms.deselect(editor)\n          }\n\n          const { anchorNode, focusNode } = domSelection\n\n          const anchorNodeSelectable =\n            ReactEditor.hasEditableTarget(editor, anchorNode) ||\n            ReactEditor.isTargetInsideNonReadonlyVoid(editor, anchorNode)\n\n          const focusNodeSelectable =\n            ReactEditor.hasEditableTarget(editor, focusNode) ||\n            ReactEditor.isTargetInsideNonReadonlyVoid(editor, focusNode)\n\n          if (anchorNodeSelectable && focusNodeSelectable) {\n            const range = ReactEditor.toSlateRange(editor, domSelection, {\n              exactMatch: false,\n              suppressThrow: true,\n            })\n\n            if (range) {\n              if (\n                !ReactEditor.isComposing(editor) &&\n                !androidInputManager?.hasPendingChanges() &&\n                !androidInputManager?.isFlushing()\n              ) {\n                Transforms.select(editor, range)\n              } else {\n                androidInputManager?.handleUserSelect(range)\n              }\n            }\n          }\n\n          // Deselect the editor if the dom selection is not selectable in readonly mode\n          if (readOnly && (!anchorNodeSelectable || !focusNodeSelectable)) {\n            Transforms.deselect(editor)\n          }\n        }\n      }, 100),\n    [editor, readOnly, state]\n  )\n\n  const scheduleOnDOMSelectionChange = useMemo(\n    () => debounce(onDOMSelectionChange, 0),\n    [onDOMSelectionChange]\n  )\n\n  androidInputManagerRef.current = useAndroidInputManager({\n    node: ref,\n    onDOMSelectionChange,\n    scheduleOnDOMSelectionChange,\n  })\n\n  useIsomorphicLayoutEffect(() => {\n    // Update element-related weak maps with the DOM element ref.\n    let window\n    if (ref.current && (window = getDefaultView(ref.current))) {\n      EDITOR_TO_WINDOW.set(editor, window)\n      EDITOR_TO_ELEMENT.set(editor, ref.current)\n      NODE_TO_ELEMENT.set(editor, ref.current)\n      ELEMENT_TO_NODE.set(ref.current, editor)\n    } else {\n      NODE_TO_ELEMENT.delete(editor)\n    }\n\n    // Make sure the DOM selection state is in sync.\n    const { selection } = editor\n    const root = ReactEditor.findDocumentOrShadowRoot(editor)\n    const domSelection = root.getSelection()\n\n    if (\n      !domSelection ||\n      !ReactEditor.isFocused(editor) ||\n      androidInputManagerRef.current?.hasPendingAction()\n    ) {\n      return\n    }\n\n    const setDomSelection = (forceChange?: boolean) => {\n      const hasDomSelection = domSelection.type !== 'None'\n\n      // If the DOM selection is properly unset, we're done.\n      if (!selection && !hasDomSelection) {\n        return\n      }\n\n      // Get anchorNode and focusNode\n      const focusNode = domSelection.focusNode\n      let anchorNode\n\n      // COMPAT: In firefox the normal seletion way does not work\n      // (https://github.com/ianstormtaylor/slate/pull/5486#issue-1820720223)\n      if (IS_FIREFOX && domSelection.rangeCount > 1) {\n        const firstRange = domSelection.getRangeAt(0)\n        const lastRange = domSelection.getRangeAt(domSelection.rangeCount - 1)\n\n        // Right to left\n        if (firstRange.startContainer === focusNode) {\n          anchorNode = lastRange.endContainer\n        } else {\n          // Left to right\n          anchorNode = firstRange.startContainer\n        }\n      } else {\n        anchorNode = domSelection.anchorNode\n      }\n\n      // verify that the dom selection is in the editor\n      const editorElement = EDITOR_TO_ELEMENT.get(editor)!\n      let hasDomSelectionInEditor = false\n      if (\n        editorElement.contains(anchorNode) &&\n        editorElement.contains(focusNode)\n      ) {\n        hasDomSelectionInEditor = true\n      }\n\n      // If the DOM selection is in the editor and the editor selection is already correct, we're done.\n      if (\n        hasDomSelection &&\n        hasDomSelectionInEditor &&\n        selection &&\n        !forceChange\n      ) {\n        const slateRange = ReactEditor.toSlateRange(editor, domSelection, {\n          exactMatch: true,\n\n          // domSelection is not necessarily a valid Slate range\n          // (e.g. when clicking on contentEditable:false element)\n          suppressThrow: true,\n        })\n\n        if (slateRange && Range.equals(slateRange, selection)) {\n          if (!state.hasMarkPlaceholder) {\n            return\n          }\n\n          // Ensure selection is inside the mark placeholder\n          if (\n            anchorNode?.parentElement?.hasAttribute(\n              'data-slate-mark-placeholder'\n            )\n          ) {\n            return\n          }\n        }\n      }\n\n      // when <Editable/> is being controlled through external value\n      // then its children might just change - DOM responds to it on its own\n      // but Slate's value is not being updated through any operation\n      // and thus it doesn't transform selection on its own\n      if (selection && !ReactEditor.hasRange(editor, selection)) {\n        editor.selection = ReactEditor.toSlateRange(editor, domSelection, {\n          exactMatch: false,\n          suppressThrow: true,\n        })\n        return\n      }\n\n      // Otherwise the DOM selection is out of sync, so update it.\n      state.isUpdatingSelection = true\n\n      const newDomRange: DOMRange | null =\n        selection && ReactEditor.toDOMRange(editor, selection)\n\n      if (newDomRange) {\n        if (ReactEditor.isComposing(editor) && !IS_ANDROID) {\n          domSelection.collapseToEnd()\n        } else if (Range.isBackward(selection!)) {\n          domSelection.setBaseAndExtent(\n            newDomRange.endContainer,\n            newDomRange.endOffset,\n            newDomRange.startContainer,\n            newDomRange.startOffset\n          )\n        } else {\n          domSelection.setBaseAndExtent(\n            newDomRange.startContainer,\n            newDomRange.startOffset,\n            newDomRange.endContainer,\n            newDomRange.endOffset\n          )\n        }\n        scrollSelectionIntoView(editor, newDomRange)\n      } else {\n        domSelection.removeAllRanges()\n      }\n\n      return newDomRange\n    }\n\n    // In firefox if there is more then 1 range and we call setDomSelection we remove the ability to select more cells in a table\n    if (domSelection.rangeCount <= 1) {\n      setDomSelection()\n    }\n\n    const ensureSelection =\n      androidInputManagerRef.current?.isFlushing() === 'action'\n\n    if (!IS_ANDROID || !ensureSelection) {\n      setTimeout(() => {\n        state.isUpdatingSelection = false\n      })\n      return\n    }\n\n    let timeoutId: ReturnType<typeof setTimeout> | null = null\n    const animationFrameId = requestAnimationFrame(() => {\n      if (ensureSelection) {\n        const ensureDomSelection = (forceChange?: boolean) => {\n          try {\n            const el = ReactEditor.toDOMNode(editor, editor)\n            el.focus()\n\n            setDomSelection(forceChange)\n          } catch (e) {\n            // Ignore, dom and state might be out of sync\n          }\n        }\n\n        // Compat: Android IMEs try to force their selection by manually re-applying it even after we set it.\n        // This essentially would make setting the slate selection during an update meaningless, so we force it\n        // again here. We can't only do it in the setTimeout after the animation frame since that would cause a\n        // visible flicker.\n        ensureDomSelection()\n\n        timeoutId = setTimeout(() => {\n          // COMPAT: While setting the selection in an animation frame visually correctly sets the selection,\n          // it doesn't update GBoards spellchecker state. We have to manually trigger a selection change after\n          // the animation frame to ensure it displays the correct state.\n          ensureDomSelection(true)\n          state.isUpdatingSelection = false\n        })\n      }\n    })\n\n    return () => {\n      cancelAnimationFrame(animationFrameId)\n      if (timeoutId) {\n        clearTimeout(timeoutId)\n      }\n    }\n  })\n\n  // Listen on the native `beforeinput` event to get real \"Level 2\" events. This\n  // is required because React's `beforeinput` is fake and never really attaches\n  // to the real event sadly. (2019/11/01)\n  // https://github.com/facebook/react/issues/11211\n  const onDOMBeforeInput = useCallback(\n    (event: InputEvent) => {\n      onUserInput()\n\n      if (\n        !readOnly &&\n        ReactEditor.hasEditableTarget(editor, event.target) &&\n        !isDOMEventHandled(event, propsOnDOMBeforeInput)\n      ) {\n        // COMPAT: BeforeInput events aren't cancelable on android, so we have to handle them differently using the android input manager.\n        if (androidInputManagerRef.current) {\n          return androidInputManagerRef.current.handleDOMBeforeInput(event)\n        }\n\n        // Some IMEs/Chrome extensions like e.g. Grammarly set the selection immediately before\n        // triggering a `beforeinput` expecting the change to be applied to the immediately before\n        // set selection.\n        scheduleOnDOMSelectionChange.flush()\n        onDOMSelectionChange.flush()\n\n        const { selection } = editor\n        const { inputType: type } = event\n        const data = (event as any).dataTransfer || event.data || undefined\n\n        const isCompositionChange =\n          type === 'insertCompositionText' || type === 'deleteCompositionText'\n\n        // COMPAT: use composition change events as a hint to where we should insert\n        // composition text if we aren't composing to work around https://github.com/ianstormtaylor/slate/issues/5038\n        if (isCompositionChange && ReactEditor.isComposing(editor)) {\n          return\n        }\n\n        let native = false\n        if (\n          type === 'insertText' &&\n          selection &&\n          Range.isCollapsed(selection) &&\n          // Only use native character insertion for single characters a-z or space for now.\n          // Long-press events (hold a + press 4 = ä) to choose a special character otherwise\n          // causes duplicate inserts.\n          event.data &&\n          event.data.length === 1 &&\n          /[a-z ]/i.test(event.data) &&\n          // Chrome has issues correctly editing the start of nodes: https://bugs.chromium.org/p/chromium/issues/detail?id=1249405\n          // When there is an inline element, e.g. a link, and you select\n          // right after it (the start of the next node).\n          selection.anchor.offset !== 0\n        ) {\n          native = true\n\n          // Skip native if there are marks, as\n          // `insertText` will insert a node, not just text.\n          if (editor.marks) {\n            native = false\n          }\n\n          // Chrome also has issues correctly editing the end of anchor elements: https://bugs.chromium.org/p/chromium/issues/detail?id=1259100\n          // Therefore we don't allow native events to insert text at the end of anchor nodes.\n          const { anchor } = selection\n\n          const [node, offset] = ReactEditor.toDOMPoint(editor, anchor)\n          const anchorNode = node.parentElement?.closest('a')\n\n          const window = ReactEditor.getWindow(editor)\n\n          if (\n            native &&\n            anchorNode &&\n            ReactEditor.hasDOMNode(editor, anchorNode)\n          ) {\n            // Find the last text node inside the anchor.\n            const lastText = window?.document\n              .createTreeWalker(anchorNode, NodeFilter.SHOW_TEXT)\n              .lastChild() as DOMText | null\n\n            if (lastText === node && lastText.textContent?.length === offset) {\n              native = false\n            }\n          }\n\n          // Chrome has issues with the presence of tab characters inside elements with whiteSpace = 'pre'\n          // causing abnormal insert behavior: https://bugs.chromium.org/p/chromium/issues/detail?id=1219139\n          if (\n            native &&\n            node.parentElement &&\n            window?.getComputedStyle(node.parentElement)?.whiteSpace === 'pre'\n          ) {\n            const block = Editor.above(editor, {\n              at: anchor.path,\n              match: n => Element.isElement(n) && Editor.isBlock(editor, n),\n            })\n\n            if (block && Node.string(block[0]).includes('\\t')) {\n              native = false\n            }\n          }\n        }\n\n        // COMPAT: For the deleting forward/backward input types we don't want\n        // to change the selection because it is the range that will be deleted,\n        // and those commands determine that for themselves.\n        if (!type.startsWith('delete') || type.startsWith('deleteBy')) {\n          const [targetRange] = (event as any).getTargetRanges()\n\n          if (targetRange) {\n            const range = ReactEditor.toSlateRange(editor, targetRange, {\n              exactMatch: false,\n              suppressThrow: false,\n            })\n\n            if (!selection || !Range.equals(selection, range)) {\n              native = false\n\n              const selectionRef =\n                !isCompositionChange &&\n                editor.selection &&\n                Editor.rangeRef(editor, editor.selection)\n\n              Transforms.select(editor, range)\n\n              if (selectionRef) {\n                EDITOR_TO_USER_SELECTION.set(editor, selectionRef)\n              }\n            }\n          }\n        }\n\n        // Composition change types occur while a user is composing text and can't be\n        // cancelled. Let them through and wait for the composition to end.\n        if (isCompositionChange) {\n          return\n        }\n\n        if (!native) {\n          event.preventDefault()\n        }\n\n        // COMPAT: If the selection is expanded, even if the command seems like\n        // a delete forward/backward command it should delete the selection.\n        if (\n          selection &&\n          Range.isExpanded(selection) &&\n          type.startsWith('delete')\n        ) {\n          const direction = type.endsWith('Backward') ? 'backward' : 'forward'\n          Editor.deleteFragment(editor, { direction })\n          return\n        }\n\n        switch (type) {\n          case 'deleteByComposition':\n          case 'deleteByCut':\n          case 'deleteByDrag': {\n            Editor.deleteFragment(editor)\n            break\n          }\n\n          case 'deleteContent':\n          case 'deleteContentForward': {\n            Editor.deleteForward(editor)\n            break\n          }\n\n          case 'deleteContentBackward': {\n            Editor.deleteBackward(editor)\n            break\n          }\n\n          case 'deleteEntireSoftLine': {\n            Editor.deleteBackward(editor, { unit: 'line' })\n            Editor.deleteForward(editor, { unit: 'line' })\n            break\n          }\n\n          case 'deleteHardLineBackward': {\n            Editor.deleteBackward(editor, { unit: 'block' })\n            break\n          }\n\n          case 'deleteSoftLineBackward': {\n            Editor.deleteBackward(editor, { unit: 'line' })\n            break\n          }\n\n          case 'deleteHardLineForward': {\n            Editor.deleteForward(editor, { unit: 'block' })\n            break\n          }\n\n          case 'deleteSoftLineForward': {\n            Editor.deleteForward(editor, { unit: 'line' })\n            break\n          }\n\n          case 'deleteWordBackward': {\n            Editor.deleteBackward(editor, { unit: 'word' })\n            break\n          }\n\n          case 'deleteWordForward': {\n            Editor.deleteForward(editor, { unit: 'word' })\n            break\n          }\n\n          case 'insertLineBreak':\n            Editor.insertSoftBreak(editor)\n            break\n\n          case 'insertParagraph': {\n            Editor.insertBreak(editor)\n            break\n          }\n\n          case 'insertFromComposition':\n          case 'insertFromDrop':\n          case 'insertFromPaste':\n          case 'insertFromYank':\n          case 'insertReplacementText':\n          case 'insertText': {\n            if (type === 'insertFromComposition') {\n              // COMPAT: in Safari, `compositionend` is dispatched after the\n              // `beforeinput` for \"insertFromComposition\". But if we wait for it\n              // then we will abort because we're still composing and the selection\n              // won't be updated properly.\n              // https://www.w3.org/TR/input-events-2/\n              if (ReactEditor.isComposing(editor)) {\n                setIsComposing(false)\n                IS_COMPOSING.set(editor, false)\n              }\n            }\n\n            // use a weak comparison instead of 'instanceof' to allow\n            // programmatic access of paste events coming from external windows\n            // like cypress where cy.window does not work realibly\n            if (data?.constructor.name === 'DataTransfer') {\n              ReactEditor.insertData(editor, data)\n            } else if (typeof data === 'string') {\n              // Only insertText operations use the native functionality, for now.\n              // Potentially expand to single character deletes, as well.\n              if (native) {\n                deferredOperations.current.push(() =>\n                  Editor.insertText(editor, data)\n                )\n              } else {\n                Editor.insertText(editor, data)\n              }\n            }\n\n            break\n          }\n        }\n\n        // Restore the actual user section if nothing manually set it.\n        const toRestore = EDITOR_TO_USER_SELECTION.get(editor)?.unref()\n        EDITOR_TO_USER_SELECTION.delete(editor)\n\n        if (\n          toRestore &&\n          (!editor.selection || !Range.equals(editor.selection, toRestore))\n        ) {\n          Transforms.select(editor, toRestore)\n        }\n      }\n    },\n    [\n      editor,\n      onDOMSelectionChange,\n      onUserInput,\n      propsOnDOMBeforeInput,\n      readOnly,\n      scheduleOnDOMSelectionChange,\n    ]\n  )\n\n  const callbackRef = useCallback(\n    node => {\n      if (node == null) {\n        onDOMSelectionChange.cancel()\n        scheduleOnDOMSelectionChange.cancel()\n\n        EDITOR_TO_ELEMENT.delete(editor)\n        NODE_TO_ELEMENT.delete(editor)\n\n        if (ref.current && HAS_BEFORE_INPUT_SUPPORT) {\n          // @ts-ignore The `beforeinput` event isn't recognized.\n          ref.current.removeEventListener('beforeinput', onDOMBeforeInput)\n        }\n      } else {\n        // Attach a native DOM event handler for `beforeinput` events, because React's\n        // built-in `onBeforeInput` is actually a leaky polyfill that doesn't expose\n        // real `beforeinput` events sadly... (2019/11/04)\n        // https://github.com/facebook/react/issues/11211\n        if (HAS_BEFORE_INPUT_SUPPORT) {\n          // @ts-ignore The `beforeinput` event isn't recognized.\n          node.addEventListener('beforeinput', onDOMBeforeInput)\n        }\n      }\n\n      ref.current = node\n    },\n    [\n      onDOMSelectionChange,\n      scheduleOnDOMSelectionChange,\n      editor,\n      onDOMBeforeInput,\n    ]\n  )\n\n  // Attach a native DOM event handler for `selectionchange`, because React's\n  // built-in `onSelect` handler doesn't fire for all selection changes. It's a\n  // leaky polyfill that only fires on keypresses or clicks. Instead, we want to\n  // fire for any change to the selection inside the editor. (2019/11/04)\n  // https://github.com/facebook/react/issues/5785\n  useIsomorphicLayoutEffect(() => {\n    const window = ReactEditor.getWindow(editor)\n\n    window.document.addEventListener(\n      'selectionchange',\n      scheduleOnDOMSelectionChange\n    )\n\n    return () => {\n      window.document.removeEventListener(\n        'selectionchange',\n        scheduleOnDOMSelectionChange\n      )\n    }\n  }, [scheduleOnDOMSelectionChange])\n\n  const decorations = decorate([editor, []])\n\n  const showPlaceholder =\n    placeholder &&\n    editor.children.length === 1 &&\n    Array.from(Node.texts(editor)).length === 1 &&\n    Node.string(editor) === '' &&\n    !isComposing\n\n  const placeHolderResizeHandler = useCallback(\n    (placeholderEl: HTMLElement | null) => {\n      if (placeholderEl && showPlaceholder) {\n        setPlaceholderHeight(placeholderEl.getBoundingClientRect()?.height)\n      } else {\n        setPlaceholderHeight(undefined)\n      }\n    },\n    [showPlaceholder]\n  )\n\n  if (showPlaceholder) {\n    const start = Editor.start(editor, [])\n    decorations.push({\n      [PLACEHOLDER_SYMBOL]: true,\n      placeholder,\n      onPlaceholderResize: placeHolderResizeHandler,\n      anchor: start,\n      focus: start,\n    })\n  }\n\n  const { marks } = editor\n  state.hasMarkPlaceholder = false\n\n  if (editor.selection && Range.isCollapsed(editor.selection) && marks) {\n    const { anchor } = editor.selection\n    const leaf = Node.leaf(editor, anchor.path)\n    const { text, ...rest } = leaf\n\n    // While marks isn't a 'complete' text, we can still use loose Text.equals\n    // here which only compares marks anyway.\n    if (!Text.equals(leaf, marks as Text, { loose: true })) {\n      state.hasMarkPlaceholder = true\n\n      const unset = Object.fromEntries(\n        Object.keys(rest).map(mark => [mark, null])\n      )\n\n      decorations.push({\n        [MARK_PLACEHOLDER_SYMBOL]: true,\n        ...unset,\n        ...marks,\n\n        anchor,\n        focus: anchor,\n      })\n    }\n  }\n\n  // Update EDITOR_TO_MARK_PLACEHOLDER_MARKS in setTimeout useEffect to ensure we don't set it\n  // before we receive the composition end event.\n  useEffect(() => {\n    setTimeout(() => {\n      const { selection } = editor\n      if (selection) {\n        const { anchor } = selection\n        const text = Node.leaf(editor, anchor.path)\n\n        // While marks isn't a 'complete' text, we can still use loose Text.equals\n        // here which only compares marks anyway.\n        if (marks && !Text.equals(text, marks as Text, { loose: true })) {\n          EDITOR_TO_PENDING_INSERTION_MARKS.set(editor, marks)\n          return\n        }\n      }\n\n      EDITOR_TO_PENDING_INSERTION_MARKS.delete(editor)\n    })\n  })\n\n  return (\n    <ReadOnlyContext.Provider value={readOnly}>\n      <DecorateContext.Provider value={decorate}>\n        <RestoreDOM node={ref} receivedUserInput={receivedUserInput}>\n          <Component\n            role={readOnly ? undefined : 'textbox'}\n            aria-multiline={readOnly ? undefined : true}\n            {...attributes}\n            // COMPAT: Certain browsers don't support the `beforeinput` event, so we'd\n            // have to use hacks to make these replacement-based features work.\n            // For SSR situations HAS_BEFORE_INPUT_SUPPORT is false and results in prop\n            // mismatch warning app moves to browser. Pass-through consumer props when\n            // not CAN_USE_DOM (SSR) and default to falsy value\n            spellCheck={\n              HAS_BEFORE_INPUT_SUPPORT || !CAN_USE_DOM\n                ? attributes.spellCheck\n                : false\n            }\n            autoCorrect={\n              HAS_BEFORE_INPUT_SUPPORT || !CAN_USE_DOM\n                ? attributes.autoCorrect\n                : 'false'\n            }\n            autoCapitalize={\n              HAS_BEFORE_INPUT_SUPPORT || !CAN_USE_DOM\n                ? attributes.autoCapitalize\n                : 'false'\n            }\n            data-slate-editor\n            data-slate-node=\"value\"\n            // explicitly set this\n            contentEditable={!readOnly}\n            // in some cases, a decoration needs access to the range / selection to decorate a text node,\n            // then you will select the whole text node when you select part the of text\n            // this magic zIndex=\"-1\" will fix it\n            zindex={-1}\n            suppressContentEditableWarning\n            ref={callbackRef}\n            style={{\n              ...(disableDefaultStyles\n                ? {}\n                : {\n                    // Allow positioning relative to the editable element.\n                    position: 'relative',\n                    // Preserve adjacent whitespace and new lines.\n                    whiteSpace: 'pre-wrap',\n                    // Allow words to break if they are too long.\n                    wordWrap: 'break-word',\n                    // Make the minimum height that of the placeholder.\n                    ...(placeholderHeight\n                      ? { minHeight: placeholderHeight }\n                      : {}),\n                  }),\n              // Allow for passed-in styles to override anything.\n              ...userStyle,\n            }}\n            onBeforeInput={useCallback(\n              (event: React.FormEvent<HTMLDivElement>) => {\n                // COMPAT: Certain browsers don't support the `beforeinput` event, so we\n                // fall back to React's leaky polyfill instead just for it. It\n                // only works for the `insertText` input type.\n                if (\n                  !HAS_BEFORE_INPUT_SUPPORT &&\n                  !readOnly &&\n                  !isEventHandled(event, attributes.onBeforeInput) &&\n                  ReactEditor.hasSelectableTarget(editor, event.target)\n                ) {\n                  event.preventDefault()\n                  if (!ReactEditor.isComposing(editor)) {\n                    const text = (event as any).data as string\n                    Editor.insertText(editor, text)\n                  }\n                }\n              },\n              [attributes.onBeforeInput, editor, readOnly]\n            )}\n            onInput={useCallback(\n              (event: React.FormEvent<HTMLDivElement>) => {\n                if (isEventHandled(event, attributes.onInput)) {\n                  return\n                }\n\n                if (androidInputManagerRef.current) {\n                  androidInputManagerRef.current.handleInput()\n                  return\n                }\n\n                // Flush native operations, as native events will have propogated\n                // and we can correctly compare DOM text values in components\n                // to stop rendering, so that browser functions like autocorrect\n                // and spellcheck work as expected.\n                for (const op of deferredOperations.current) {\n                  op()\n                }\n                deferredOperations.current = []\n              },\n              [attributes.onInput]\n            )}\n            onBlur={useCallback(\n              (event: React.FocusEvent<HTMLDivElement>) => {\n                if (\n                  readOnly ||\n                  state.isUpdatingSelection ||\n                  !ReactEditor.hasSelectableTarget(editor, event.target) ||\n                  isEventHandled(event, attributes.onBlur)\n                ) {\n                  return\n                }\n\n                // COMPAT: If the current `activeElement` is still the previous\n                // one, this is due to the window being blurred when the tab\n                // itself becomes unfocused, so we want to abort early to allow to\n                // editor to stay focused when the tab becomes focused again.\n                const root = ReactEditor.findDocumentOrShadowRoot(editor)\n                if (state.latestElement === root.activeElement) {\n                  return\n                }\n\n                const { relatedTarget } = event\n                const el = ReactEditor.toDOMNode(editor, editor)\n\n                // COMPAT: The event should be ignored if the focus is returning\n                // to the editor from an embedded editable element (eg. an <input>\n                // element inside a void node).\n                if (relatedTarget === el) {\n                  return\n                }\n\n                // COMPAT: The event should be ignored if the focus is moving from\n                // the editor to inside a void node's spacer element.\n                if (\n                  isDOMElement(relatedTarget) &&\n                  relatedTarget.hasAttribute('data-slate-spacer')\n                ) {\n                  return\n                }\n\n                // COMPAT: The event should be ignored if the focus is moving to a\n                // non- editable section of an element that isn't a void node (eg.\n                // a list item of the check list example).\n                if (\n                  relatedTarget != null &&\n                  isDOMNode(relatedTarget) &&\n                  ReactEditor.hasDOMNode(editor, relatedTarget)\n                ) {\n                  const node = ReactEditor.toSlateNode(editor, relatedTarget)\n\n                  if (Element.isElement(node) && !editor.isVoid(node)) {\n                    return\n                  }\n                }\n\n                // COMPAT: Safari doesn't always remove the selection even if the content-\n                // editable element no longer has focus. Refer to:\n                // https://stackoverflow.com/questions/12353247/force-contenteditable-div-to-stop-accepting-input-after-it-loses-focus-under-web\n                if (IS_WEBKIT) {\n                  const domSelection = root.getSelection()\n                  domSelection?.removeAllRanges()\n                }\n\n                IS_FOCUSED.delete(editor)\n              },\n              [\n                readOnly,\n                state.isUpdatingSelection,\n                state.latestElement,\n                editor,\n                attributes.onBlur,\n              ]\n            )}\n            onClick={useCallback(\n              (event: React.MouseEvent<HTMLDivElement>) => {\n                if (\n                  ReactEditor.hasTarget(editor, event.target) &&\n                  !isEventHandled(event, attributes.onClick) &&\n                  isDOMNode(event.target)\n                ) {\n                  const node = ReactEditor.toSlateNode(editor, event.target)\n                  const path = ReactEditor.findPath(editor, node)\n\n                  // At this time, the Slate document may be arbitrarily different,\n                  // because onClick handlers can change the document before we get here.\n                  // Therefore we must check that this path actually exists,\n                  // and that it still refers to the same node.\n                  if (\n                    !Editor.hasPath(editor, path) ||\n                    Node.get(editor, path) !== node\n                  ) {\n                    return\n                  }\n\n                  if (event.detail === TRIPLE_CLICK && path.length >= 1) {\n                    let blockPath = path\n                    if (\n                      !(Element.isElement(node) && Editor.isBlock(editor, node))\n                    ) {\n                      const block = Editor.above(editor, {\n                        match: n =>\n                          Element.isElement(n) && Editor.isBlock(editor, n),\n                        at: path,\n                      })\n\n                      blockPath = block?.[1] ?? path.slice(0, 1)\n                    }\n\n                    const range = Editor.range(editor, blockPath)\n                    Transforms.select(editor, range)\n                    return\n                  }\n\n                  if (readOnly) {\n                    return\n                  }\n\n                  const start = Editor.start(editor, path)\n                  const end = Editor.end(editor, path)\n                  const startVoid = Editor.void(editor, { at: start })\n                  const endVoid = Editor.void(editor, { at: end })\n\n                  if (\n                    startVoid &&\n                    endVoid &&\n                    Path.equals(startVoid[1], endVoid[1])\n                  ) {\n                    const range = Editor.range(editor, start)\n                    Transforms.select(editor, range)\n                  }\n                }\n              },\n              [editor, attributes.onClick, readOnly]\n            )}\n            onCompositionEnd={useCallback(\n              (event: React.CompositionEvent<HTMLDivElement>) => {\n                if (ReactEditor.hasSelectableTarget(editor, event.target)) {\n                  if (ReactEditor.isComposing(editor)) {\n                    setIsComposing(false)\n                    IS_COMPOSING.set(editor, false)\n                  }\n\n                  androidInputManagerRef.current?.handleCompositionEnd(event)\n\n                  if (\n                    isEventHandled(event, attributes.onCompositionEnd) ||\n                    IS_ANDROID\n                  ) {\n                    return\n                  }\n\n                  // COMPAT: In Chrome, `beforeinput` events for compositions\n                  // aren't correct and never fire the \"insertFromComposition\"\n                  // type that we need. So instead, insert whenever a composition\n                  // ends since it will already have been committed to the DOM.\n                  if (\n                    !IS_WEBKIT &&\n                    !IS_FIREFOX_LEGACY &&\n                    !IS_IOS &&\n                    !IS_WECHATBROWSER &&\n                    !IS_UC_MOBILE &&\n                    event.data\n                  ) {\n                    const placeholderMarks = EDITOR_TO_PENDING_INSERTION_MARKS.get(\n                      editor\n                    )\n                    EDITOR_TO_PENDING_INSERTION_MARKS.delete(editor)\n\n                    // Ensure we insert text with the marks the user was actually seeing\n                    if (placeholderMarks !== undefined) {\n                      EDITOR_TO_USER_MARKS.set(editor, editor.marks)\n                      editor.marks = placeholderMarks\n                    }\n\n                    Editor.insertText(editor, event.data)\n\n                    const userMarks = EDITOR_TO_USER_MARKS.get(editor)\n                    EDITOR_TO_USER_MARKS.delete(editor)\n                    if (userMarks !== undefined) {\n                      editor.marks = userMarks\n                    }\n                  }\n                }\n              },\n              [attributes.onCompositionEnd, editor]\n            )}\n            onCompositionUpdate={useCallback(\n              (event: React.CompositionEvent<HTMLDivElement>) => {\n                if (\n                  ReactEditor.hasSelectableTarget(editor, event.target) &&\n                  !isEventHandled(event, attributes.onCompositionUpdate)\n                ) {\n                  if (!ReactEditor.isComposing(editor)) {\n                    setIsComposing(true)\n                    IS_COMPOSING.set(editor, true)\n                  }\n                }\n              },\n              [attributes.onCompositionUpdate, editor]\n            )}\n            onCompositionStart={useCallback(\n              (event: React.CompositionEvent<HTMLDivElement>) => {\n                if (ReactEditor.hasSelectableTarget(editor, event.target)) {\n                  androidInputManagerRef.current?.handleCompositionStart(event)\n\n                  if (\n                    isEventHandled(event, attributes.onCompositionStart) ||\n                    IS_ANDROID\n                  ) {\n                    return\n                  }\n\n                  setIsComposing(true)\n\n                  const { selection } = editor\n                  if (selection) {\n                    if (Range.isExpanded(selection)) {\n                      Editor.deleteFragment(editor)\n                      return\n                    }\n                    const inline = Editor.above(editor, {\n                      match: n =>\n                        Element.isElement(n) && Editor.isInline(editor, n),\n                      mode: 'highest',\n                    })\n                    if (inline) {\n                      const [, inlinePath] = inline\n                      if (Editor.isEnd(editor, selection.anchor, inlinePath)) {\n                        const point = Editor.after(editor, inlinePath)!\n                        Transforms.setSelection(editor, {\n                          anchor: point,\n                          focus: point,\n                        })\n                      }\n                    }\n                  }\n                }\n              },\n              [attributes.onCompositionStart, editor]\n            )}\n            onCopy={useCallback(\n              (event: React.ClipboardEvent<HTMLDivElement>) => {\n                if (\n                  ReactEditor.hasSelectableTarget(editor, event.target) &&\n                  !isEventHandled(event, attributes.onCopy) &&\n                  !isDOMEventTargetInput(event)\n                ) {\n                  event.preventDefault()\n                  ReactEditor.setFragmentData(\n                    editor,\n                    event.clipboardData,\n                    'copy'\n                  )\n                }\n              },\n              [attributes.onCopy, editor]\n            )}\n            onCut={useCallback(\n              (event: React.ClipboardEvent<HTMLDivElement>) => {\n                if (\n                  !readOnly &&\n                  ReactEditor.hasSelectableTarget(editor, event.target) &&\n                  !isEventHandled(event, attributes.onCut) &&\n                  !isDOMEventTargetInput(event)\n                ) {\n                  event.preventDefault()\n                  ReactEditor.setFragmentData(\n                    editor,\n                    event.clipboardData,\n                    'cut'\n                  )\n                  const { selection } = editor\n\n                  if (selection) {\n                    if (Range.isExpanded(selection)) {\n                      Editor.deleteFragment(editor)\n                    } else {\n                      const node = Node.parent(editor, selection.anchor.path)\n                      if (Editor.isVoid(editor, node)) {\n                        Transforms.delete(editor)\n                      }\n                    }\n                  }\n                }\n              },\n              [readOnly, editor, attributes.onCut]\n            )}\n            onDragOver={useCallback(\n              (event: React.DragEvent<HTMLDivElement>) => {\n                if (\n                  ReactEditor.hasTarget(editor, event.target) &&\n                  !isEventHandled(event, attributes.onDragOver)\n                ) {\n                  // Only when the target is void, call `preventDefault` to signal\n                  // that drops are allowed. Editable content is droppable by\n                  // default, and calling `preventDefault` hides the cursor.\n                  const node = ReactEditor.toSlateNode(editor, event.target)\n\n                  if (Element.isElement(node) && Editor.isVoid(editor, node)) {\n                    event.preventDefault()\n                  }\n                }\n              },\n              [attributes.onDragOver, editor]\n            )}\n            onDragStart={useCallback(\n              (event: React.DragEvent<HTMLDivElement>) => {\n                if (\n                  !readOnly &&\n                  ReactEditor.hasTarget(editor, event.target) &&\n                  !isEventHandled(event, attributes.onDragStart)\n                ) {\n                  const node = ReactEditor.toSlateNode(editor, event.target)\n                  const path = ReactEditor.findPath(editor, node)\n                  const voidMatch =\n                    (Element.isElement(node) && Editor.isVoid(editor, node)) ||\n                    Editor.void(editor, { at: path, voids: true })\n\n                  // If starting a drag on a void node, make sure it is selected\n                  // so that it shows up in the selection's fragment.\n                  if (voidMatch) {\n                    const range = Editor.range(editor, path)\n                    Transforms.select(editor, range)\n                  }\n\n                  state.isDraggingInternally = true\n\n                  ReactEditor.setFragmentData(\n                    editor,\n                    event.dataTransfer,\n                    'drag'\n                  )\n                }\n              },\n              [readOnly, editor, attributes.onDragStart, state]\n            )}\n            onDrop={useCallback(\n              (event: React.DragEvent<HTMLDivElement>) => {\n                if (\n                  !readOnly &&\n                  ReactEditor.hasTarget(editor, event.target) &&\n                  !isEventHandled(event, attributes.onDrop)\n                ) {\n                  event.preventDefault()\n\n                  // Keep a reference to the dragged range before updating selection\n                  const draggedRange = editor.selection\n\n                  // Find the range where the drop happened\n                  const range = ReactEditor.findEventRange(editor, event)\n                  const data = event.dataTransfer\n\n                  Transforms.select(editor, range)\n\n                  if (state.isDraggingInternally) {\n                    if (\n                      draggedRange &&\n                      !Range.equals(draggedRange, range) &&\n                      !Editor.void(editor, { at: range, voids: true })\n                    ) {\n                      Transforms.delete(editor, {\n                        at: draggedRange,\n                      })\n                    }\n                  }\n\n                  ReactEditor.insertData(editor, data)\n\n                  // When dragging from another source into the editor, it's possible\n                  // that the current editor does not have focus.\n                  if (!ReactEditor.isFocused(editor)) {\n                    ReactEditor.focus(editor)\n                  }\n                }\n\n                state.isDraggingInternally = false\n              },\n              [readOnly, editor, attributes.onDrop, state]\n            )}\n            onDragEnd={useCallback(\n              (event: React.DragEvent<HTMLDivElement>) => {\n                if (\n                  !readOnly &&\n                  state.isDraggingInternally &&\n                  attributes.onDragEnd &&\n                  ReactEditor.hasTarget(editor, event.target)\n                ) {\n                  attributes.onDragEnd(event)\n                }\n\n                // When dropping on a different droppable element than the current editor,\n                // `onDrop` is not called. So we need to clean up in `onDragEnd` instead.\n                // Note: `onDragEnd` is only called when `onDrop` is not called\n                state.isDraggingInternally = false\n              },\n              [readOnly, state, attributes, editor]\n            )}\n            onFocus={useCallback(\n              (event: React.FocusEvent<HTMLDivElement>) => {\n                if (\n                  !readOnly &&\n                  !state.isUpdatingSelection &&\n                  ReactEditor.hasEditableTarget(editor, event.target) &&\n                  !isEventHandled(event, attributes.onFocus)\n                ) {\n                  const el = ReactEditor.toDOMNode(editor, editor)\n                  const root = ReactEditor.findDocumentOrShadowRoot(editor)\n                  state.latestElement = root.activeElement\n\n                  // COMPAT: If the editor has nested editable elements, the focus\n                  // can go to them. In Firefox, this must be prevented because it\n                  // results in issues with keyboard navigation. (2017/03/30)\n                  if (IS_FIREFOX && event.target !== el) {\n                    el.focus()\n                    return\n                  }\n\n                  IS_FOCUSED.set(editor, true)\n                }\n              },\n              [readOnly, state, editor, attributes.onFocus]\n            )}\n            onKeyDown={useCallback(\n              (event: React.KeyboardEvent<HTMLDivElement>) => {\n                if (\n                  !readOnly &&\n                  ReactEditor.hasEditableTarget(editor, event.target)\n                ) {\n                  androidInputManagerRef.current?.handleKeyDown(event)\n\n                  const { nativeEvent } = event\n\n                  // COMPAT: The composition end event isn't fired reliably in all browsers,\n                  // so we sometimes might end up stuck in a composition state even though we\n                  // aren't composing any more.\n                  if (\n                    ReactEditor.isComposing(editor) &&\n                    nativeEvent.isComposing === false\n                  ) {\n                    IS_COMPOSING.set(editor, false)\n                    setIsComposing(false)\n                  }\n\n                  if (\n                    isEventHandled(event, attributes.onKeyDown) ||\n                    ReactEditor.isComposing(editor)\n                  ) {\n                    return\n                  }\n\n                  const { selection } = editor\n                  const element =\n                    editor.children[\n                      selection !== null ? selection.focus.path[0] : 0\n                    ]\n                  const isRTL = getDirection(Node.string(element)) === 'rtl'\n\n                  // COMPAT: Since we prevent the default behavior on\n                  // `beforeinput` events, the browser doesn't think there's ever\n                  // any history stack to undo or redo, so we have to manage these\n                  // hotkeys ourselves. (2019/11/06)\n                  if (Hotkeys.isRedo(nativeEvent)) {\n                    event.preventDefault()\n                    const maybeHistoryEditor: any = editor\n\n                    if (typeof maybeHistoryEditor.redo === 'function') {\n                      maybeHistoryEditor.redo()\n                    }\n\n                    return\n                  }\n\n                  if (Hotkeys.isUndo(nativeEvent)) {\n                    event.preventDefault()\n                    const maybeHistoryEditor: any = editor\n\n                    if (typeof maybeHistoryEditor.undo === 'function') {\n                      maybeHistoryEditor.undo()\n                    }\n\n                    return\n                  }\n\n                  // COMPAT: Certain browsers don't handle the selection updates\n                  // properly. In Chrome, the selection isn't properly extended.\n                  // And in Firefox, the selection isn't properly collapsed.\n                  // (2017/10/17)\n                  if (Hotkeys.isMoveLineBackward(nativeEvent)) {\n                    event.preventDefault()\n                    Transforms.move(editor, { unit: 'line', reverse: true })\n                    return\n                  }\n\n                  if (Hotkeys.isMoveLineForward(nativeEvent)) {\n                    event.preventDefault()\n                    Transforms.move(editor, { unit: 'line' })\n                    return\n                  }\n\n                  if (Hotkeys.isExtendLineBackward(nativeEvent)) {\n                    event.preventDefault()\n                    Transforms.move(editor, {\n                      unit: 'line',\n                      edge: 'focus',\n                      reverse: true,\n                    })\n                    return\n                  }\n\n                  if (Hotkeys.isExtendLineForward(nativeEvent)) {\n                    event.preventDefault()\n                    Transforms.move(editor, { unit: 'line', edge: 'focus' })\n                    return\n                  }\n\n                  // COMPAT: If a void node is selected, or a zero-width text node\n                  // adjacent to an inline is selected, we need to handle these\n                  // hotkeys manually because browsers won't be able to skip over\n                  // the void node with the zero-width space not being an empty\n                  // string.\n                  if (Hotkeys.isMoveBackward(nativeEvent)) {\n                    event.preventDefault()\n\n                    if (selection && Range.isCollapsed(selection)) {\n                      Transforms.move(editor, { reverse: !isRTL })\n                    } else {\n                      Transforms.collapse(editor, { edge: 'start' })\n                    }\n\n                    return\n                  }\n\n                  if (Hotkeys.isMoveForward(nativeEvent)) {\n                    event.preventDefault()\n\n                    if (selection && Range.isCollapsed(selection)) {\n                      Transforms.move(editor, { reverse: isRTL })\n                    } else {\n                      Transforms.collapse(editor, { edge: 'end' })\n                    }\n\n                    return\n                  }\n\n                  if (Hotkeys.isMoveWordBackward(nativeEvent)) {\n                    event.preventDefault()\n\n                    if (selection && Range.isExpanded(selection)) {\n                      Transforms.collapse(editor, { edge: 'focus' })\n                    }\n\n                    Transforms.move(editor, { unit: 'word', reverse: !isRTL })\n                    return\n                  }\n\n                  if (Hotkeys.isMoveWordForward(nativeEvent)) {\n                    event.preventDefault()\n\n                    if (selection && Range.isExpanded(selection)) {\n                      Transforms.collapse(editor, { edge: 'focus' })\n                    }\n\n                    Transforms.move(editor, { unit: 'word', reverse: isRTL })\n                    return\n                  }\n\n                  // COMPAT: Certain browsers don't support the `beforeinput` event, so we\n                  // fall back to guessing at the input intention for hotkeys.\n                  // COMPAT: In iOS, some of these hotkeys are handled in the\n                  if (!HAS_BEFORE_INPUT_SUPPORT) {\n                    // We don't have a core behavior for these, but they change the\n                    // DOM if we don't prevent them, so we have to.\n                    if (\n                      Hotkeys.isBold(nativeEvent) ||\n                      Hotkeys.isItalic(nativeEvent) ||\n                      Hotkeys.isTransposeCharacter(nativeEvent)\n                    ) {\n                      event.preventDefault()\n                      return\n                    }\n\n                    if (Hotkeys.isSoftBreak(nativeEvent)) {\n                      event.preventDefault()\n                      Editor.insertSoftBreak(editor)\n                      return\n                    }\n\n                    if (Hotkeys.isSplitBlock(nativeEvent)) {\n                      event.preventDefault()\n                      Editor.insertBreak(editor)\n                      return\n                    }\n\n                    if (Hotkeys.isDeleteBackward(nativeEvent)) {\n                      event.preventDefault()\n\n                      if (selection && Range.isExpanded(selection)) {\n                        Editor.deleteFragment(editor, { direction: 'backward' })\n                      } else {\n                        Editor.deleteBackward(editor)\n                      }\n\n                      return\n                    }\n\n                    if (Hotkeys.isDeleteForward(nativeEvent)) {\n                      event.preventDefault()\n\n                      if (selection && Range.isExpanded(selection)) {\n                        Editor.deleteFragment(editor, { direction: 'forward' })\n                      } else {\n                        Editor.deleteForward(editor)\n                      }\n\n                      return\n                    }\n\n                    if (Hotkeys.isDeleteLineBackward(nativeEvent)) {\n                      event.preventDefault()\n\n                      if (selection && Range.isExpanded(selection)) {\n                        Editor.deleteFragment(editor, { direction: 'backward' })\n                      } else {\n                        Editor.deleteBackward(editor, { unit: 'line' })\n                      }\n\n                      return\n                    }\n\n                    if (Hotkeys.isDeleteLineForward(nativeEvent)) {\n                      event.preventDefault()\n\n                      if (selection && Range.isExpanded(selection)) {\n                        Editor.deleteFragment(editor, { direction: 'forward' })\n                      } else {\n                        Editor.deleteForward(editor, { unit: 'line' })\n                      }\n\n                      return\n                    }\n\n                    if (Hotkeys.isDeleteWordBackward(nativeEvent)) {\n                      event.preventDefault()\n\n                      if (selection && Range.isExpanded(selection)) {\n                        Editor.deleteFragment(editor, { direction: 'backward' })\n                      } else {\n                        Editor.deleteBackward(editor, { unit: 'word' })\n                      }\n\n                      return\n                    }\n\n                    if (Hotkeys.isDeleteWordForward(nativeEvent)) {\n                      event.preventDefault()\n\n                      if (selection && Range.isExpanded(selection)) {\n                        Editor.deleteFragment(editor, { direction: 'forward' })\n                      } else {\n                        Editor.deleteForward(editor, { unit: 'word' })\n                      }\n\n                      return\n                    }\n                  } else {\n                    if (IS_CHROME || IS_WEBKIT) {\n                      // COMPAT: Chrome and Safari support `beforeinput` event but do not fire\n                      // an event when deleting backwards in a selected void inline node\n                      if (\n                        selection &&\n                        (Hotkeys.isDeleteBackward(nativeEvent) ||\n                          Hotkeys.isDeleteForward(nativeEvent)) &&\n                        Range.isCollapsed(selection)\n                      ) {\n                        const currentNode = Node.parent(\n                          editor,\n                          selection.anchor.path\n                        )\n\n                        if (\n                          Element.isElement(currentNode) &&\n                          Editor.isVoid(editor, currentNode) &&\n                          (Editor.isInline(editor, currentNode) ||\n                            Editor.isBlock(editor, currentNode))\n                        ) {\n                          event.preventDefault()\n                          Editor.deleteBackward(editor, { unit: 'block' })\n\n                          return\n                        }\n                      }\n                    }\n                  }\n                }\n              },\n              [readOnly, editor, attributes.onKeyDown]\n            )}\n            onPaste={useCallback(\n              (event: React.ClipboardEvent<HTMLDivElement>) => {\n                if (\n                  !readOnly &&\n                  ReactEditor.hasEditableTarget(editor, event.target) &&\n                  !isEventHandled(event, attributes.onPaste)\n                ) {\n                  // COMPAT: Certain browsers don't support the `beforeinput` event, so we\n                  // fall back to React's `onPaste` here instead.\n                  // COMPAT: Firefox, Chrome and Safari don't emit `beforeinput` events\n                  // when \"paste without formatting\" is used, so fallback. (2020/02/20)\n                  // COMPAT: Safari InputEvents generated by pasting won't include\n                  // application/x-slate-fragment items, so use the\n                  // ClipboardEvent here. (2023/03/15)\n                  if (\n                    !HAS_BEFORE_INPUT_SUPPORT ||\n                    isPlainTextOnlyPaste(event.nativeEvent) ||\n                    IS_WEBKIT\n                  ) {\n                    event.preventDefault()\n                    ReactEditor.insertData(editor, event.clipboardData)\n                  }\n                }\n              },\n              [readOnly, editor, attributes.onPaste]\n            )}\n          >\n            <Children\n              decorations={decorations}\n              node={editor}\n              renderElement={renderElement}\n              renderPlaceholder={renderPlaceholder}\n              renderLeaf={renderLeaf}\n              selection={editor.selection}\n            />\n          </Component>\n        </RestoreDOM>\n      </DecorateContext.Provider>\n    </ReadOnlyContext.Provider>\n  )\n}\n\n/**\n * The props that get passed to renderPlaceholder\n */\nexport type RenderPlaceholderProps = {\n  children: any\n  attributes: {\n    'data-slate-placeholder': boolean\n    dir?: 'rtl'\n    contentEditable: boolean\n    ref: React.RefCallback<any>\n    style: React.CSSProperties\n  }\n}\n\n/**\n * The default placeholder element\n */\n\nexport const DefaultPlaceholder = ({\n  attributes,\n  children,\n}: RenderPlaceholderProps) => (\n  // COMPAT: Artificially add a line-break to the end on the placeholder element\n  // to prevent Android IMEs to pick up its content in autocorrect and to auto-capitalize the first letter\n  <span {...attributes}>\n    {children}\n    {IS_ANDROID && <br />}\n  </span>\n)\n\n/**\n * A default memoized decorate function.\n */\n\nexport const defaultDecorate: (entry: NodeEntry) => Range[] = () => []\n\n/**\n * A default implement to scroll dom range into view.\n */\n\nconst defaultScrollSelectionIntoView = (\n  editor: ReactEditor,\n  domRange: DOMRange\n) => {\n  // This was affecting the selection of multiple blocks and dragging behavior,\n  // so enabled only if the selection has been collapsed.\n  if (\n    domRange.getBoundingClientRect &&\n    (!editor.selection ||\n      (editor.selection && Range.isCollapsed(editor.selection)))\n  ) {\n    const leafEl = domRange.startContainer.parentElement!\n    leafEl.getBoundingClientRect = domRange.getBoundingClientRect.bind(domRange)\n    scrollIntoView(leafEl, {\n      scrollMode: 'if-needed',\n    })\n\n    // @ts-expect-error an unorthodox delete D:\n    delete leafEl.getBoundingClientRect\n  }\n}\n\n/**\n * Check if an event is overrided by a handler.\n */\n\nexport const isEventHandled = <\n  EventType extends React.SyntheticEvent<unknown, unknown>\n>(\n  event: EventType,\n  handler?: (event: EventType) => void | boolean\n) => {\n  if (!handler) {\n    return false\n  }\n  // The custom event handler may return a boolean to specify whether the event\n  // shall be treated as being handled or not.\n  const shouldTreatEventAsHandled = handler(event)\n\n  if (shouldTreatEventAsHandled != null) {\n    return shouldTreatEventAsHandled\n  }\n\n  return event.isDefaultPrevented() || event.isPropagationStopped()\n}\n\n/**\n * Check if the event's target is an input element\n */\nexport const isDOMEventTargetInput = <\n  EventType extends React.SyntheticEvent<unknown, unknown>\n>(\n  event: EventType\n) => {\n  return (\n    isDOMNode(event.target) &&\n    (event.target instanceof HTMLInputElement ||\n      event.target instanceof HTMLTextAreaElement)\n  )\n}\n\n/**\n * Check if a DOM event is overrided by a handler.\n */\n\nexport const isDOMEventHandled = <E extends Event>(\n  event: E,\n  handler?: (event: E) => void | boolean\n) => {\n  if (!handler) {\n    return false\n  }\n\n  // The custom event handler may return a boolean to specify whether the event\n  // shall be treated as being handled or not.\n  const shouldTreatEventAsHandled = handler(event)\n\n  if (shouldTreatEventAsHandled != null) {\n    return shouldTreatEventAsHandled\n  }\n\n  return event.defaultPrevented\n}\n", "import { createContext, useContext } from 'react'\n\n/**\n * A React context for sharing the `focused` state of the editor.\n */\n\nexport const FocusedContext = createContext(false)\n\n/**\n * Get the current `focused` state of the editor.\n */\n\nexport const useFocused = (): boolean => {\n  return useContext(FocusedContext)\n}\n", "import {\n  createContext,\n  useCallback,\n  useContext,\n  useMemo,\n  useReducer,\n  useRef,\n} from 'react'\nimport { Editor } from 'slate'\nimport { useIsomorphicLayoutEffect } from './use-isomorphic-layout-effect'\n\nfunction isError(error: any): error is Error {\n  return error instanceof Error\n}\n\ntype EditorChangeHandler = (editor: Editor) => void\n/**\n * A React context for sharing the editor selector context in a way to control rerenders\n */\n\nexport const SlateSelectorContext = createContext<{\n  getSlate: () => Editor\n  addEventListener: (callback: EditorChangeHandler) => () => void\n}>({} as any)\n\nconst refEquality = (a: any, b: any) => a === b\n\n/**\n * use redux style selectors to prevent rerendering on every keystroke.\n * Bear in mind rerendering can only prevented if the returned value is a value type or for reference types (e.g. objects and arrays) add a custom equality function.\n *\n * Example:\n * ```\n *  const isSelectionActive = useSlateSelector(editor => Bo<PERSON>an(editor.selection));\n * ```\n */\nexport function useSlateSelector<T>(\n  selector: (editor: Editor) => T,\n  equalityFn: (a: T, b: T) => boolean = refEquality\n) {\n  const [, forceRender] = useReducer(s => s + 1, 0)\n  const context = useContext(SlateSelectorContext)\n  if (!context) {\n    throw new Error(\n      `The \\`useSlateSelector\\` hook must be used inside the <Slate> component's context.`\n    )\n  }\n  const { getSlate, addEventListener } = context\n\n  const latestSubscriptionCallbackError = useRef<Error | undefined>()\n  const latestSelector = useRef<(editor: Editor) => T>(() => null as any)\n  const latestSelectedState = useRef<T>((null as any) as T)\n  let selectedState: T\n\n  try {\n    if (\n      selector !== latestSelector.current ||\n      latestSubscriptionCallbackError.current\n    ) {\n      selectedState = selector(getSlate())\n    } else {\n      selectedState = latestSelectedState.current\n    }\n  } catch (err) {\n    if (latestSubscriptionCallbackError.current && isError(err)) {\n      err.message += `\\nThe error may be correlated with this previous error:\\n${latestSubscriptionCallbackError.current.stack}\\n\\n`\n    }\n\n    throw err\n  }\n  useIsomorphicLayoutEffect(() => {\n    latestSelector.current = selector\n    latestSelectedState.current = selectedState\n    latestSubscriptionCallbackError.current = undefined\n  })\n\n  useIsomorphicLayoutEffect(\n    () => {\n      function checkForUpdates() {\n        try {\n          const newSelectedState = latestSelector.current(getSlate())\n\n          if (equalityFn(newSelectedState, latestSelectedState.current)) {\n            return\n          }\n\n          latestSelectedState.current = newSelectedState\n        } catch (err) {\n          // we ignore all errors here, since when the component\n          // is re-rendered, the selectors are called again, and\n          // will throw again, if neither props nor store state\n          // changed\n          latestSubscriptionCallbackError.current = err\n        }\n\n        forceRender()\n      }\n\n      const unsubscribe = addEventListener(checkForUpdates)\n\n      checkForUpdates()\n\n      return () => unsubscribe()\n    },\n    // don't rerender on equalityFn change since we want to be able to define it inline\n    [addEventListener, getSlate]\n  )\n\n  return selectedState\n}\n\n/**\n * Create selector context with editor updating on every editor change\n */\nexport function useSelectorContext(editor: Editor) {\n  const eventListeners = useRef<EditorChangeHandler[]>([]).current\n  const slateRef = useRef<{\n    editor: Editor\n  }>({\n    editor,\n  }).current\n  const onChange = useCallback(\n    (editor: Editor) => {\n      slateRef.editor = editor\n      eventListeners.forEach((listener: EditorChangeHandler) =>\n        listener(editor)\n      )\n    },\n    [eventListeners, slateRef]\n  )\n\n  const selectorContext = useMemo(() => {\n    return {\n      getSlate: () => slateRef.editor,\n      addEventListener: (callback: EditorChangeHandler) => {\n        eventListeners.push(callback)\n        return () => {\n          eventListeners.splice(eventListeners.indexOf(callback), 1)\n        }\n      },\n    }\n  }, [eventListeners, slateRef])\n  return { selectorContext, onChange }\n}\n", "import React, { useCallback, useEffect, useState } from 'react'\nimport { <PERSON><PERSON><PERSON>, <PERSON>, No<PERSON>, <PERSON>ru<PERSON><PERSON> } from 'slate'\nimport { FocusedContext } from '../hooks/use-focused'\nimport { useIsomorphicLayoutEffect } from '../hooks/use-isomorphic-layout-effect'\nimport { SlateContext, SlateContextValue } from '../hooks/use-slate'\nimport {\n  useSelectorContext,\n  SlateSelectorContext,\n} from '../hooks/use-slate-selector'\nimport { EditorContext } from '../hooks/use-slate-static'\nimport { ReactEditor } from '../plugin/react-editor'\nimport { REACT_MAJOR_VERSION } from '../utils/environment'\nimport { EDITOR_TO_ON_CHANGE } from '../utils/weak-maps'\n\n/**\n * A wrapper around the provider to handle `onChange` events, because the editor\n * is a mutable singleton so it won't ever register as \"changed\" otherwise.\n */\n\nexport const Slate = (props: {\n  editor: ReactEditor\n  initialValue: Descendant[]\n  children: React.ReactNode\n  onChange?: (value: Descendant[]) => void\n}) => {\n  const { editor, children, onChange, initialValue, ...rest } = props\n\n  const [context, setContext] = React.useState<SlateContextValue>(() => {\n    if (!Node.isNodeList(initialValue)) {\n      throw new Error(\n        `[Slate] initialValue is invalid! Expected a list of elements but got: ${Scrubber.stringify(\n          initialValue\n        )}`\n      )\n    }\n    if (!Editor.isEditor(editor)) {\n      throw new Error(\n        `[Slate] editor is invalid! You passed: ${Scrubber.stringify(editor)}`\n      )\n    }\n    editor.children = initialValue\n    Object.assign(editor, rest)\n    return { v: 0, editor }\n  })\n\n  const {\n    selectorContext,\n    onChange: handleSelectorChange,\n  } = useSelectorContext(editor)\n\n  const onContextChange = useCallback(() => {\n    if (onChange) {\n      onChange(editor.children)\n    }\n\n    setContext(prevContext => ({\n      v: prevContext.v + 1,\n      editor,\n    }))\n    handleSelectorChange(editor)\n  }, [editor, handleSelectorChange, onChange])\n\n  useEffect(() => {\n    EDITOR_TO_ON_CHANGE.set(editor, onContextChange)\n\n    return () => {\n      EDITOR_TO_ON_CHANGE.set(editor, () => {})\n    }\n  }, [editor, onContextChange])\n\n  const [isFocused, setIsFocused] = useState(ReactEditor.isFocused(editor))\n\n  useEffect(() => {\n    setIsFocused(ReactEditor.isFocused(editor))\n  }, [editor])\n\n  useIsomorphicLayoutEffect(() => {\n    const fn = () => setIsFocused(ReactEditor.isFocused(editor))\n    if (REACT_MAJOR_VERSION >= 17) {\n      // In React >= 17 onFocus and onBlur listen to the focusin and focusout events during the bubbling phase.\n      // Therefore in order for <Editable />'s handlers to run first, which is necessary for ReactEditor.isFocused(editor)\n      // to return the correct value, we have to listen to the focusin and focusout events without useCapture here.\n      document.addEventListener('focusin', fn)\n      document.addEventListener('focusout', fn)\n      return () => {\n        document.removeEventListener('focusin', fn)\n        document.removeEventListener('focusout', fn)\n      }\n    } else {\n      document.addEventListener('focus', fn, true)\n      document.addEventListener('blur', fn, true)\n      return () => {\n        document.removeEventListener('focus', fn, true)\n        document.removeEventListener('blur', fn, true)\n      }\n    }\n  }, [])\n\n  return (\n    <SlateSelectorContext.Provider value={selectorContext}>\n      <SlateContext.Provider value={context}>\n        <EditorContext.Provider value={context.editor}>\n          <FocusedContext.Provider value={isFocused}>\n            {children}\n          </FocusedContext.Provider>\n        </EditorContext.Provider>\n      </SlateContext.Provider>\n    </SlateSelectorContext.Provider>\n  )\n}\n", "import { useContext } from 'react'\n\nimport { EditorContext } from './use-slate-static'\n\n/**\n * Get the current editor object from the React context.\n * @deprecated Use useSlateStatic instead.\n */\n\nexport const useEditor = () => {\n  const editor = useContext(EditorContext)\n\n  if (!editor) {\n    throw new Error(\n      `The \\`useEditor\\` hook must be used inside the <Slate> component's context.`\n    )\n  }\n\n  return editor\n}\n", "import { BaseSelection, Range } from 'slate'\n\nimport { useSlateSelector } from './use-slate-selector'\n\n/**\n * Get the current slate selection.\n * Only triggers a rerender when the selection actually changes\n */\nexport const useSlateSelection = () => {\n  return useSlateSelector(editor => editor.selection, isSelectionEqual)\n}\n\nconst isSelectionEqual = (a: BaseSelection, b: BaseSelection) => {\n  if (!a && !b) return true\n  if (!a || !b) return false\n  return Range.equals(a, b)\n}\n", "/**\n * Utilities for single-line deletion\n */\n\nimport { Editor, Range } from 'slate'\nimport { ReactEditor } from '../plugin/react-editor'\n\nconst doRectsIntersect = (rect: DOMRect, compareRect: DOMRect) => {\n  const middle = (compareRect.top + compareRect.bottom) / 2\n\n  return rect.top <= middle && rect.bottom >= middle\n}\n\nconst areRangesSameLine = (\n  editor: ReactEditor,\n  range1: Range,\n  range2: Range\n) => {\n  const rect1 = ReactEditor.toDOMRange(editor, range1).getBoundingClientRect()\n  const rect2 = ReactEditor.toDOMRange(editor, range2).getBoundingClientRect()\n\n  return doRectsIntersect(rect1, rect2) && doRectsIntersect(rect2, rect1)\n}\n\n/**\n * A helper utility that returns the end portion of a `Range`\n * which is located on a single line.\n *\n * @param {Editor} editor The editor object to compare against\n * @param {Range} parentRange The parent range to compare against\n * @returns {Range} A valid portion of the parentRange which is one a single line\n */\nexport const findCurrentLineRange = (\n  editor: ReactEditor,\n  parentRange: Range\n): Range => {\n  const parentRangeBoundary = Editor.range(editor, Range.end(parentRange))\n  const positions = Array.from(Editor.positions(editor, { at: parentRange }))\n\n  let left = 0\n  let right = positions.length\n  let middle = Math.floor(right / 2)\n\n  if (\n    areRangesSameLine(\n      editor,\n      Editor.range(editor, positions[left]),\n      parentRangeBoundary\n    )\n  ) {\n    return Editor.range(editor, positions[left], parentRangeBoundary)\n  }\n\n  if (positions.length < 2) {\n    return Editor.range(\n      editor,\n      positions[positions.length - 1],\n      parentRangeBoundary\n    )\n  }\n\n  while (middle !== positions.length && middle !== left) {\n    if (\n      areRangesSameLine(\n        editor,\n        Editor.range(editor, positions[middle]),\n        parentRangeBoundary\n      )\n    ) {\n      right = middle\n    } else {\n      left = middle\n    }\n\n    middle = Math.floor((left + right) / 2)\n  }\n\n  return Editor.range(editor, positions[right], parentRangeBoundary)\n}\n", "import ReactDOM from 'react-dom'\nimport {\n  BaseE<PERSON>or,\n  Editor,\n  Element,\n  Node,\n  Operation,\n  Path,\n  Point,\n  Range,\n  Transforms,\n} from 'slate'\nimport {\n  TextDiff,\n  transformPendingPoint,\n  transformPendingRange,\n  transformTextDiff,\n} from '../utils/diff-text'\nimport {\n  getPlainText,\n  getSlateFragmentAttribute,\n  isDOMText,\n} from '../utils/dom'\nimport { Key } from '../utils/key'\nimport { findCurrentLineRange } from '../utils/lines'\nimport {\n  EDITOR_TO_KEY_TO_ELEMENT,\n  EDITOR_TO_ON_CHANGE,\n  EDITOR_TO_PENDING_ACTION,\n  EDITOR_TO_PENDING_DIFFS,\n  EDITOR_TO_PENDING_INSERTION_MARKS,\n  EDITOR_TO_PENDING_SELECTION,\n  EDITOR_TO_SCHEDULE_FLUSH,\n  EDITOR_TO_USER_MARKS,\n  EDITOR_TO_USER_SELECTION,\n  NODE_TO_KEY,\n} from '../utils/weak-maps'\nimport { ReactEditor } from './react-editor'\nimport { REACT_MAJOR_VERSION } from '../utils/environment'\n\n/**\n * `withReact` adds React and DOM specific behaviors to the editor.\n *\n * If you are using TypeScript, you must extend Slate's CustomTypes to use\n * this plugin.\n *\n * See https://docs.slatejs.org/concepts/11-typescript to learn how.\n */\n\nexport const withReact = <T extends BaseEditor>(\n  editor: T,\n  clipboardFormatKey = 'x-slate-fragment'\n): T & ReactEditor => {\n  const e = editor as T & ReactEditor\n  const { apply, onChange, deleteBackward, addMark, removeMark } = e\n\n  // The WeakMap which maps a key to a specific HTMLElement must be scoped to the editor instance to\n  // avoid collisions between editors in the DOM that share the same value.\n  EDITOR_TO_KEY_TO_ELEMENT.set(e, new WeakMap())\n\n  e.addMark = (key, value) => {\n    EDITOR_TO_SCHEDULE_FLUSH.get(e)?.()\n\n    if (\n      !EDITOR_TO_PENDING_INSERTION_MARKS.get(e) &&\n      EDITOR_TO_PENDING_DIFFS.get(e)?.length\n    ) {\n      // Ensure the current pending diffs originating from changes before the addMark\n      // are applied with the current formatting\n      EDITOR_TO_PENDING_INSERTION_MARKS.set(e, null)\n    }\n\n    EDITOR_TO_USER_MARKS.delete(e)\n\n    addMark(key, value)\n  }\n\n  e.removeMark = key => {\n    if (\n      !EDITOR_TO_PENDING_INSERTION_MARKS.get(e) &&\n      EDITOR_TO_PENDING_DIFFS.get(e)?.length\n    ) {\n      // Ensure the current pending diffs originating from changes before the addMark\n      // are applied with the current formatting\n      EDITOR_TO_PENDING_INSERTION_MARKS.set(e, null)\n    }\n\n    EDITOR_TO_USER_MARKS.delete(e)\n\n    removeMark(key)\n  }\n\n  e.deleteBackward = unit => {\n    if (unit !== 'line') {\n      return deleteBackward(unit)\n    }\n\n    if (e.selection && Range.isCollapsed(e.selection)) {\n      const parentBlockEntry = Editor.above(e, {\n        match: n => Element.isElement(n) && Editor.isBlock(e, n),\n        at: e.selection,\n      })\n\n      if (parentBlockEntry) {\n        const [, parentBlockPath] = parentBlockEntry\n        const parentElementRange = Editor.range(\n          e,\n          parentBlockPath,\n          e.selection.anchor\n        )\n\n        const currentLineRange = findCurrentLineRange(e, parentElementRange)\n\n        if (!Range.isCollapsed(currentLineRange)) {\n          Transforms.delete(e, { at: currentLineRange })\n        }\n      }\n    }\n  }\n\n  // This attempts to reset the NODE_TO_KEY entry to the correct value\n  // as apply() changes the object reference and hence invalidates the NODE_TO_KEY entry\n  e.apply = (op: Operation) => {\n    const matches: [Path, Key][] = []\n\n    const pendingDiffs = EDITOR_TO_PENDING_DIFFS.get(e)\n    if (pendingDiffs?.length) {\n      const transformed = pendingDiffs\n        .map(textDiff => transformTextDiff(textDiff, op))\n        .filter(Boolean) as TextDiff[]\n\n      EDITOR_TO_PENDING_DIFFS.set(e, transformed)\n    }\n\n    const pendingSelection = EDITOR_TO_PENDING_SELECTION.get(e)\n    if (pendingSelection) {\n      EDITOR_TO_PENDING_SELECTION.set(\n        e,\n        transformPendingRange(e, pendingSelection, op)\n      )\n    }\n\n    const pendingAction = EDITOR_TO_PENDING_ACTION.get(e)\n    if (pendingAction?.at) {\n      const at = Point.isPoint(pendingAction?.at)\n        ? transformPendingPoint(e, pendingAction.at, op)\n        : transformPendingRange(e, pendingAction.at, op)\n\n      EDITOR_TO_PENDING_ACTION.set(e, at ? { ...pendingAction, at } : null)\n    }\n\n    switch (op.type) {\n      case 'insert_text':\n      case 'remove_text':\n      case 'set_node':\n      case 'split_node': {\n        matches.push(...getMatches(e, op.path))\n        break\n      }\n\n      case 'set_selection': {\n        // Selection was manually set, don't restore the user selection after the change.\n        EDITOR_TO_USER_SELECTION.get(e)?.unref()\n        EDITOR_TO_USER_SELECTION.delete(e)\n        break\n      }\n\n      case 'insert_node':\n      case 'remove_node': {\n        matches.push(...getMatches(e, Path.parent(op.path)))\n        break\n      }\n\n      case 'merge_node': {\n        const prevPath = Path.previous(op.path)\n        matches.push(...getMatches(e, prevPath))\n        break\n      }\n\n      case 'move_node': {\n        const commonPath = Path.common(\n          Path.parent(op.path),\n          Path.parent(op.newPath)\n        )\n        matches.push(...getMatches(e, commonPath))\n        break\n      }\n    }\n\n    apply(op)\n\n    for (const [path, key] of matches) {\n      const [node] = Editor.node(e, path)\n      NODE_TO_KEY.set(node, key)\n    }\n  }\n\n  e.setFragmentData = (data: Pick<DataTransfer, 'getData' | 'setData'>) => {\n    const { selection } = e\n\n    if (!selection) {\n      return\n    }\n\n    const [start, end] = Range.edges(selection)\n    const startVoid = Editor.void(e, { at: start.path })\n    const endVoid = Editor.void(e, { at: end.path })\n\n    if (Range.isCollapsed(selection) && !startVoid) {\n      return\n    }\n\n    // Create a fake selection so that we can add a Base64-encoded copy of the\n    // fragment to the HTML, to decode on future pastes.\n    const domRange = ReactEditor.toDOMRange(e, selection)\n    let contents = domRange.cloneContents()\n    let attach = contents.childNodes[0] as HTMLElement\n\n    // Make sure attach is non-empty, since empty nodes will not get copied.\n    contents.childNodes.forEach(node => {\n      if (node.textContent && node.textContent.trim() !== '') {\n        attach = node as HTMLElement\n      }\n    })\n\n    // COMPAT: If the end node is a void node, we need to move the end of the\n    // range from the void node's spacer span, to the end of the void node's\n    // content, since the spacer is before void's content in the DOM.\n    if (endVoid) {\n      const [voidNode] = endVoid\n      const r = domRange.cloneRange()\n      const domNode = ReactEditor.toDOMNode(e, voidNode)\n      r.setEndAfter(domNode)\n      contents = r.cloneContents()\n    }\n\n    // COMPAT: If the start node is a void node, we need to attach the encoded\n    // fragment to the void node's content node instead of the spacer, because\n    // attaching it to empty `<div>/<span>` nodes will end up having it erased by\n    // most browsers. (2018/04/27)\n    if (startVoid) {\n      attach = contents.querySelector('[data-slate-spacer]')! as HTMLElement\n    }\n\n    // Remove any zero-width space spans from the cloned DOM so that they don't\n    // show up elsewhere when pasted.\n    Array.from(contents.querySelectorAll('[data-slate-zero-width]')).forEach(\n      zw => {\n        const isNewline = zw.getAttribute('data-slate-zero-width') === 'n'\n        zw.textContent = isNewline ? '\\n' : ''\n      }\n    )\n\n    // Set a `data-slate-fragment` attribute on a non-empty node, so it shows up\n    // in the HTML, and can be used for intra-Slate pasting. If it's a text\n    // node, wrap it in a `<span>` so we have something to set an attribute on.\n    if (isDOMText(attach)) {\n      const span = attach.ownerDocument.createElement('span')\n      // COMPAT: In Chrome and Safari, if we don't add the `white-space` style\n      // then leading and trailing spaces will be ignored. (2017/09/21)\n      span.style.whiteSpace = 'pre'\n      span.appendChild(attach)\n      contents.appendChild(span)\n      attach = span\n    }\n\n    const fragment = e.getFragment()\n    const string = JSON.stringify(fragment)\n    const encoded = window.btoa(encodeURIComponent(string))\n    attach.setAttribute('data-slate-fragment', encoded)\n    data.setData(`application/${clipboardFormatKey}`, encoded)\n\n    // Add the content to a <div> so that we can get its inner HTML.\n    const div = contents.ownerDocument.createElement('div')\n    div.appendChild(contents)\n    div.setAttribute('hidden', 'true')\n    contents.ownerDocument.body.appendChild(div)\n    data.setData('text/html', div.innerHTML)\n    data.setData('text/plain', getPlainText(div))\n    contents.ownerDocument.body.removeChild(div)\n    return data\n  }\n\n  e.insertData = (data: DataTransfer) => {\n    if (!e.insertFragmentData(data)) {\n      e.insertTextData(data)\n    }\n  }\n\n  e.insertFragmentData = (data: DataTransfer): boolean => {\n    /**\n     * Checking copied fragment from application/x-slate-fragment or data-slate-fragment\n     */\n    const fragment =\n      data.getData(`application/${clipboardFormatKey}`) ||\n      getSlateFragmentAttribute(data)\n\n    if (fragment) {\n      const decoded = decodeURIComponent(window.atob(fragment))\n      const parsed = JSON.parse(decoded) as Node[]\n      e.insertFragment(parsed)\n      return true\n    }\n    return false\n  }\n\n  e.insertTextData = (data: DataTransfer): boolean => {\n    const text = data.getData('text/plain')\n\n    if (text) {\n      const lines = text.split(/\\r\\n|\\r|\\n/)\n      let split = false\n\n      for (const line of lines) {\n        if (split) {\n          Transforms.splitNodes(e, { always: true })\n        }\n\n        e.insertText(line)\n        split = true\n      }\n      return true\n    }\n    return false\n  }\n\n  e.onChange = options => {\n    // COMPAT: React < 18 doesn't batch `setState` hook calls, which means\n    // that the children and selection can get out of sync for one render\n    // pass. So we have to use this unstable API to ensure it batches them.\n    // (2019/12/03)\n    // https://github.com/facebook/react/issues/14259#issuecomment-439702367\n    const maybeBatchUpdates =\n      REACT_MAJOR_VERSION < 18\n        ? ReactDOM.unstable_batchedUpdates\n        : (callback: () => void) => callback()\n\n    maybeBatchUpdates(() => {\n      const onContextChange = EDITOR_TO_ON_CHANGE.get(e)\n\n      if (onContextChange) {\n        onContextChange()\n      }\n\n      onChange(options)\n    })\n  }\n\n  return e\n}\n\nconst getMatches = (e: Editor, path: Path) => {\n  const matches: [Path, Key][] = []\n  for (const [n, p] of Editor.levels(e, { at: path })) {\n    const key = ReactEditor.findKey(e, n)\n    matches.push([p, key])\n  }\n  return matches\n}\n"], "names": ["EditorContext", "createContext", "useSlateStatic", "editor", "useContext", "Error", "REACT_MAJOR_VERSION", "parseInt", "React", "version", "split", "IS_IOS", "navigator", "window", "test", "userAgent", "MSStream", "IS_APPLE", "IS_ANDROID", "IS_FIREFOX", "IS_WEBKIT", "IS_EDGE_LEGACY", "IS_CHROME", "IS_CHROME_LEGACY", "IS_ANDROID_CHROME_LEGACY", "IS_FIREFOX_LEGACY", "IS_UC_MOBILE", "IS_WECHATBROWSER", "CAN_USE_DOM", "document", "createElement", "HAS_BEFORE_INPUT_SUPPORT", "globalThis", "InputEvent", "prototype", "getTargetRanges", "NODE_TO_INDEX", "WeakMap", "NODE_TO_PARENT", "EDITOR_TO_WINDOW", "EDITOR_TO_ELEMENT", "EDITOR_TO_PLACEHOLDER_ELEMENT", "ELEMENT_TO_NODE", "NODE_TO_ELEMENT", "NODE_TO_KEY", "EDITOR_TO_KEY_TO_ELEMENT", "IS_READ_ONLY", "IS_FOCUSED", "IS_COMPOSING", "EDITOR_TO_USER_SELECTION", "EDITOR_TO_ON_CHANGE", "EDITOR_TO_SCHEDULE_FLUSH", "EDITOR_TO_PENDING_INSERTION_MARKS", "EDITOR_TO_USER_MARKS", "EDITOR_TO_PENDING_DIFFS", "EDITOR_TO_PENDING_ACTION", "EDITOR_TO_PENDING_SELECTION", "EDITOR_TO_FORCE_RENDER", "PLACEHOLDER_SYMBOL", "Symbol", "MARK_PLACEHOLDER_SYMBOL", "DOMText", "Text", "getDefaultView", "value", "ownerDocument", "defaultView", "isDOMComment", "isDOMNode", "nodeType", "isDOMElement", "Node", "isDOMSelection", "anchorNode", "Selection", "isDOMText", "isPlainTextOnlyPaste", "event", "clipboardData", "getData", "types", "length", "normalizeDOMPoint", "domPoint", "node", "offset", "childNodes", "isLast", "index", "getEditableChildAndIndex", "i", "getEditable<PERSON><PERSON><PERSON>", "textContent", "hasShadowRoot", "parent", "parentNode", "toString", "direction", "child", "triedForward", "<PERSON><PERSON><PERSON><PERSON>", "getAttribute", "getPlainText", "domNode", "text", "nodeValue", "Array", "from", "childNode", "display", "getComputedStyle", "getPropertyValue", "tagName", "catchSlateFragment", "getSlateFragmentAttribute", "dataTransfer", "htmlData", "match", "fragment", "isTrackedMutation", "mutation", "batch", "target", "matches", "ReactEditor", "getWindow", "contains", "hasDOMNode", "editable", "parentMutation", "find", "addedNodes", "removedNodes", "n", "Key", "id", "androidPendingDiffs", "get", "androidScheduleFlush", "blur", "el", "toDOMNode", "root", "findDocumentOrShadowRoot", "set", "activeElement", "deselect", "selection", "domSelection", "getSelection", "rangeCount", "removeAllRanges", "Transforms", "getRootNode", "Document", "ShadowRoot", "findEventRange", "nativeEvent", "x", "clientX", "y", "clientY", "toSlateNode", "path", "<PERSON><PERSON><PERSON>", "Element", "isElement", "Editor", "isVoid", "rect", "getBoundingClientRect", "isPrev", "isInline", "left", "width", "top", "height", "edge", "point", "before", "after", "range", "dom<PERSON><PERSON><PERSON>", "caretRangeFromPoint", "position", "caretPositionFromPoint", "createRange", "setStart", "offsetNode", "setEnd", "to<PERSON>late<PERSON><PERSON><PERSON>", "exactMatch", "suppressThrow", "<PERSON><PERSON><PERSON>", "key", "isEditor", "unshift", "<PERSON><PERSON><PERSON><PERSON>", "stringify", "focus", "preventScroll", "options", "editor<PERSON><PERSON>", "targetEl", "parentElement", "err", "message", "includes", "closest", "isContentEditable", "hasEditableTarget", "has<PERSON><PERSON><PERSON>", "anchor", "<PERSON><PERSON><PERSON>", "hasSelectableTarget", "isTargetInsideNonReadonlyVoid", "<PERSON><PERSON><PERSON><PERSON>", "insertData", "data", "insertFragmentData", "insertTextData", "isComposing", "isFocused", "isReadOnly", "slateNode", "setFragmentData", "originEvent", "KEY_TO_ELEMENT", "toDOMPoint", "at", "selector", "texts", "querySelectorAll", "start", "attr", "<PERSON><PERSON><PERSON><PERSON>", "end", "nextText", "hasAttribute", "domText", "startsWith", "Math", "min", "max", "toDOMRange", "isBackward", "Range", "dom<PERSON><PERSON><PERSON>", "domFocus", "isCollapsed", "startNode", "startOffset", "endNode", "endOffset", "startEl", "isStartAtZeroWidth", "endEl", "isEndAtZeroWidth", "domEl", "toSlatePoint", "nearestNode", "nearestOffset", "textNode", "potentialVoidNode", "voidNode", "leafNode", "contents", "cloneContents", "removals", "slice", "call", "for<PERSON>ach", "textContext", "<PERSON><PERSON><PERSON><PERSON>", "leafNodes", "current", "endsWith", "querySelector", "startContainer", "anchorOffset", "focusNode", "focusOffset", "firstRange", "getRangeAt", "<PERSON><PERSON><PERSON><PERSON>", "HTMLTableRowElement", "getLastChildren", "element", "childElementCount", "children", "firstNodeRow", "lastNodeRow", "firstNode", "lastNode", "HTMLElement", "innerHTML", "endContainer", "collapsed", "isExpanded", "isForward", "mode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "voids", "verifyDiffState", "textDiff", "diff", "isText", "nextPath", "Path", "next", "nextNode", "applyStringDiff", "diffs", "reduce", "longestCommonPrefixLength", "str", "another", "char<PERSON>t", "longestCommonSuffixLength", "normalizeStringDiff", "targetText", "removedText", "prefixLength", "suffixLength", "normalized", "mergeStringDiffs", "a", "b", "overlap", "applied", "sliceEnd", "targetRange", "normalizePoint", "leaf", "parentBlock", "above", "isBlock", "entry", "isDescendant", "normalizeRange", "transformPendingPoint", "op", "pendingDiffs", "equals", "Point", "transform", "affinity", "transformed", "type", "transformPendingRange", "transformTextDiff", "newPath", "RESOLVE_DELAY", "FLUSH_DELAY", "debug", "isDataTransfer", "constructor", "name", "createAndroidInputManager", "scheduleOnDOMSelectionChange", "onDOMSelectionChange", "flushing", "compositionEndTimeoutId", "flushTimeoutId", "actionTimeoutId", "idCounter", "insertPositionHint", "applyPendingSelection", "pendingSelection", "select", "performAction", "action", "isPoint", "run", "flush", "clearTimeout", "hasPendingDiffs", "hasPendingAction", "setTimeout", "selectionRef", "rangeRef", "marks", "scheduleSelectionChange", "pendingMarks", "undefined", "insertText", "deleteFragment", "filter", "cancel", "unref", "userMarks", "onChange", "handleCompositionEnd", "_event", "handleCompositionStart", "updatePlaceholderVisibility", "forceHide", "placeholderElement", "style", "removeProperty", "storeDiff", "idx", "findIndex", "change", "push", "merged", "splice", "scheduleAction", "handleDOMBeforeInput", "inputType", "nativeTargetRange", "canStoreDiff", "edges", "relevantPendingDiffs", "handleUserSelect", "targetNode", "deleteForward", "nativeCollapsed", "deleteBackward", "unit", "insertSoftBreak", "insertBreak", "replace", "parts", "line", "hintPosition", "search", "diffPosition", "scheduleFlush", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "isFlushing", "pathChanged", "parentPath<PERSON><PERSON>ed", "handleInput", "handleKeyDown", "_", "handleDomMutations", "mutations", "some", "useIsMounted", "isMountedRef", "useRef", "useEffect", "useIsomorphicLayoutEffect", "useLayoutEffect", "useMutationObserver", "callback", "useState", "MutationObserver", "mutationObserver", "takeRecords", "observe", "disconnect", "MUTATION_OBSERVER_CONFIG", "subtree", "childList", "characterData", "useAndroidInputManager", "isMounted", "inputManager", "shallowCompare", "obj1", "obj2", "Object", "keys", "every", "hasOwnProperty", "isDecorationFlagsEqual", "other", "rangeOwnProps", "otherOwnProps", "isElementDecorationsEqual", "list", "isTextDecorationsEqual", "String", "props", "parentPath", "isMarkPlaceholder", "ZeroWidthString", "string", "isLineBreak", "TextString", "isTrailing", "ref", "getTextContent", "initialText", "textWithTrailing", "MemoizedText", "memo", "forwardRef", "attributes", "disconnectPlaceholderResizeObserver", "placeholderResizeObserver", "releaseObserver", "clearTimeoutRef", "timeoutRef", "Leaf", "renderPlaceholder", "renderLeaf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder<PERSON><PERSON>", "showPlaceholder", "setShowPlaceholder", "showPlaceholderTimeoutRef", "callbackPlaceholderRef", "useCallback", "placeholder<PERSON><PERSON>", "onPlaceholderResize", "ResizeObserver", "ResizeObserverPolyfill", "leafIsPlaceholder", "placeholderProps", "placeholder", "pointerEvents", "max<PERSON><PERSON><PERSON>", "opacity", "userSelect", "textDecoration", "WebkitUserModify", "contentEditable", "Fragment", "MemoizedLeaf", "prev", "decorations", "leaves", "SlateText", "callback<PERSON><PERSON>", "span", "renderElement", "p", "DefaultElement", "readOnly", "useReadOnly", "useChildren", "hasInlines", "dir", "getDirection", "Tag", "color", "outline", "MemoizedElement", "DecorateContext", "useDecorate", "SelectedContext", "useSelected", "decorate", "isLeafBlock", "concat", "sel", "intersection", "ds", "dec", "d", "Provider", "ElementComponent", "TextComponent", "ReadOnlyContext", "SlateContext", "useSlate", "context", "useSlateWithV", "useTrackUserInput", "receivedUserInput", "animationFrameIdRef", "onUserInput", "cancelAnimationFrame", "requestAnimationFrame", "TRIPLE_CLICK", "HOTKEYS", "bold", "compose", "moveBackward", "moveForward", "moveWordBackward", "moveWordForward", "extendBackward", "extendForward", "italic", "splitBlock", "undo", "APPLE_HOTKEYS", "moveLineBackward", "moveLineForward", "deleteLineBackward", "deleteLineForward", "deleteWordBackward", "deleteWordForward", "extendLineBackward", "extendLineForward", "redo", "transposeCharacter", "WINDOWS_HOTKEYS", "create", "generic", "apple", "windows", "isGeneric", "isHotkey", "isApple", "isWindows", "isBold", "isCompose", "isMoveBackward", "isMoveForward", "isDeleteBackward", "isDeleteForward", "isDeleteLineBackward", "isDeleteLineForward", "isDeleteWordBackward", "isDeleteWordForward", "isExtendBackward", "isExtendForward", "isExtendLineBackward", "isExtendLineForward", "isItalic", "isMoveLineBackward", "isMoveLineForward", "isMoveWordBackward", "isMoveWordForward", "isRedo", "isSoftBreak", "isSplitBlock", "isTransposeCharacter", "isUndo", "require$$0", "createRestoreDomManager", "bufferedMutations", "clear", "registerMutations", "trackedMutations", "restoreDOM", "reverse", "insertBefore", "nextS<PERSON>ling", "characterDataOldValue", "RestoreDOMComponent", "manager", "pendingMutations", "Component", "RestoreDOM", "Children", "Editable", "defaultRenderPlaceholder", "DefaultPlaceholder", "autoFocus", "defaultDecorate", "propsOnDOMBeforeInput", "onDOMBeforeInput", "scrollSelectionIntoView", "defaultScrollSelectionIntoView", "userStyle", "as", "disableDefaultStyles", "setIsComposing", "deferredOperations", "placeholder<PERSON><PERSON><PERSON>", "setPlaceholderHeight", "useReducer", "s", "forceRender", "state", "useMemo", "isDraggingInternally", "isUpdatingSelection", "latestElement", "hasMarkPlaceholder", "androidInputManagerRef", "throttle", "androidInputManager", "anchorNodeSelectable", "focusNodeSelectable", "debounce", "setDomSelection", "forceChange", "hasDomSelection", "editor<PERSON><PERSON>", "hasDomSelectionInEditor", "slateRange", "newDomRange", "collapseToEnd", "setBaseAndExtent", "ensureSelection", "timeoutId", "animationFrameId", "ensureDomSelection", "e", "isDOMEventHandled", "isCompositionChange", "native", "lastText", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_TEXT", "<PERSON><PERSON><PERSON><PERSON>", "whiteSpace", "block", "preventDefault", "toRestore", "removeEventListener", "addEventListener", "placeHolderResizeHandler", "rest", "loose", "unset", "fromEntries", "map", "mark", "role", "spell<PERSON>heck", "autoCorrect", "autoCapitalize", "zindex", "suppressContentEditableWarning", "wordWrap", "minHeight", "onBeforeInput", "isEventHandled", "onInput", "onBlur", "relatedTarget", "onClick", "detail", "blockPath", "startVoid", "endVoid", "onCompositionEnd", "placeholderMarks", "onCompositionUpdate", "onCompositionStart", "inline", "inlinePath", "isEnd", "setSelection", "onCopy", "isDOMEventTargetInput", "onCut", "onDragOver", "onDragStart", "voidMatch", "onDrop", "<PERSON><PERSON><PERSON><PERSON>", "onDragEnd", "onFocus", "onKeyDown", "isRTL", "Hotkeys", "maybeHistoryEditor", "move", "collapse", "currentNode", "onPaste", "leafEl", "bind", "scrollIntoView", "scrollMode", "handler", "shouldTreatEventAsHandled", "isDefaultPrevented", "isPropagationStopped", "HTMLInputElement", "HTMLTextAreaElement", "defaultPrevented", "FocusedContext", "useFocused", "isError", "error", "SlateSelectorContext", "refEquality", "useSlateSelector", "equalityFn", "getSlate", "latestSubscriptionCallbackError", "latestSelector", "latestSelectedState", "selectedState", "stack", "checkForUpdates", "newSelectedState", "unsubscribe", "useSelectorContext", "eventListeners", "slateRef", "listener", "selectorContext", "indexOf", "Slate", "initialValue", "isNodeList", "assign", "v", "setContext", "handleSelectorChange", "onContextChange", "prevContext", "setIsFocused", "fn", "useEditor", "useSlateSelection", "isSelectionEqual", "doRectsIntersect", "compareRect", "middle", "bottom", "areRangesSameLine", "range1", "range2", "rect1", "rect2", "findCurrentLineRange", "parentRange", "parentRangeBoundary", "positions", "right", "floor", "withReact", "clipboardFormatKey", "apply", "addMark", "removeMark", "parentBlockEntry", "parentBlockPath", "parentElementRange", "currentLineRange", "Boolean", "pendingAction", "getMatches", "prevPath", "previous", "commonPath", "common", "attach", "trim", "r", "cloneRange", "setEndAfter", "zw", "isNewline", "append<PERSON><PERSON><PERSON>", "getFragment", "JSON", "encoded", "btoa", "encodeURIComponent", "setAttribute", "setData", "div", "body", "decoded", "decodeURIComponent", "atob", "parsed", "parse", "insertFragment", "lines", "splitNodes", "always", "maybeBatchUpdates", "ReactDOM", "unstable_batchedUpdates", "levels"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;AAC1C,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE;AAClB,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;AACpC,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,UAAU,EAAE,IAAI;AACtB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,QAAQ,EAAE,IAAI;AACpB,KAAK,CAAC,CAAC;AACP,GAAG,MAAM;AACT,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACrB,GAAG;AACH;AACA,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD;AACA,cAAc,GAAG,eAAe,CAAC;AACjC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;AChB5E,SAAS,eAAe,CAAC,GAAG,EAAE;AAC9B,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC;AACrC,CAAC;AACD;AACA,cAAc,GAAG,eAAe,CAAC;AACjC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACL5E,SAAS,qBAAqB,CAAC,GAAG,EAAE,CAAC,EAAE;AACvC,EAAE,IAAI,EAAE,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAO,MAAM,KAAK,WAAW,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC;AAC3G;AACA,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,OAAO;AACzB,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AAChB,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;AACjB;AACA,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AACb;AACA,EAAE,IAAI;AACN,IAAI,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,EAAE;AACtE,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC1B;AACA,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM;AACxC,KAAK;AACL,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,EAAE,GAAG,IAAI,CAAC;AACd,IAAI,EAAE,GAAG,GAAG,CAAC;AACb,GAAG,SAAS;AACZ,IAAI,IAAI;AACR,MAAM,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;AACtD,KAAK,SAAS;AACd,MAAM,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC;AACvB,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD;AACA,cAAc,GAAG,qBAAqB,CAAC;AACvC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;AC/B5E,SAAS,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE;AACrC,EAAE,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;AACxD;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACvD,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD;AACA,cAAc,GAAG,iBAAiB,CAAC;AACnC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACT5E,SAAS,2BAA2B,CAAC,CAAC,EAAE,MAAM,EAAE;AAChD,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO;AACjB,EAAE,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,OAAO,gBAAgB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAChE,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACzD,EAAE,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC;AAC9D,EAAE,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvD,EAAE,IAAI,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,gBAAgB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAClH,CAAC;AACD;AACA,cAAc,GAAG,2BAA2B,CAAC;AAC7C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACZ5E,SAAS,gBAAgB,GAAG;AAC5B,EAAE,MAAM,IAAI,SAAS,CAAC,2IAA2I,CAAC,CAAC;AACnK,CAAC;AACD;AACA,cAAc,GAAG,gBAAgB,CAAC;AAClC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACG5E,SAAS,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE;AAChC,EAAE,OAAO,cAAc,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,0BAA0B,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AACxH,CAAC;AACD;AACA,cAAc,GAAG,cAAc,CAAC;AAChC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACb5E,SAAS,6BAA6B,CAAC,MAAM,EAAE,QAAQ,EAAE;AACzD,EAAE,IAAI,MAAM,IAAI,IAAI,EAAE,OAAO,EAAE,CAAC;AAChC,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AACb;AACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACxB,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS;AAC7C,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;AACA,cAAc,GAAG,6BAA6B,CAAC;AAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACd5E,SAAS,wBAAwB,CAAC,MAAM,EAAE,QAAQ,EAAE;AACpD,EAAE,IAAI,MAAM,IAAI,IAAI,EAAE,OAAO,EAAE,CAAC;AAChC,EAAE,IAAI,MAAM,GAAG,4BAA4B,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC9D,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AACb;AACA,EAAE,IAAI,MAAM,CAAC,qBAAqB,EAAE;AACpC,IAAI,IAAI,gBAAgB,GAAG,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;AAChE;AACA,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,MAAM,GAAG,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS;AAC/C,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,SAAS;AAC7E,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAChC,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;AACA,cAAc,GAAG,wBAAwB,CAAC;AAC1C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;AClB5E;;;;AAIO,IAAMA,aAAa,gBAAGC,mBAAa,CAAqB,IAArB,CAAnC;AAEP;;;;IAIaC,cAAc,GAAG,SAAjBA,cAAiB;AAC5B,MAAMC,MAAM,GAAGC,gBAAU,CAACJ,aAAD,CAAzB;;AAEA,MAAI,CAACG,MAAL,EAAa;AACX,UAAM,IAAIE,KAAJ,kFAAN;AAGD;;AAED,SAAOF,MAAP;AACD;;ACtBM,IAAMG,mBAAmB,GAAGC,QAAQ,CAACC,yBAAK,CAACC,OAAN,CAAcC,KAAd,CAAoB,GAApB,EAAyB,CAAzB,CAAD,EAA8B,EAA9B,CAApC;AAEA,IAAMC,MAAM,GACjB,OAAOC,SAAP,KAAqB,WAArB,IACA,OAAOC,MAAP,KAAkB,WADlB,IAEA,mBAAmBC,IAAnB,CAAwBF,SAAS,CAACG,SAAlC,CAFA,IAGA,CAACF,MAAM,CAACG,QAJH;AAMA,IAAMC,QAAQ,GACnB,OAAOL,SAAP,KAAqB,WAArB,IAAoC,WAAWE,IAAX,CAAgBF,SAAS,CAACG,SAA1B,CAD/B;AAGA,IAAMG,UAAU,GACrB,OAAON,SAAP,KAAqB,WAArB,IAAoC,UAAUE,IAAV,CAAeF,SAAS,CAACG,SAAzB,CAD/B;AAGA,IAAMI,UAAU,GACrB,OAAOP,SAAP,KAAqB,WAArB,IACA,mCAAmCE,IAAnC,CAAwCF,SAAS,CAACG,SAAlD,CAFK;AAIA,IAAMK,SAAS,GACpB,OAAOR,SAAP,KAAqB,WAArB,IACA,2BAA2BE,IAA3B,CAAgCF,SAAS,CAACG,SAA1C,CAFK;;AAKA,IAAMM,cAAc,GACzB,OAAOT,SAAP,KAAqB,WAArB,IACA,0CAA0CE,IAA1C,CAA+CF,SAAS,CAACG,SAAzD,CAFK;AAIA,IAAMO,SAAS,GACpB,OAAOV,SAAP,KAAqB,WAArB,IAAoC,UAAUE,IAAV,CAAeF,SAAS,CAACG,SAAzB,CAD/B;AAIP;;AACO,IAAMQ,gBAAgB,GAC3B,OAAOX,SAAP,KAAqB,WAArB,IACA,4CAA4CE,IAA5C,CAAiDF,SAAS,CAACG,SAA3D,CAFK;AAIA,IAAMS,wBAAwB,GACnCN,UAAU,IACV,OAAON,SAAP,KAAqB,WADrB,IAEA,+BAA+BE,IAA/B,CAAoCF,SAAS,CAACG,SAA9C,CAHK;;AAMA,IAAMU,iBAAiB,GAC5B,OAAOb,SAAP,KAAqB,WAArB,IACA,oEAAoEE,IAApE,CACEF,SAAS,CAACG,SADZ,CAFK;;AAOA,IAAMW,YAAY,GACvB,OAAOd,SAAP,KAAqB,WAArB,IAAoC,cAAcE,IAAd,CAAmBF,SAAS,CAACG,SAA7B,CAD/B;;AAIA,IAAMY,gBAAgB,GAC3B,OAAOf,SAAP,KAAqB,WAArB,IACA,WAAWE,IAAX,CAAgBF,SAAS,CAACG,SAA1B,CADA,IAEA,CAAC,cAAcD,IAAd,CAAmBF,SAAS,CAACG,SAA7B,CAHI;AAKP;AACA;;AACO,IAAMa,WAAW,GAAG,CAAC,EAC1B,OAAOf,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACgB,QAAd,KAA2B,WAD3B,IAEA,OAAOhB,MAAM,CAACgB,QAAP,CAAgBC,aAAvB,KAAyC,WAHf,CAArB;AAOP;;AACO,IAAMC,wBAAwB,GACnC,CAAC,CAACR,gBAAD,IAAqB,CAACC,wBAAvB,KACA,CAACH,cADD;AAGA,OAAOW,UAAP,KAAsB,WAHtB,IAIAA,UAAU,CAACC,UAJX;AAMA,OAAOD,UAAU,CAACC,UAAX,CAAsBC,SAAtB,CAAgCC,eAAvC,KAA2D,UAPtD;;ACjEP;;;;AAKO,IAAMC,aAAa,GAA0B,IAAIC,OAAJ,EAA7C;AACA,IAAMC,cAAc,GAA4B,IAAID,OAAJ,EAAhD;AAEP;;;;;AAIO,IAAME,gBAAgB,GAA4B,IAAIF,OAAJ,EAAlD;AACA,IAAMG,iBAAiB,GAAiC,IAAIH,OAAJ,EAAxD;AAEA,IAAMI,6BAA6B,GAGtC,IAAIJ,OAAJ,EAHG;AAIA,IAAMK,eAAe,GAA+B,IAAIL,OAAJ,EAApD;AACA,IAAMM,eAAe,GAA+B,IAAIN,OAAJ,EAApD;AACA,IAAMO,WAAW,GAAuB,IAAIP,OAAJ,EAAxC;AACA,IAAMQ,wBAAwB,GAGjC,IAAIR,OAAJ,EAHG;AAKP;;;;AAIO,IAAMS,YAAY,GAA6B,IAAIT,OAAJ,EAA/C;AACA,IAAMU,UAAU,GAA6B,IAAIV,OAAJ,EAA7C;AACA,IAAMW,YAAY,GAA6B,IAAIX,OAAJ,EAA/C;AAEA,IAAMY,wBAAwB,GAGjC,IAAIZ,OAAJ,EAHG;AAKP;;;;AAIO,IAAMa,mBAAmB,GAAG,IAAIb,OAAJ,EAA5B;AAEP;;;;AAIO,IAAMc,wBAAwB,GAGjC,IAAId,OAAJ,EAHG;AAKA,IAAMe,iCAAiC,GAG1C,IAAIf,OAAJ,EAHG;AAKA,IAAMgB,oBAAoB,GAG7B,IAAIhB,OAAJ,EAHG;AAKP;;;;AAIO,IAAMiB,uBAAuB,GAGhC,IAAIjB,OAAJ,EAHG;AAKA,IAAMkB,wBAAwB,GAGjC,IAAIlB,OAAJ,EAHG;AAKA,IAAMmB,2BAA2B,GAGpC,IAAInB,OAAJ,EAHG;AAKA,IAAMoB,sBAAsB,GAAgC,IAAIpB,OAAJ,EAA5D;AAEP;;;;AAIO,IAAMqB,kBAAkB,GAAIC,MAAM,CAAC,aAAD,CAAlC;AACA,IAAMC,uBAAuB,GAAID,MAAM,CAC5C,kBAD4C,CAAvC;;;AC9FP,SAAS,kBAAkB,CAAC,GAAG,EAAE;AACjC,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACvD,CAAC;AACD;AACA,cAAc,GAAG,kBAAkB,CAAC;AACpC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACP5E,SAAS,gBAAgB,CAAC,IAAI,EAAE;AAChC,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5H,CAAC;AACD;AACA,cAAc,GAAG,gBAAgB,CAAC;AAClC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACL5E,SAAS,kBAAkB,GAAG;AAC9B,EAAE,MAAM,IAAI,SAAS,CAAC,sIAAsI,CAAC,CAAC;AAC9J,CAAC;AACD;AACA,cAAc,GAAG,kBAAkB,CAAC;AACpC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACG5E,SAAS,kBAAkB,CAAC,GAAG,EAAE;AACjC,EAAE,OAAO,iBAAiB,CAAC,GAAG,CAAC,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,0BAA0B,CAAC,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAClH,CAAC;AACD;AACA,cAAc,GAAG,kBAAkB,CAAC;AACpC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;;;;;ACH5E,IAAOE,OAAO,GAAG7B,UAAU,CAAC8B,IAA5B;AA0BA;;;;AAIO,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,KAAD;AAC5B,SACGA,KAAK,IAAIA,KAAK,CAACC,aAAf,IAAgCD,KAAK,CAACC,aAAN,CAAoBC,WAArD,IAAqE,IADvE;AAGD,CAJM;AAMP;;;;AAIO,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAACH,KAAD;AAC1B,SAAOI,SAAS,CAACJ,KAAD,CAAT,IAAoBA,KAAK,CAACK,QAAN,KAAmB,CAA9C;AACD,CAFM;AAIP;;;;AAIO,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAACN,KAAD;AAC1B,SAAOI,SAAS,CAACJ,KAAD,CAAT,IAAoBA,KAAK,CAACK,QAAN,KAAmB,CAA9C;AACD,CAFM;AAIP;;;;AAIO,IAAMD,SAAS,GAAG,SAAZA,SAAY,CAACJ,KAAD;AACvB,MAAMnD,MAAM,GAAGkD,cAAc,CAACC,KAAD,CAA7B;AACA,SAAO,CAAC,CAACnD,MAAF,IAAYmD,KAAK,YAAYnD,MAAM,CAAC0D,IAA3C;AACD,CAHM;AAKP;;;;AAIO,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACR,KAAD;AAC5B,MAAMnD,MAAM,GAAGmD,KAAK,IAAIA,KAAK,CAACS,UAAf,IAA6BV,cAAc,CAACC,KAAK,CAACS,UAAP,CAA1D;AACA,SAAO,CAAC,CAAC5D,MAAF,IAAYmD,KAAK,YAAYnD,MAAM,CAAC6D,SAA3C;AACD,CAHM;AAKP;;;;AAIO,IAAMC,SAAS,GAAG,SAAZA,SAAY,CAACX,KAAD;AACvB,SAAOI,SAAS,CAACJ,KAAD,CAAT,IAAoBA,KAAK,CAACK,QAAN,KAAmB,CAA9C;AACD,CAFM;AAIP;;;;AAIO,IAAMO,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACC,KAAD;AAClC,SACEA,KAAK,CAACC,aAAN,IACAD,KAAK,CAACC,aAAN,CAAoBC,OAApB,CAA4B,YAA5B,MAA8C,EAD9C,IAEAF,KAAK,CAACC,aAAN,CAAoBE,KAApB,CAA0BC,MAA1B,KAAqC,CAHvC;AAKD,CANM;AAQP;;;;AAIO,IAAMC,iBAAiB,GAAG,SAApBA,iBAAoB,CAACC,QAAD;AAC/B,iCAAqBA,QAArB;AAAA,MAAKC,IAAL;AAAA,MAAWC,MAAX;AAGA;;;AACA,MAAIf,YAAY,CAACc,IAAD,CAAZ,IAAsBA,IAAI,CAACE,UAAL,CAAgBL,MAA1C,EAAkD;AAChD,QAAIM,MAAM,GAAGF,MAAM,KAAKD,IAAI,CAACE,UAAL,CAAgBL,MAAxC;AACA,QAAIO,KAAK,GAAGD,MAAM,GAAGF,MAAM,GAAG,CAAZ,GAAgBA,MAAlC;;AAFgD,gCAG/BI,wBAAwB,CACvCL,IADuC,EAEvCI,KAFuC,EAGvCD,MAAM,GAAG,UAAH,GAAgB,SAHiB,CAHO;;AAAA;;AAG9CH,IAAAA,IAH8C;AAGxCI,IAAAA,KAHwC;AAQhD;AACAD,IAAAA,MAAM,GAAGC,KAAK,GAAGH,MAAjB,CATgD;AAYhD;;AACA,WAAOf,YAAY,CAACc,IAAD,CAAZ,IAAsBA,IAAI,CAACE,UAAL,CAAgBL,MAA7C,EAAqD;AACnD,UAAMS,CAAC,GAAGH,MAAM,GAAGH,IAAI,CAACE,UAAL,CAAgBL,MAAhB,GAAyB,CAA5B,GAAgC,CAAhD;AACAG,MAAAA,IAAI,GAAGO,gBAAgB,CAACP,IAAD,EAAOM,CAAP,EAAUH,MAAM,GAAG,UAAH,GAAgB,SAAhC,CAAvB;AACD,KAhB+C;;;AAmBhDF,IAAAA,MAAM,GAAGE,MAAM,IAAIH,IAAI,CAACQ,WAAL,IAAoB,IAA9B,GAAqCR,IAAI,CAACQ,WAAL,CAAiBX,MAAtD,GAA+D,CAAxE;AACD;;;AAGD,SAAO,CAACG,IAAD,EAAOC,MAAP,CAAP;AACD,CA7BM;AA+BP;;;;AAIO,IAAMQ,aAAa,GAAG,SAAhBA,aAAgB,CAACT,IAAD;AAC3B,MAAIU,MAAM,GAAGV,IAAI,IAAIA,IAAI,CAACW,UAA1B;;AACA,SAAOD,MAAP,EAAe;AACb,QAAIA,MAAM,CAACE,QAAP,OAAsB,qBAA1B,EAAiD;AAC/C,aAAO,IAAP;AACD;;AACDF,IAAAA,MAAM,GAAGA,MAAM,CAACC,UAAhB;AACD;;AACD,SAAO,KAAP;AACD,CATM;AAWP;;;;;AAKO,IAAMN,wBAAwB,GAAG,SAA3BA,wBAA2B,CACtCK,MADsC,EAEtCN,KAFsC,EAGtCS,SAHsC;AAKtC,MAAQX,UAAR,GAAuBQ,MAAvB,CAAQR,UAAR;AACA,MAAIY,KAAK,GAAGZ,UAAU,CAACE,KAAD,CAAtB;AACA,MAAIE,CAAC,GAAGF,KAAR;AACA,MAAIW,YAAY,GAAG,KAAnB;AACA,MAAIC,aAAa,GAAG,KAApB;AAGA;;AACA,SACEjC,YAAY,CAAC+B,KAAD,CAAZ,IACC5B,YAAY,CAAC4B,KAAD,CAAZ,IAAuBA,KAAK,CAACZ,UAAN,CAAiBL,MAAjB,KAA4B,CADpD,IAECX,YAAY,CAAC4B,KAAD,CAAZ,IAAuBA,KAAK,CAACG,YAAN,CAAmB,iBAAnB,MAA0C,OAHpE,EAIE;AACA,QAAIF,YAAY,IAAIC,aAApB,EAAmC;AACjC;AACD;;AAED,QAAIV,CAAC,IAAIJ,UAAU,CAACL,MAApB,EAA4B;AAC1BkB,MAAAA,YAAY,GAAG,IAAf;AACAT,MAAAA,CAAC,GAAGF,KAAK,GAAG,CAAZ;AACAS,MAAAA,SAAS,GAAG,UAAZ;AACA;AACD;;AAED,QAAIP,CAAC,GAAG,CAAR,EAAW;AACTU,MAAAA,aAAa,GAAG,IAAhB;AACAV,MAAAA,CAAC,GAAGF,KAAK,GAAG,CAAZ;AACAS,MAAAA,SAAS,GAAG,SAAZ;AACA;AACD;;AAEDC,IAAAA,KAAK,GAAGZ,UAAU,CAACI,CAAD,CAAlB;AACAF,IAAAA,KAAK,GAAGE,CAAR;AACAA,IAAAA,CAAC,IAAIO,SAAS,KAAK,SAAd,GAA0B,CAA1B,GAA8B,CAAC,CAApC;AACD;;AAED,SAAO,CAACC,KAAD,EAAQV,KAAR,CAAP;AACD,CA1CM;AA4CP;;;;;AAKO,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAmB,CAC9BG,MAD8B,EAE9BN,KAF8B,EAG9BS,SAH8B;AAK9B,+BAAgBR,wBAAwB,CAACK,MAAD,EAASN,KAAT,EAAgBS,SAAhB,CAAxC;AAAA;AAAA,MAAOC,KAAP;;AACA,SAAOA,KAAP;AACD,CAPM;AASP;;;;;;;AAOO,IAAMI,YAAY,GAAG,SAAfA,YAAe,CAACC,OAAD;AAC1B,MAAIC,IAAI,GAAG,EAAX;;AAEA,MAAI7B,SAAS,CAAC4B,OAAD,CAAT,IAAsBA,OAAO,CAACE,SAAlC,EAA6C;AAC3C,WAAOF,OAAO,CAACE,SAAf;AACD;;AAED,MAAInC,YAAY,CAACiC,OAAD,CAAhB,EAA2B;AACzB,mCAAwBG,KAAK,CAACC,IAAN,CAAWJ,OAAO,CAACjB,UAAnB,CAAxB,iCAAwD;AAAnD,UAAMsB,SAAS,kBAAf;AACHJ,MAAAA,IAAI,IAAIF,YAAY,CAACM,SAAD,CAApB;AACD;;AAED,QAAMC,OAAO,GAAGC,gBAAgB,CAACP,OAAD,CAAhB,CAA0BQ,gBAA1B,CAA2C,SAA3C,CAAhB;;AAEA,QAAIF,OAAO,KAAK,OAAZ,IAAuBA,OAAO,KAAK,MAAnC,IAA6CN,OAAO,CAACS,OAAR,KAAoB,IAArE,EAA2E;AACzER,MAAAA,IAAI,IAAI,IAAR;AACD;AACF;;AAED,SAAOA,IAAP;AACD,CApBM;AAsBP;;;;AAGA,IAAMS,kBAAkB,GAAG,8BAA3B;AACO,IAAMC,yBAAyB,GAAG,SAA5BA,yBAA4B,CACvCC,YADuC;AAGvC,MAAMC,QAAQ,GAAGD,YAAY,CAACpC,OAAb,CAAqB,WAArB,CAAjB;;AACA,aAAqBqC,QAAQ,CAACC,KAAT,CAAeJ,kBAAf,KAAsC,EAA3D;AAAA;AAAA,MAASK,QAAT;;AACA,SAAOA,QAAP;AACD,CANM;AA8BP;;;;AAIO,IAAMC,iBAAiB,GAAG,SAApBA,iBAAoB,CAC/BpH,MAD+B,EAE/BqH,QAF+B,EAG/BC,KAH+B;AAK/B,MAAQC,MAAR,GAAmBF,QAAnB,CAAQE,MAAR;;AACA,MAAIpD,YAAY,CAACoD,MAAD,CAAZ,IAAwBA,MAAM,CAACC,OAAP,CAAe,2BAAf,CAA5B,EAAyE;AACvE,WAAO,KAAP;AACD;;AAED,8BAAqBC,WAAW,CAACC,SAAZ,CAAsB1H,MAAtB,CAArB;AAAA,MAAQ0B,QAAR,yBAAQA,QAAR;;AACA,MAAIA,QAAQ,CAACiG,QAAT,CAAkBJ,MAAlB,CAAJ,EAA+B;AAC7B,WAAOE,WAAW,CAACG,UAAZ,CAAuB5H,MAAvB,EAA+BuH,MAA/B,EAAuC;AAAEM,MAAAA,QAAQ,EAAE;AAAZ,KAAvC,CAAP;AACD;;AAED,MAAMC,cAAc,GAAGR,KAAK,CAACS,IAAN,CAAW;QAAGC,mBAAAA;QAAYC,qBAAAA;;iDAC5BD;;;;AAAnB,0DAA+B;AAAA,YAApB/C,IAAoB;;AAC7B,YAAIA,IAAI,KAAKsC,MAAT,IAAmBtC,IAAI,CAAC0C,QAAL,CAAcJ,MAAd,CAAvB,EAA8C;AAC5C,iBAAO,IAAP;AACD;AACF;;;;;;;kDAEkBU;;;;AAAnB,6DAAiC;AAAA,YAAtBhD,KAAsB;;AAC/B,YAAIA,KAAI,KAAKsC,MAAT,IAAmBtC,KAAI,CAAC0C,QAAL,CAAcJ,MAAd,CAAvB,EAA8C;AAC5C,iBAAO,IAAP;AACD;AACF;;;;;;AACF,GAZsB,CAAvB;;AAcA,MAAI,CAACO,cAAD,IAAmBA,cAAc,KAAKT,QAA1C,EAAoD;AAClD,WAAO,KAAP;AACD;;;AAGD,SAAOD,iBAAiB,CAACpH,MAAD,EAAS8H,cAAT,EAAyBR,KAAzB,CAAxB;AACD,CAnCM;;;ACxRP,SAAS,eAAe,CAAC,QAAQ,EAAE,WAAW,EAAE;AAChD,EAAE,IAAI,EAAE,QAAQ,YAAY,WAAW,CAAC,EAAE;AAC1C,IAAI,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC;AAC7D,GAAG;AACH,CAAC;AACD;AACA,cAAc,GAAG,eAAe,CAAC;AACjC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;ACP5E;;;AAIA,IAAIY,CAAC,GAAG,CAAR;AAEA;;;;;IAKaC,GAAb,GAGE;;;AACE,OAAKC,EAAL,aAAaF,CAAC,EAAd;AACD,CALH;;IC2PaT,WAAW,GAAyB;AAC/CY,EAAAA,mBAAmB,EAAE,6BAAArI,MAAM;AAAA,WAAImD,uBAAuB,CAACmF,GAAxB,CAA4BtI,MAA5B,CAAJ;AAAA,GADoB;AAG/CuI,EAAAA,oBAAoB,EAAE,8BAAAvI,MAAM;;;AAC1B,6BAAAgD,wBAAwB,CAACsF,GAAzB,CAA6BtI,MAA7B;AACD,GAL8C;AAO/CwI,EAAAA,IAAI,EAAE,cAAAxI,MAAM;AACV,QAAMyI,EAAE,GAAGhB,WAAW,CAACiB,SAAZ,CAAsB1I,MAAtB,EAA8BA,MAA9B,CAAX;AACA,QAAM2I,IAAI,GAAGlB,WAAW,CAACmB,wBAAZ,CAAqC5I,MAArC,CAAb;AACA4C,IAAAA,UAAU,CAACiG,GAAX,CAAe7I,MAAf,EAAuB,KAAvB;;AAEA,QAAI2I,IAAI,CAACG,aAAL,KAAuBL,EAA3B,EAA+B;AAC7BA,MAAAA,EAAE,CAACD,IAAH;AACD;AACF,GAf8C;AAiB/CO,EAAAA,QAAQ,EAAE,kBAAA/I,MAAM;AACd,QAAQgJ,SAAR,GAAsBhJ,MAAtB,CAAQgJ,SAAR;AACA,QAAML,IAAI,GAAGlB,WAAW,CAACmB,wBAAZ,CAAqC5I,MAArC,CAAb;AACA,QAAMiJ,YAAY,GAAGN,IAAI,CAACO,YAAL,EAArB;;AAEA,QAAID,YAAY,IAAIA,YAAY,CAACE,UAAb,GAA0B,CAA9C,EAAiD;AAC/CF,MAAAA,YAAY,CAACG,eAAb;AACD;;AAED,QAAIJ,SAAJ,EAAe;AACbK,MAAAA,gBAAU,CAACN,QAAX,CAAoB/I,MAApB;AACD;AACF,GA7B8C;AA+B/C4I,EAAAA,wBAAwB,EAAE,kCAAA5I,MAAM;AAC9B,QAAMyI,EAAE,GAAGhB,WAAW,CAACiB,SAAZ,CAAsB1I,MAAtB,EAA8BA,MAA9B,CAAX;AACA,QAAM2I,IAAI,GAAGF,EAAE,CAACa,WAAH,EAAb;;AAEA,QACE,CAACX,IAAI,YAAYY,QAAhB,IAA4BZ,IAAI,YAAYa,UAA7C,KACAb,IAAI,CAACO,YAAL,IAAqB,IAFvB,EAGE;AACA,aAAOP,IAAP;AACD;;AAED,WAAOF,EAAE,CAAC3E,aAAV;AACD,GA3C8C;AA6C/C2F,EAAAA,cAAc,EAAE,wBAACzJ,MAAD,EAAS0E,KAAT;AACd,QAAI,iBAAiBA,KAArB,EAA4B;AAC1BA,MAAAA,KAAK,GAAGA,KAAK,CAACgF,WAAd;AACD;;AAED,iBAA2ChF,KAA3C;AAAA,QAAiBiF,CAAjB,UAAQC,OAAR;AAAA,QAA6BC,CAA7B,UAAoBC,OAApB;AAAA,QAAgCvC,MAAhC,UAAgCA,MAAhC;;AAEA,QAAIoC,CAAC,IAAI,IAAL,IAAaE,CAAC,IAAI,IAAtB,EAA4B;AAC1B,YAAM,IAAI3J,KAAJ,0DAA4DwE,KAA5D,EAAN;AACD;;AAED,QAAMO,IAAI,GAAGwC,WAAW,CAACsC,WAAZ,CAAwB/J,MAAxB,EAAgC0E,KAAK,CAAC6C,MAAtC,CAAb;AACA,QAAMyC,IAAI,GAAGvC,WAAW,CAACwC,QAAZ,CAAqBjK,MAArB,EAA6BiF,IAA7B,CAAb;AAGA;AACA;;AACA,QAAIiF,aAAO,CAACC,SAAR,CAAkBlF,IAAlB,KAA2BmF,YAAM,CAACC,MAAP,CAAcrK,MAAd,EAAsBiF,IAAtB,CAA/B,EAA4D;AAC1D,UAAMqF,IAAI,GAAG/C,MAAM,CAACgD,qBAAP,EAAb;AACA,UAAMC,MAAM,GAAGxK,MAAM,CAACyK,QAAP,CAAgBxF,IAAhB,IACX0E,CAAC,GAAGW,IAAI,CAACI,IAAT,GAAgBJ,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAACK,KAAjB,GAAyBhB,CAD9B,GAEXE,CAAC,GAAGS,IAAI,CAACM,GAAT,GAAeN,IAAI,CAACM,GAAL,GAAWN,IAAI,CAACO,MAAhB,GAAyBhB,CAF5C;AAIA,UAAMiB,IAAI,GAAGV,YAAM,CAACW,KAAP,CAAa/K,MAAb,EAAqBgK,IAArB,EAA2B;AACtCc,QAAAA,IAAI,EAAEN,MAAM,GAAG,OAAH,GAAa;AADa,OAA3B,CAAb;AAGA,UAAMO,KAAK,GAAGP,MAAM,GAChBJ,YAAM,CAACY,MAAP,CAAchL,MAAd,EAAsB8K,IAAtB,CADgB,GAEhBV,YAAM,CAACa,KAAP,CAAajL,MAAb,EAAqB8K,IAArB,CAFJ;;AAIA,UAAIC,KAAJ,EAAW;AACT,YAAMG,MAAK,GAAGd,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqB+K,KAArB,CAAd;;AACA,eAAOG,MAAP;AACD;AACF;;;AAGD,QAAIC,QAAJ;;AACA,gCAAqB1D,WAAW,CAACC,SAAZ,CAAsB1H,MAAtB,CAArB;AAAA,QAAQ0B,QAAR,yBAAQA,QAAR;;;AAGA,QAAIA,QAAQ,CAAC0J,mBAAb,EAAkC;AAChCD,MAAAA,QAAQ,GAAGzJ,QAAQ,CAAC0J,mBAAT,CAA6BzB,CAA7B,EAAgCE,CAAhC,CAAX;AACD,KAFD,MAEO;AACL,UAAMwB,QAAQ,GAAG3J,QAAQ,CAAC4J,sBAAT,CAAgC3B,CAAhC,EAAmCE,CAAnC,CAAjB;;AAEA,UAAIwB,QAAJ,EAAc;AACZF,QAAAA,QAAQ,GAAGzJ,QAAQ,CAAC6J,WAAT,EAAX;AACAJ,QAAAA,QAAQ,CAACK,QAAT,CAAkBH,QAAQ,CAACI,UAA3B,EAAuCJ,QAAQ,CAACnG,MAAhD;AACAiG,QAAAA,QAAQ,CAACO,MAAT,CAAgBL,QAAQ,CAACI,UAAzB,EAAqCJ,QAAQ,CAACnG,MAA9C;AACD;AACF;;AAED,QAAI,CAACiG,QAAL,EAAe;AACb,YAAM,IAAIjL,KAAJ,0DAA4DwE,KAA5D,EAAN;AACD;;;AAGD,QAAMwG,KAAK,GAAGzD,WAAW,CAACkE,YAAZ,CAAyB3L,MAAzB,EAAiCmL,QAAjC,EAA2C;AACvDS,MAAAA,UAAU,EAAE,KAD2C;AAEvDC,MAAAA,aAAa,EAAE;AAFwC,KAA3C,CAAd;AAIA,WAAOX,KAAP;AACD,GA5G8C;AA8G/CY,EAAAA,OAAO,EAAE,iBAAC9L,MAAD,EAASiF,IAAT;AACP,QAAI8G,GAAG,GAAGtJ,WAAW,CAAC6F,GAAZ,CAAgBrD,IAAhB,CAAV;;AAEA,QAAI,CAAC8G,GAAL,EAAU;AACRA,MAAAA,GAAG,GAAG,IAAI5D,GAAJ,EAAN;AACA1F,MAAAA,WAAW,CAACoG,GAAZ,CAAgB5D,IAAhB,EAAsB8G,GAAtB;AACD;;AAED,WAAOA,GAAP;AACD,GAvH8C;AAyH/C9B,EAAAA,QAAQ,EAAE,kBAACjK,MAAD,EAASiF,IAAT;AACR,QAAM+E,IAAI,GAAS,EAAnB;AACA,QAAIjE,KAAK,GAAGd,IAAZ;;AAEA,WAAO,IAAP,EAAa;AACX,UAAMU,MAAM,GAAGxD,cAAc,CAACmG,GAAf,CAAmBvC,KAAnB,CAAf;;AAEA,UAAIJ,MAAM,IAAI,IAAd,EAAoB;AAClB,YAAIyE,YAAM,CAAC4B,QAAP,CAAgBjG,KAAhB,CAAJ,EAA4B;AAC1B,iBAAOiE,IAAP;AACD,SAFD,MAEO;AACL;AACD;AACF;;AAED,UAAMzE,CAAC,GAAGtD,aAAa,CAACqG,GAAd,CAAkBvC,KAAlB,CAAV;;AAEA,UAAIR,CAAC,IAAI,IAAT,EAAe;AACb;AACD;;AAEDyE,MAAAA,IAAI,CAACiC,OAAL,CAAa1G,CAAb;AACAQ,MAAAA,KAAK,GAAGJ,MAAR;AACD;;AAED,UAAM,IAAIzF,KAAJ,mDACuCgM,cAAQ,CAACC,SAAT,CAAmBlH,IAAnB,CADvC,EAAN;AAGD,GArJ8C;AAuJ/CmH,EAAAA,KAAK,EAAE,eAAApM,MAAM;AACX,QAAMyI,EAAE,GAAGhB,WAAW,CAACiB,SAAZ,CAAsB1I,MAAtB,EAA8BA,MAA9B,CAAX;AACA,QAAM2I,IAAI,GAAGlB,WAAW,CAACmB,wBAAZ,CAAqC5I,MAArC,CAAb;AACA4C,IAAAA,UAAU,CAACiG,GAAX,CAAe7I,MAAf,EAAuB,IAAvB;;AAEA,QAAI2I,IAAI,CAACG,aAAL,KAAuBL,EAA3B,EAA+B;AAC7BA,MAAAA,EAAE,CAAC2D,KAAH,CAAS;AAAEC,QAAAA,aAAa,EAAE;AAAjB,OAAT;AACD;AACF,GA/J8C;AAiK/C3E,EAAAA,SAAS,EAAE,mBAAA1H,MAAM;AACf,QAAMU,MAAM,GAAG0B,gBAAgB,CAACkG,GAAjB,CAAqBtI,MAArB,CAAf;;AACA,QAAI,CAACU,MAAL,EAAa;AACX,YAAM,IAAIR,KAAJ,CAAU,sDAAV,CAAN;AACD;;AACD,WAAOQ,MAAP;AACD,GAvK8C;AAyK/CkH,EAAAA,UAAU,EAAE,oBAAC5H,MAAD,EAASuH,MAAT;QAAiB+E,8EAAU;AACrC,4BAA6BA,OAA7B,CAAQzE,QAAR;AAAA,QAAQA,QAAR,kCAAmB,KAAnB;AACA,QAAM0E,QAAQ,GAAG9E,WAAW,CAACiB,SAAZ,CAAsB1I,MAAtB,EAA8BA,MAA9B,CAAjB;AACA,QAAIwM,QAAJ;AAGA;AACA;AACA;;AACA,QAAI;AACFA,MAAAA,QAAQ,GAAIrI,YAAY,CAACoD,MAAD,CAAZ,GACRA,MADQ,GAERA,MAAM,CAACkF,aAFX;AAGD,KAJD,CAIE,OAAOC,GAAP,EAAY;AACZ,UACE,CAACA,GAAG,CAACC,OAAJ,CAAYC,QAAZ,CAAqB,iDAArB,CADH,EAEE;AACA,cAAMF,GAAN;AACD;AACF;;AAED,QAAI,CAACF,QAAL,EAAe;AACb,aAAO,KAAP;AACD;;AAED,WACEA,QAAQ,CAACK,OAAT,4BAA4CN,QAA5C,KACC,CAAC1E,QAAD,IAAa2E,QAAQ,CAACM,iBAAtB,GACG,IADH,GAEI,OAAON,QAAQ,CAACM,iBAAhB,KAAsC,SAAtC;AACC;AACAN,IAAAA,QAAQ,CAACK,OAAT,CAAiB,2BAAjB,MAAkDN,QAFpD,IAGA,CAAC,CAACC,QAAQ,CAACtG,YAAT,CAAsB,uBAAtB,CANN,CADF;AASD,GA3M8C;AA6M/C6G,EAAAA,iBAAiB,EAAE,2BAAC/M,MAAD,EAASuH,MAAT;AAAA,WACjBtD,SAAS,CAACsD,MAAD,CAAT,IACAE,WAAW,CAACG,UAAZ,CAAuB5H,MAAvB,EAA+BuH,MAA/B,EAAuC;AAAEM,MAAAA,QAAQ,EAAE;AAAZ,KAAvC,CAFiB;AAAA,GA7M4B;AAiN/CmF,EAAAA,QAAQ,EAAE,kBAAChN,MAAD,EAASkL,KAAT;AACR,QAAQ+B,MAAR,GAA0B/B,KAA1B,CAAQ+B,MAAR;AAAA,QAAgBb,KAAhB,GAA0BlB,KAA1B,CAAgBkB,KAAhB;AACA,WACEhC,YAAM,CAAC8C,OAAP,CAAelN,MAAf,EAAuBiN,MAAM,CAACjD,IAA9B,KAAuCI,YAAM,CAAC8C,OAAP,CAAelN,MAAf,EAAuBoM,KAAK,CAACpC,IAA7B,CADzC;AAGD,GAtN8C;AAwN/CmD,EAAAA,mBAAmB,EAAE,6BAACnN,MAAD,EAASuH,MAAT;AAAA,WACnBE,WAAW,CAACsF,iBAAZ,CAA8B/M,MAA9B,EAAsCuH,MAAtC,KACAE,WAAW,CAAC2F,6BAAZ,CAA0CpN,MAA1C,EAAkDuH,MAAlD,CAFmB;AAAA,GAxN0B;AA4N/C8F,EAAAA,SAAS,EAAE,mBAACrN,MAAD,EAASuH,MAAT;AAAA,WACTtD,SAAS,CAACsD,MAAD,CAAT,IAAqBE,WAAW,CAACG,UAAZ,CAAuB5H,MAAvB,EAA+BuH,MAA/B,CADZ;AAAA,GA5NoC;AA+N/C+F,EAAAA,UAAU,EAAE,oBAACtN,MAAD,EAASuN,IAAT;AACVvN,IAAAA,MAAM,CAACsN,UAAP,CAAkBC,IAAlB;AACD,GAjO8C;AAmO/CC,EAAAA,kBAAkB,EAAE,4BAACxN,MAAD,EAASuN,IAAT;AAAA,WAAkBvN,MAAM,CAACwN,kBAAP,CAA0BD,IAA1B,CAAlB;AAAA,GAnO2B;AAqO/CE,EAAAA,cAAc,EAAE,wBAACzN,MAAD,EAASuN,IAAT;AAAA,WAAkBvN,MAAM,CAACyN,cAAP,CAAsBF,IAAtB,CAAlB;AAAA,GArO+B;AAuO/CG,EAAAA,WAAW,EAAE,qBAAA1N,MAAM;AACjB,WAAO,CAAC,CAAC6C,YAAY,CAACyF,GAAb,CAAiBtI,MAAjB,CAAT;AACD,GAzO8C;AA2O/C2N,EAAAA,SAAS,EAAE,mBAAA3N,MAAM;AAAA,WAAI,CAAC,CAAC4C,UAAU,CAAC0F,GAAX,CAAetI,MAAf,CAAN;AAAA,GA3O8B;AA6O/C4N,EAAAA,UAAU,EAAE,oBAAA5N,MAAM;AAAA,WAAI,CAAC,CAAC2C,YAAY,CAAC2F,GAAb,CAAiBtI,MAAjB,CAAN;AAAA,GA7O6B;AA+O/CoN,EAAAA,6BAA6B,EAAE,uCAACpN,MAAD,EAASuH,MAAT;AAC7B,QAAI5E,YAAY,CAAC2F,GAAb,CAAiBtI,MAAjB,CAAJ,EAA8B,OAAO,KAAP;AAE9B,QAAM6N,SAAS,GACbpG,WAAW,CAAC4F,SAAZ,CAAsBrN,MAAtB,EAA8BuH,MAA9B,KACAE,WAAW,CAACsC,WAAZ,CAAwB/J,MAAxB,EAAgCuH,MAAhC,CAFF;AAGA,WAAO2C,aAAO,CAACC,SAAR,CAAkB0D,SAAlB,KAAgCzD,YAAM,CAACC,MAAP,CAAcrK,MAAd,EAAsB6N,SAAtB,CAAvC;AACD,GAtP8C;AAwP/CC,EAAAA,eAAe,EAAE,yBAAC9N,MAAD,EAASuN,IAAT,EAAeQ,WAAf;AAAA,WACf/N,MAAM,CAAC8N,eAAP,CAAuBP,IAAvB,EAA6BQ,WAA7B,CADe;AAAA,GAxP8B;AA2P/CrF,EAAAA,SAAS,EAAE,mBAAC1I,MAAD,EAASiF,IAAT;AACT,QAAM+I,cAAc,GAAGtL,wBAAwB,CAAC4F,GAAzB,CAA6BtI,MAA7B,CAAvB;AACA,QAAMoG,OAAO,GAAGgE,YAAM,CAAC4B,QAAP,CAAgB/G,IAAhB,IACZ5C,iBAAiB,CAACiG,GAAlB,CAAsBtI,MAAtB,CADY,GAEZgO,cAFY,aAEZA,cAFY,uBAEZA,cAAc,CAAE1F,GAAhB,CAAoBb,WAAW,CAACqE,OAAZ,CAAoB9L,MAApB,EAA4BiF,IAA5B,CAApB,CAFJ;;AAIA,QAAI,CAACmB,OAAL,EAAc;AACZ,YAAM,IAAIlG,KAAJ,sDAC0CgM,cAAQ,CAACC,SAAT,CAAmBlH,IAAnB,CAD1C,EAAN;AAGD;;AAED,WAAOmB,OAAP;AACD,GAxQ8C;AA0Q/C6H,EAAAA,UAAU,EAAE,oBAACjO,MAAD,EAAS+K,KAAT;AACV,uBAAeX,YAAM,CAACnF,IAAP,CAAYjF,MAAZ,EAAoB+K,KAAK,CAACf,IAA1B,CAAf;AAAA;AAAA,QAAO/E,IAAP;;AACA,QAAMwD,EAAE,GAAGhB,WAAW,CAACiB,SAAZ,CAAsB1I,MAAtB,EAA8BiF,IAA9B,CAAX;AACA,QAAID,QAAJ;AAGA;;AACA,QAAIoF,YAAM,QAAN,CAAYpK,MAAZ,EAAoB;AAAEkO,MAAAA,EAAE,EAAEnD;AAAN,KAApB,CAAJ,EAAwC;AACtCA,MAAAA,KAAK,GAAG;AAAEf,QAAAA,IAAI,EAAEe,KAAK,CAACf,IAAd;AAAoB9E,QAAAA,MAAM,EAAE;AAA5B,OAAR;AACD;AAGD;AACA;;;AACA,QAAMiJ,QAAQ,iDAAd;AACA,QAAMC,KAAK,GAAG7H,KAAK,CAACC,IAAN,CAAWiC,EAAE,CAAC4F,gBAAH,CAAoBF,QAApB,CAAX,CAAd;AACA,QAAIG,KAAK,GAAG,CAAZ;;AAEA,SAAK,IAAI/I,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6I,KAAK,CAACtJ,MAA1B,EAAkCS,CAAC,EAAnC,EAAuC;AACrC,UAAMc,IAAI,GAAG+H,KAAK,CAAC7I,CAAD,CAAlB;AACA,UAAMa,OAAO,GAAGC,IAAI,CAAClB,UAAL,CAAgB,CAAhB,CAAhB;;AAEA,UAAIiB,OAAO,IAAI,IAAX,IAAmBA,OAAO,CAACX,WAAR,IAAuB,IAA9C,EAAoD;AAClD;AACD;;AAED,UAAQX,MAAR,GAAmBsB,OAAO,CAACX,WAA3B,CAAQX,MAAR;AACA,UAAMyJ,IAAI,GAAGlI,IAAI,CAACH,YAAL,CAAkB,mBAAlB,CAAb;AACA,UAAMsI,UAAU,GAAGD,IAAI,IAAI,IAAR,GAAezJ,MAAf,GAAwB1E,QAAQ,CAACmO,IAAD,EAAO,EAAP,CAAnD;AACA,UAAME,GAAG,GAAGH,KAAK,GAAGE,UAApB,CAXqC;AAcrC;;AACA,UAAME,QAAQ,GAAGN,KAAK,CAAC7I,CAAC,GAAG,CAAL,CAAtB;;AACA,UACEwF,KAAK,CAAC7F,MAAN,KAAiBuJ,GAAjB,IACAC,QADA,aACAA,QADA,eACAA,QAAQ,CAAEC,YAAV,CAAuB,6BAAvB,CAFF,EAGE;AAAA;;AACA,YAAMC,OAAO,GAAGF,QAAQ,CAACvJ,UAAT,CAAoB,CAApB,CAAhB;AAEAH,QAAAA,QAAQ,GAAG;AAET;AACA;AACA;AACA;AACA4J,QAAAA,OAAO,YAAYlL,OAAnB,GAA6BkL,OAA7B,GAAuCF,QAN9B,EAOT,yBAAAA,QAAQ,CAACjJ,WAAT,wEAAsBoJ,UAAtB,CAAiC,QAAjC,IAA6C,CAA7C,GAAiD,CAPxC,CAAX;AASA;AACD;;AAED,UAAI9D,KAAK,CAAC7F,MAAN,IAAgBuJ,GAApB,EAAyB;AACvB,YAAMvJ,MAAM,GAAG4J,IAAI,CAACC,GAAL,CAASjK,MAAT,EAAiBgK,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYjE,KAAK,CAAC7F,MAAN,GAAeoJ,KAA3B,CAAjB,CAAf;AACAtJ,QAAAA,QAAQ,GAAG,CAACoB,OAAD,EAAUlB,MAAV,CAAX;AACA;AACD;;AAEDoJ,MAAAA,KAAK,GAAGG,GAAR;AACD;;AAED,QAAI,CAACzJ,QAAL,EAAe;AACb,YAAM,IAAI9E,KAAJ,wDAC4CgM,cAAQ,CAACC,SAAT,CAC9CpB,KAD8C,CAD5C,EAAN;AAKD;;AAED,WAAO/F,QAAP;AACD,GAhV8C;AAkV/CiK,EAAAA,UAAU,EAAE,oBAACjP,MAAD,EAASkL,KAAT;AACV,QAAQ+B,MAAR,GAA0B/B,KAA1B,CAAQ+B,MAAR;AAAA,QAAgBb,KAAhB,GAA0BlB,KAA1B,CAAgBkB,KAAhB;AACA,QAAM8C,UAAU,GAAGC,WAAK,CAACD,UAAN,CAAiBhE,KAAjB,CAAnB;AACA,QAAMkE,SAAS,GAAG3H,WAAW,CAACwG,UAAZ,CAAuBjO,MAAvB,EAA+BiN,MAA/B,CAAlB;AACA,QAAMoC,QAAQ,GAAGF,WAAK,CAACG,WAAN,CAAkBpE,KAAlB,IACbkE,SADa,GAEb3H,WAAW,CAACwG,UAAZ,CAAuBjO,MAAvB,EAA+BoM,KAA/B,CAFJ;AAIA,QAAM1L,MAAM,GAAG+G,WAAW,CAACC,SAAZ,CAAsB1H,MAAtB,CAAf;AACA,QAAMmL,QAAQ,GAAGzK,MAAM,CAACgB,QAAP,CAAgB6J,WAAhB,EAAjB;;AACA,eAAiC2D,UAAU,GAAGG,QAAH,GAAcD,SAAzD;AAAA;AAAA,QAAOG,SAAP;AAAA,QAAkBC,WAAlB;;AACA,gBAA6BN,UAAU,GAAGE,SAAH,GAAeC,QAAtD;AAAA;AAAA,QAAOI,OAAP;AAAA,QAAgBC,SAAhB;AAGA;AACA;;;AACA,QAAMC,OAAO,GAAIxL,YAAY,CAACoL,SAAD,CAAZ,GACbA,SADa,GAEbA,SAAS,CAAC9C,aAFd;AAGA,QAAMmD,kBAAkB,GAAG,CAAC,CAACD,OAAO,CAACzJ,YAAR,CAAqB,uBAArB,CAA7B;AACA,QAAM2J,KAAK,GAAI1L,YAAY,CAACsL,OAAD,CAAZ,GACXA,OADW,GAEXA,OAAO,CAAChD,aAFZ;AAGA,QAAMqD,gBAAgB,GAAG,CAAC,CAACD,KAAK,CAAC3J,YAAN,CAAmB,uBAAnB,CAA3B;AAEAiF,IAAAA,QAAQ,CAACK,QAAT,CAAkB+D,SAAlB,EAA6BK,kBAAkB,GAAG,CAAH,GAAOJ,WAAtD;AACArE,IAAAA,QAAQ,CAACO,MAAT,CAAgB+D,OAAhB,EAAyBK,gBAAgB,GAAG,CAAH,GAAOJ,SAAhD;AACA,WAAOvE,QAAP;AACD,GA9W8C;AAgX/CpB,EAAAA,WAAW,EAAE,qBAAC/J,MAAD,EAASoG,OAAT;AACX,QAAI2J,KAAK,GAAG5L,YAAY,CAACiC,OAAD,CAAZ,GAAwBA,OAAxB,GAAkCA,OAAO,CAACqG,aAAtD;;AAEA,QAAIsD,KAAK,IAAI,CAACA,KAAK,CAACpB,YAAN,CAAmB,iBAAnB,CAAd,EAAqD;AACnDoB,MAAAA,KAAK,GAAGA,KAAK,CAAClD,OAAN,qBAAR;AACD;;AAED,QAAM5H,IAAI,GAAG8K,KAAK,GAAGxN,eAAe,CAAC+F,GAAhB,CAAoByH,KAApB,CAAH,GAA+C,IAAjE;;AAEA,QAAI,CAAC9K,IAAL,EAAW;AACT,YAAM,IAAI/E,KAAJ,sDAAwD6P,KAAxD,EAAN;AACD;;AAED,WAAO9K,IAAP;AACD,GA9X8C;AAgY/C+K,EAAAA,YAAY,EAAE,sBACZhQ,MADY,EAEZgF,QAFY,EAGZsH,OAHY;AAQZ,QAAQV,UAAR,GAAsCU,OAAtC,CAAQV,UAAR;AAAA,QAAoBC,aAApB,GAAsCS,OAAtC,CAAoBT,aAApB;;AACA,gBAAqCD,UAAU,GAC3C5G,QAD2C,GAE3CD,iBAAiB,CAACC,QAAD,CAFrB;AAAA;AAAA,QAAOiL,WAAP;AAAA,QAAoBC,aAApB;;AAGA,QAAMtK,UAAU,GAAGqK,WAAW,CAACrK,UAA/B;AACA,QAAIuK,QAAQ,GAAsB,IAAlC;AACA,QAAIjL,MAAM,GAAG,CAAb;;AAEA,QAAIU,UAAJ,EAAgB;AAAA;;AACd,UAAM2G,QAAQ,GAAG9E,WAAW,CAACiB,SAAZ,CAAsB1I,MAAtB,EAA8BA,MAA9B,CAAjB;AACA,UAAMoQ,iBAAiB,GAAGxK,UAAU,CAACiH,OAAX,CAAmB,0BAAnB,CAA1B,CAFc;AAId;AACA;AACA;;AACA,UAAMwD,QAAQ,GACZD,iBAAiB,IAAI7D,QAAQ,CAAC5E,QAAT,CAAkByI,iBAAlB,CAArB,GACIA,iBADJ,GAEI,IAHN;AAIA,UAAIE,QAAQ,GAAG1K,UAAU,CAACiH,OAAX,CAAmB,mBAAnB,CAAf;AACA,UAAIzG,OAAO,GAAsB,IAAjC,CAZc;AAed;;AACA,UAAIkK,QAAJ,EAAc;AACZH,QAAAA,QAAQ,GAAGG,QAAQ,CAACzD,OAAT,CAAiB,0BAAjB,CAAX;;AAEA,YAAIsD,QAAJ,EAAc;AACZ,cAAMzP,MAAM,GAAG+G,WAAW,CAACC,SAAZ,CAAsB1H,MAAtB,CAAf;AACA,cAAMkL,KAAK,GAAGxK,MAAM,CAACgB,QAAP,CAAgB6J,WAAhB,EAAd;AACAL,UAAAA,KAAK,CAACM,QAAN,CAAe2E,QAAf,EAAyB,CAAzB;AACAjF,UAAAA,KAAK,CAACQ,MAAN,CAAauE,WAAb,EAA0BC,aAA1B;AAEA,cAAMK,QAAQ,GAAGrF,KAAK,CAACsF,aAAN,EAAjB;AACA,cAAMC,QAAQ,gCACTlK,KAAK,CAACxE,SAAN,CAAgB2O,KAAhB,CAAsBC,IAAtB,CACDJ,QAAQ,CAAClC,gBAAT,CAA0B,yBAA1B,CADC,CADS,sBAIT9H,KAAK,CAACxE,SAAN,CAAgB2O,KAAhB,CAAsBC,IAAtB,CACDJ,QAAQ,CAAClC,gBAAT,CAA0B,yBAA1B,CADC,CAJS,EAAd;AASAoC,UAAAA,QAAQ,CAACG,OAAT,CAAiB,UAAAnI,EAAE;AACjB;AACA;AACA,gBACE1H,UAAU,IACV,CAAC6K,UADD,IAEAnD,EAAE,CAACkG,YAAH,CAAgB,uBAAhB,CAFA,IAGAlG,EAAE,CAAChD,WAAH,CAAeX,MAAf,GAAwB,CAHxB,IAIA2D,EAAE,CAACoI,WAAH,KAAmB,QALrB,EAME;AACA,kBAAIpI,EAAE,CAAChD,WAAH,CAAeoJ,UAAf,CAA0B,QAA1B,CAAJ,EAAyC;AACvCpG,gBAAAA,EAAE,CAAChD,WAAH,GAAiBgD,EAAE,CAAChD,WAAH,CAAeiL,KAAf,CAAqB,CAArB,CAAjB;AACD;;AAED;AACD;;AAEDjI,YAAAA,EAAG,CAAC7C,UAAJ,CAAgBkL,WAAhB,CAA4BrI,EAA5B;AACD,WAlBD,EAhBY;AAqCZ;AACA;AACA;AACA;;AACAvD,UAAAA,MAAM,GAAGqL,QAAQ,CAAC9K,WAAT,CAAsBX,MAA/B;AACAsB,UAAAA,OAAO,GAAG+J,QAAV;AACD;AACF,OA/CD,MA+CO,IAAIE,QAAJ,EAAc;AACnB;AACA;AACA;AACA,YAAMU,SAAS,GAAGV,QAAQ,CAAChC,gBAAT,CAA0B,mBAA1B,CAAlB;;AACA,aAAK,IAAIhJ,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG0L,SAAS,CAACjM,MAAtC,EAA8CO,KAAK,EAAnD,EAAuD;AACrD,cAAM2L,OAAO,GAAGD,SAAS,CAAC1L,KAAD,CAAzB;;AACA,cAAIoC,WAAW,CAACG,UAAZ,CAAuB5H,MAAvB,EAA+BgR,OAA/B,CAAJ,EAA6C;AAC3CV,YAAAA,QAAQ,GAAGU,OAAX;AACA;AACD;AACF,SAXkB;;;AAcnB,YAAI,CAACV,QAAL,EAAe;AACbpL,UAAAA,MAAM,GAAG,CAAT;AACD,SAFD,MAEO;AACLiL,UAAAA,QAAQ,GAAGG,QAAQ,CAACzD,OAAT,CAAiB,0BAAjB,CAAX;AACAzG,UAAAA,OAAO,GAAGkK,QAAV;AACApL,UAAAA,MAAM,GAAGkB,OAAO,CAACX,WAAR,CAAqBX,MAA9B;AACAsB,UAAAA,OAAO,CAACiI,gBAAR,CAAyB,yBAAzB,EAAoDuC,OAApD,CAA4D,UAAAnI,EAAE;AAC5DvD,YAAAA,MAAM,IAAIuD,EAAE,CAAChD,WAAH,CAAgBX,MAA1B;AACD,WAFD;AAGD;AACF;;AAED,UACEsB,OAAO,IACPlB,MAAM,KAAKkB,OAAO,CAACX,WAAR,CAAqBX,MADhC;AAGA;AACA/D,MAAAA,UAJA,IAKAqF,OAAO,CAACF,YAAR,CAAqB,uBAArB,MAAkD,GALlD,4BAMAE,OAAO,CAACX,WANR,iDAMA,qBAAqBoJ,UAArB,CAAgC,QAAhC,CANA,KAYCjJ,UAAU,CAAC+I,YAAX,CAAwB,uBAAxB,KAIE3N,UAAU,6BAAIoF,OAAO,CAACX,WAAZ,kDAAI,sBAAqBwL,QAArB,CAA8B,MAA9B,CAhBjB,CADF,EAkBE;AACA/L,QAAAA,MAAM;AACP;AACF;;AAED,QAAInE,UAAU,IAAI,CAACoP,QAAf,IAA2B,CAACvE,UAAhC,EAA4C;AAC1C,UAAM3G,IAAI,GAAGW,UAAU,CAAC+I,YAAX,CAAwB,iBAAxB,IACT/I,UADS,GAETA,UAAU,CAACiH,OAAX,CAAmB,mBAAnB,CAFJ;;AAIA,UAAI5H,IAAI,IAAIwC,WAAW,CAACG,UAAZ,CAAuB5H,MAAvB,EAA+BiF,IAA/B,EAAqC;AAAE4C,QAAAA,QAAQ,EAAE;AAAZ,OAArC,CAAZ,EAAsE;AACpE,YAAMgG,UAAS,GAAGpG,WAAW,CAACsC,WAAZ,CAAwB/J,MAAxB,EAAgCiF,IAAhC,CAAlB;;AACA,4BAAuBmF,YAAM,CAACkE,KAAP,CACrBtO,MADqB,EAErByH,WAAW,CAACwC,QAAZ,CAAqBjK,MAArB,EAA6B6N,UAA7B,CAFqB,CAAvB;AAAA,YAAM7D,KAAN,iBAAMA,IAAN;AAAA,YAAY9E,OAAZ,iBAAYA,MAAZ;;AAKA,YAAI,CAACD,IAAI,CAACiM,aAAL,CAAmB,mBAAnB,CAAL,EAA8C;AAC5ChM,UAAAA,OAAM,GAAGgL,aAAT;AACD;;AAED,eAAO;AAAElG,UAAAA,IAAI,EAAJA,KAAF;AAAQ9E,UAAAA,MAAM,EAANA;AAAR,SAAP;AACD;AACF;;AAED,QAAI,CAACiL,QAAL,EAAe;AACb,UAAItE,aAAJ,EAAmB;AACjB,eAAO,IAAP;AACD;;AACD,YAAM,IAAI3L,KAAJ,wDAC4C8E,QAD5C,EAAN;AAGD;AAGD;AACA;;;AACA,QAAM6I,SAAS,GAAGpG,WAAW,CAACsC,WAAZ,CAAwB/J,MAAxB,EAAgCmQ,QAAhC,CAAlB;AACA,QAAMnG,IAAI,GAAGvC,WAAW,CAACwC,QAAZ,CAAqBjK,MAArB,EAA6B6N,SAA7B,CAAb;AACA,WAAO;AAAE7D,MAAAA,IAAI,EAAJA,IAAF;AAAQ9E,MAAAA,MAAM,EAANA;AAAR,KAAP;AACD,GAniB8C;AAqiB/CyG,EAAAA,YAAY,EAAE,sBACZ3L,MADY,EAEZmL,QAFY,EAGZmB,OAHY;AAQZ,QAAQV,UAAR,GAAsCU,OAAtC,CAAQV,UAAR;AAAA,QAAoBC,aAApB,GAAsCS,OAAtC,CAAoBT,aAApB;AACA,QAAMpD,EAAE,GAAGpE,cAAc,CAAC8G,QAAD,CAAd,GACPA,QAAQ,CAAC7G,UADF,GAEP6G,QAAQ,CAACgG,cAFb;AAGA,QAAI7M,UAAJ;AACA,QAAI8M,YAAJ;AACA,QAAIC,SAAJ;AACA,QAAIC,WAAJ;AACA,QAAIhC,WAAJ;;AAEA,QAAI7G,EAAJ,EAAQ;AACN,UAAIpE,cAAc,CAAC8G,QAAD,CAAlB,EAA8B;AAC5B;AACA;AACA,YAAInK,UAAU,IAAImK,QAAQ,CAAChC,UAAT,GAAsB,CAAxC,EAA2C;AACzCkI,UAAAA,SAAS,GAAGlG,QAAQ,CAACkG,SAArB,CADyC;;AAEzC,cAAME,UAAU,GAAGpG,QAAQ,CAACqG,UAAT,CAAoB,CAApB,CAAnB;AACA,cAAMC,SAAS,GAAGtG,QAAQ,CAACqG,UAAT,CAAoBrG,QAAQ,CAAChC,UAAT,GAAsB,CAA1C,CAAlB,CAHyC;;AAMzC,cACEkI,SAAS,YAAYK,mBAArB,IACAH,UAAU,CAACJ,cAAX,YAAqCO,mBADrC,IAEAD,SAAS,CAACN,cAAV,YAAoCO,mBAHtC,EAIE;AACA;AADA,gBAESC,eAFT,GAEA,SAASA,eAAT,CAAyBC,OAAzB;AACE,kBAAIA,OAAO,CAACC,iBAAR,GAA4B,CAAhC,EAAmC;AACjC,uBAAOF,eAAe,CAAcC,OAAO,CAACE,QAAR,CAAiB,CAAjB,CAAd,CAAtB;AACD,eAFD,MAEO;AACL,uBAAOF,OAAP;AACD;AACF,aARD;;AAUA,gBAAMG,YAAY,GAAwBR,UAAU,CAACJ,cAArD;AACA,gBAAMa,WAAW,GAAwBP,SAAS,CAACN,cAAnD,CAXA;;AAcA,gBAAMc,SAAS,GAAGN,eAAe,CAClBI,YAAY,CAACD,QAAb,CAAsBP,UAAU,CAAC/B,WAAjC,CADkB,CAAjC;AAGA,gBAAM0C,QAAQ,GAAGP,eAAe,CACjBK,WAAW,CAACF,QAAZ,CAAqBL,SAAS,CAACjC,WAA/B,CADiB,CAAhC,CAjBA;;AAsBA8B,YAAAA,WAAW,GAAG,CAAd;;AAEA,gBAAIY,QAAQ,CAAC/M,UAAT,CAAoBL,MAApB,GAA6B,CAAjC,EAAoC;AAClCR,cAAAA,UAAU,GAAG4N,QAAQ,CAAC/M,UAAT,CAAoB,CAApB,CAAb;AACD,aAFD,MAEO;AACLb,cAAAA,UAAU,GAAG4N,QAAb;AACD;;AAED,gBAAID,SAAS,CAAC9M,UAAV,CAAqBL,MAArB,GAA8B,CAAlC,EAAqC;AACnCuM,cAAAA,SAAS,GAAGY,SAAS,CAAC9M,UAAV,CAAqB,CAArB,CAAZ;AACD,aAFD,MAEO;AACLkM,cAAAA,SAAS,GAAGY,SAAZ;AACD;;AAED,gBAAIC,QAAQ,YAAYC,WAAxB,EAAqC;AACnCf,cAAAA,YAAY,GAAiBc,QAAS,CAACE,SAAV,CAAoBtN,MAAjD;AACD,aAFD,MAEO;AACL;AACAsM,cAAAA,YAAY,GAAG,CAAf;AACD;AACF,WA9CD,MA8CO;AACL;AACA;AACA,gBAAIG,UAAU,CAACJ,cAAX,KAA8BE,SAAlC,EAA6C;AAC3C/M,cAAAA,UAAU,GAAGmN,SAAS,CAACY,YAAvB;AACAjB,cAAAA,YAAY,GAAGK,SAAS,CAAC/B,SAAzB;AACA4B,cAAAA,WAAW,GAAGC,UAAU,CAAC/B,WAAzB;AACD,aAJD,MAIO;AACL;AACAlL,cAAAA,UAAU,GAAGiN,UAAU,CAACJ,cAAxB;AACAC,cAAAA,YAAY,GAAGG,UAAU,CAAC7B,SAA1B;AACA4B,cAAAA,WAAW,GAAGG,SAAS,CAACjC,WAAxB;AACD;AACF;AACF,SAlED,MAkEO;AACLlL,UAAAA,UAAU,GAAG6G,QAAQ,CAAC7G,UAAtB;AACA8M,UAAAA,YAAY,GAAGjG,QAAQ,CAACiG,YAAxB;AACAC,UAAAA,SAAS,GAAGlG,QAAQ,CAACkG,SAArB;AACAC,UAAAA,WAAW,GAAGnG,QAAQ,CAACmG,WAAvB;AACD,SA1E2B;AA6E5B;AACA;AACA;AACA;;;AACA,YAAKnQ,SAAS,IAAIuE,aAAa,CAACpB,UAAD,CAA3B,IAA4CtD,UAAhD,EAA4D;AAC1DsO,UAAAA,WAAW,GACTnE,QAAQ,CAAC7G,UAAT,KAAwB6G,QAAQ,CAACkG,SAAjC,IACAlG,QAAQ,CAACiG,YAAT,KAA0BjG,QAAQ,CAACmG,WAFrC;AAGD,SAJD,MAIO;AACLhC,UAAAA,WAAW,GAAGnE,QAAQ,CAACmE,WAAvB;AACD;AACF,OAxFD,MAwFO;AACLhL,QAAAA,UAAU,GAAG6G,QAAQ,CAACgG,cAAtB;AACAC,QAAAA,YAAY,GAAGjG,QAAQ,CAACqE,WAAxB;AACA6B,QAAAA,SAAS,GAAGlG,QAAQ,CAACkH,YAArB;AACAf,QAAAA,WAAW,GAAGnG,QAAQ,CAACuE,SAAvB;AACAJ,QAAAA,WAAW,GAAGnE,QAAQ,CAACmH,SAAvB;AACD;AACF;;AAED,QACEhO,UAAU,IAAI,IAAd,IACA+M,SAAS,IAAI,IADb,IAEAD,YAAY,IAAI,IAFhB,IAGAE,WAAW,IAAI,IAJjB,EAKE;AACA,YAAM,IAAIpR,KAAJ,wDAC4CiL,QAD5C,EAAN;AAGD;AAGD;AACA;;;AACA,QACE,kBAAkBkG,SAAlB,IACCA,SAAyB,CAACnL,YAA1B,CAAuC,iBAAvC,MAA8D,OAD/D,IAECmL,SAAyB,CAACnL,YAA1B,CAAuC,iBAAvC,MAA8D,MAHjE,EAIE;AAAA;;AACAmL,MAAAA,SAAS,GAAG/M,UAAZ;AACAgN,MAAAA,WAAW,GAAG,0BAAAhN,UAAU,CAACmB,WAAX,gFAAwBX,MAAxB,KAAkC,CAAhD;AACD;;AAED,QAAMmI,MAAM,GAAGxF,WAAW,CAACuI,YAAZ,CACbhQ,MADa,EAEb,CAACsE,UAAD,EAAa8M,YAAb,CAFa,EAGb;AACExF,MAAAA,UAAU,EAAVA,UADF;AAEEC,MAAAA,aAAa,EAAbA;AAFF,KAHa,CAAf;;AAQA,QAAI,CAACoB,MAAL,EAAa;AACX,aAAO,IAAP;AACD;;AAED,QAAMb,KAAK,GAAGkD,WAAW,GACrBrC,MADqB,GAErBxF,WAAW,CAACuI,YAAZ,CAAyBhQ,MAAzB,EAAiC,CAACqR,SAAD,EAAYC,WAAZ,CAAjC,EAA2D;AACzD1F,MAAAA,UAAU,EAAVA,UADyD;AAEzDC,MAAAA,aAAa,EAAbA;AAFyD,KAA3D,CAFJ;;AAMA,QAAI,CAACO,KAAL,EAAY;AACV,aAAO,IAAP;AACD;;AAED,QAAIlB,KAAK,GAAU;AAAE+B,MAAAA,MAAM,EAAEA,MAAV;AAA2Bb,MAAAA,KAAK,EAAEA;AAAlC,KAAnB;AAEA;AACA;AACA;;AACA,QACE+C,WAAK,CAACoD,UAAN,CAAiBrH,KAAjB,KACAiE,WAAK,CAACqD,SAAN,CAAgBtH,KAAhB,CADA,IAEA/G,YAAY,CAACkN,SAAD,CAFZ,IAGAjH,YAAM,QAAN,CAAYpK,MAAZ,EAAoB;AAAEkO,MAAAA,EAAE,EAAEhD,KAAK,CAACkB,KAAZ;AAAmBqG,MAAAA,IAAI,EAAE;AAAzB,KAApB,CAJF,EAKE;AACAvH,MAAAA,KAAK,GAAGd,YAAM,CAACsI,WAAP,CAAmB1S,MAAnB,EAA2BkL,KAA3B,EAAkC;AAAEyH,QAAAA,KAAK,EAAE;AAAT,OAAlC,CAAR;AACD;;AAED,WAAQzH,KAAR;AACD;AArtB8C;;AC9OjD;;;;;SAIgB0H,gBAAgB5S,QAAgB6S;AAC9C,MAAQ7I,IAAR,GAAuB6I,QAAvB,CAAQ7I,IAAR;AAAA,MAAc8I,IAAd,GAAuBD,QAAvB,CAAcC,IAAd;;AACA,MAAI,CAAC1I,YAAM,CAAC8C,OAAP,CAAelN,MAAf,EAAuBgK,IAAvB,CAAL,EAAmC;AACjC,WAAO,KAAP;AACD;;AAED,MAAM/E,IAAI,GAAGb,UAAI,CAACkE,GAAL,CAAStI,MAAT,EAAiBgK,IAAjB,CAAb;;AACA,MAAI,CAACrG,UAAI,CAACoP,MAAL,CAAY9N,IAAZ,CAAL,EAAwB;AACtB,WAAO,KAAP;AACD;;AAED,MAAI6N,IAAI,CAACxE,KAAL,KAAerJ,IAAI,CAACoB,IAAL,CAAUvB,MAAzB,IAAmCgO,IAAI,CAACzM,IAAL,CAAUvB,MAAV,KAAqB,CAA5D,EAA+D;AAC7D,WACEG,IAAI,CAACoB,IAAL,CAAUqK,KAAV,CAAgBoC,IAAI,CAACxE,KAArB,EAA4BwE,IAAI,CAACxE,KAAL,GAAawE,IAAI,CAACzM,IAAL,CAAUvB,MAAnD,MAA+DgO,IAAI,CAACzM,IADtE;AAGD;;AAED,MAAM2M,QAAQ,GAAGC,UAAI,CAACC,IAAL,CAAUlJ,IAAV,CAAjB;;AACA,MAAI,CAACI,YAAM,CAAC8C,OAAP,CAAelN,MAAf,EAAuBgT,QAAvB,CAAL,EAAuC;AACrC,WAAO,KAAP;AACD;;AAED,MAAMG,QAAQ,GAAG/O,UAAI,CAACkE,GAAL,CAAStI,MAAT,EAAiBgT,QAAjB,CAAjB;AACA,SAAOrP,UAAI,CAACoP,MAAL,CAAYI,QAAZ,KAAyBA,QAAQ,CAAC9M,IAAT,CAAcwI,UAAd,CAAyBiE,IAAI,CAACzM,IAA9B,CAAhC;AACD;SAEe+M,gBAAgB/M;oCAAiBgN;AAAAA,IAAAA;;;AAC/C,SAAOA,KAAK,CAACC,MAAN,CACL,UAACjN,IAAD,EAAOyM,IAAP;AAAA,WACEzM,IAAI,CAACqK,KAAL,CAAW,CAAX,EAAcoC,IAAI,CAACxE,KAAnB,IAA4BwE,IAAI,CAACzM,IAAjC,GAAwCA,IAAI,CAACqK,KAAL,CAAWoC,IAAI,CAACrE,GAAhB,CAD1C;AAAA,GADK,EAGLpI,IAHK,CAAP;AAKD;;AAED,SAASkN,yBAAT,CAAmCC,GAAnC,EAAgDC,OAAhD;AACE,MAAM3O,MAAM,GAAGgK,IAAI,CAACC,GAAL,CAASyE,GAAG,CAAC1O,MAAb,EAAqB2O,OAAO,CAAC3O,MAA7B,CAAf;;AAEA,OAAK,IAAIS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,MAApB,EAA4BS,CAAC,EAA7B,EAAiC;AAC/B,QAAIiO,GAAG,CAACE,MAAJ,CAAWnO,CAAX,MAAkBkO,OAAO,CAACC,MAAR,CAAenO,CAAf,CAAtB,EAAyC;AACvC,aAAOA,CAAP;AACD;AACF;;AAED,SAAOT,MAAP;AACD;;AAED,SAAS6O,yBAAT,CACEH,GADF,EAEEC,OAFF,EAGEzE,GAHF;AAKE,MAAMlK,MAAM,GAAGgK,IAAI,CAACC,GAAL,CAASyE,GAAG,CAAC1O,MAAb,EAAqB2O,OAAO,CAAC3O,MAA7B,EAAqCkK,GAArC,CAAf;;AAEA,OAAK,IAAIzJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,MAApB,EAA4BS,CAAC,EAA7B,EAAiC;AAC/B,QACEiO,GAAG,CAACE,MAAJ,CAAWF,GAAG,CAAC1O,MAAJ,GAAaS,CAAb,GAAiB,CAA5B,MAAmCkO,OAAO,CAACC,MAAR,CAAeD,OAAO,CAAC3O,MAAR,GAAiBS,CAAjB,GAAqB,CAApC,CADrC,EAEE;AACA,aAAOA,CAAP;AACD;AACF;;AAED,SAAOT,MAAP;AACD;AAED;;;;;SAGgB8O,oBAAoBC,YAAoBf;AACtD,MAAQxE,KAAR,GAA6BwE,IAA7B,CAAQxE,KAAR;AAAA,MAAeG,GAAf,GAA6BqE,IAA7B,CAAerE,GAAf;AAAA,MAAoBpI,IAApB,GAA6ByM,IAA7B,CAAoBzM,IAApB;AACA,MAAMyN,WAAW,GAAGD,UAAU,CAACnD,KAAX,CAAiBpC,KAAjB,EAAwBG,GAAxB,CAApB;AAEA,MAAMsF,YAAY,GAAGR,yBAAyB,CAACO,WAAD,EAAczN,IAAd,CAA9C;AACA,MAAM2I,GAAG,GAAGF,IAAI,CAACC,GAAL,CACV+E,WAAW,CAAChP,MAAZ,GAAqBiP,YADX,EAEV1N,IAAI,CAACvB,MAAL,GAAciP,YAFJ,CAAZ;AAIA,MAAMC,YAAY,GAAGL,yBAAyB,CAACG,WAAD,EAAczN,IAAd,EAAoB2I,GAApB,CAA9C;AAEA,MAAMiF,UAAU,GAAe;AAC7B3F,IAAAA,KAAK,EAAEA,KAAK,GAAGyF,YADc;AAE7BtF,IAAAA,GAAG,EAAEA,GAAG,GAAGuF,YAFkB;AAG7B3N,IAAAA,IAAI,EAAEA,IAAI,CAACqK,KAAL,CAAWqD,YAAX,EAAyB1N,IAAI,CAACvB,MAAL,GAAckP,YAAvC;AAHuB,GAA/B;;AAMA,MAAIC,UAAU,CAAC3F,KAAX,KAAqB2F,UAAU,CAACxF,GAAhC,IAAuCwF,UAAU,CAAC5N,IAAX,CAAgBvB,MAAhB,KAA2B,CAAtE,EAAyE;AACvE,WAAO,IAAP;AACD;;AAED,SAAOmP,UAAP;AACD;AAED;;;;;SAIgBC,iBACdL,YACAM,GACAC;AAEA,MAAM9F,KAAK,GAAGQ,IAAI,CAACC,GAAL,CAASoF,CAAC,CAAC7F,KAAX,EAAkB8F,CAAC,CAAC9F,KAApB,CAAd;AACA,MAAM+F,OAAO,GAAGvF,IAAI,CAACE,GAAL,CACd,CADc,EAEdF,IAAI,CAACC,GAAL,CAASoF,CAAC,CAAC7F,KAAF,GAAU6F,CAAC,CAAC9N,IAAF,CAAOvB,MAA1B,EAAkCsP,CAAC,CAAC3F,GAApC,IAA2C2F,CAAC,CAAC9F,KAF/B,CAAhB;AAKA,MAAMgG,OAAO,GAAGlB,eAAe,CAACS,UAAD,EAAaM,CAAb,EAAgBC,CAAhB,CAA/B;AACA,MAAMG,QAAQ,GAAGzF,IAAI,CAACE,GAAL,CACfoF,CAAC,CAAC9F,KAAF,GAAU8F,CAAC,CAAC/N,IAAF,CAAOvB,MADF,EAEfqP,CAAC,CAAC7F,KAAF,GACE6F,CAAC,CAAC9N,IAAF,CAAOvB,MADT,IAEGqP,CAAC,CAAC7F,KAAF,GAAU6F,CAAC,CAAC9N,IAAF,CAAOvB,MAAjB,GAA0BsP,CAAC,CAAC9F,KAA5B,GAAoC8F,CAAC,CAAC/N,IAAF,CAAOvB,MAA3C,GAAoD,CAFvD,IAGEuP,OALa,CAAjB;AAQA,MAAMhO,IAAI,GAAGiO,OAAO,CAAC5D,KAAR,CAAcpC,KAAd,EAAqBiG,QAArB,CAAb;AACA,MAAM9F,GAAG,GAAGK,IAAI,CAACE,GAAL,CAASmF,CAAC,CAAC1F,GAAX,EAAgB2F,CAAC,CAAC3F,GAAF,GAAQ0F,CAAC,CAAC9N,IAAF,CAAOvB,MAAf,IAAyBqP,CAAC,CAAC1F,GAAF,GAAQ0F,CAAC,CAAC7F,KAAnC,CAAhB,CAAZ;AACA,SAAOsF,mBAAmB,CAACC,UAAD,EAAa;AAAEvF,IAAAA,KAAK,EAALA,KAAF;AAASG,IAAAA,GAAG,EAAHA,GAAT;AAAcpI,IAAAA,IAAI,EAAJA;AAAd,GAAb,CAA1B;AACD;AAED;;;;SAGgBmO,YAAY3B;AAC1B,MAAQ7I,IAAR,GAAuB6I,QAAvB,CAAQ7I,IAAR;AAAA,MAAc8I,IAAd,GAAuBD,QAAvB,CAAcC,IAAd;AACA,SAAO;AACL7F,IAAAA,MAAM,EAAE;AAAEjD,MAAAA,IAAI,EAAJA,IAAF;AAAQ9E,MAAAA,MAAM,EAAE4N,IAAI,CAACxE;AAArB,KADH;AAELlC,IAAAA,KAAK,EAAE;AAAEpC,MAAAA,IAAI,EAAJA,IAAF;AAAQ9E,MAAAA,MAAM,EAAE4N,IAAI,CAACrE;AAArB;AAFF,GAAP;AAID;AAED;;;;;;;SAMgBgG,eAAezU,QAAgB+K;AAC7C,MAAMf,IAAN,GAAuBe,KAAvB,CAAMf,IAAN;AAAA,MAAY9E,MAAZ,GAAuB6F,KAAvB,CAAY7F,MAAZ;;AACA,MAAI,CAACkF,YAAM,CAAC8C,OAAP,CAAelN,MAAf,EAAuBgK,IAAvB,CAAL,EAAmC;AACjC,WAAO,IAAP;AACD;;AAED,MAAI0K,IAAI,GAAGtQ,UAAI,CAACkE,GAAL,CAAStI,MAAT,EAAiBgK,IAAjB,CAAX;;AACA,MAAI,CAACrG,UAAI,CAACoP,MAAL,CAAY2B,IAAZ,CAAL,EAAwB;AACtB,WAAO,IAAP;AACD;;AAED,MAAMC,WAAW,GAAGvK,YAAM,CAACwK,KAAP,CAAa5U,MAAb,EAAqB;AACvCkH,IAAAA,KAAK,EAAE,eAAAgB,CAAC;AAAA,aAAIgC,aAAO,CAACC,SAAR,CAAkBjC,CAAlB,KAAwBkC,YAAM,CAACyK,OAAP,CAAe7U,MAAf,EAAuBkI,CAAvB,CAA5B;AAAA,KAD+B;AAEvCgG,IAAAA,EAAE,EAAElE;AAFmC,GAArB,CAApB;;AAKA,MAAI,CAAC2K,WAAL,EAAkB;AAChB,WAAO,IAAP;AACD;;AAED,SAAOzP,MAAM,GAAGwP,IAAI,CAACrO,IAAL,CAAUvB,MAA1B,EAAkC;AAChC,QAAMgQ,KAAK,GAAG1K,YAAM,CAAC8I,IAAP,CAAYlT,MAAZ,EAAoB;AAAEkO,MAAAA,EAAE,EAAElE,IAAN;AAAY9C,MAAAA,KAAK,EAAEvD,UAAI,CAACoP;AAAxB,KAApB,CAAd;;AACA,QAAI,CAAC+B,KAAD,IAAU,CAAC7B,UAAI,CAAC8B,YAAL,CAAkBD,KAAK,CAAC,CAAD,CAAvB,EAA4BH,WAAW,CAAC,CAAD,CAAvC,CAAf,EAA4D;AAC1D,aAAO,IAAP;AACD;;AAEDzP,IAAAA,MAAM,IAAIwP,IAAI,CAACrO,IAAL,CAAUvB,MAApB;AACA4P,IAAAA,IAAI,GAAGI,KAAK,CAAC,CAAD,CAAZ;AACA9K,IAAAA,IAAI,GAAG8K,KAAK,CAAC,CAAD,CAAZ;AACD;;AAED,SAAO;AAAE9K,IAAAA,IAAI,EAAJA,IAAF;AAAQ9E,IAAAA,MAAM,EAANA;AAAR,GAAP;AACD;AAED;;;;SAGgB8P,eAAehV,QAAgBkL;AAC7C,MAAM+B,MAAM,GAAGwH,cAAc,CAACzU,MAAD,EAASkL,KAAK,CAAC+B,MAAf,CAA7B;;AACA,MAAI,CAACA,MAAL,EAAa;AACX,WAAO,IAAP;AACD;;AAED,MAAIkC,WAAK,CAACG,WAAN,CAAkBpE,KAAlB,CAAJ,EAA8B;AAC5B,WAAO;AAAE+B,MAAAA,MAAM,EAANA,MAAF;AAAUb,MAAAA,KAAK,EAAEa;AAAjB,KAAP;AACD;;AAED,MAAMb,KAAK,GAAGqI,cAAc,CAACzU,MAAD,EAASkL,KAAK,CAACkB,KAAf,CAA5B;;AACA,MAAI,CAACA,KAAL,EAAY;AACV,WAAO,IAAP;AACD;;AAED,SAAO;AAAEa,IAAAA,MAAM,EAANA,MAAF;AAAUb,IAAAA,KAAK,EAALA;AAAV,GAAP;AACD;SAEe6I,sBACdjV,QACA+K,OACAmK;AAEA,MAAMC,YAAY,GAAGhS,uBAAuB,CAACmF,GAAxB,CAA4BtI,MAA5B,CAArB;AACA,MAAM6S,QAAQ,GAAGsC,YAAH,aAAGA,YAAH,uBAAGA,YAAY,CAAEpN,IAAd,CAAmB;AAAA,QAAGiC,IAAH,QAAGA,IAAH;AAAA,WAClCiJ,UAAI,CAACmC,MAAL,CAAYpL,IAAZ,EAAkBe,KAAK,CAACf,IAAxB,CADkC;AAAA,GAAnB,CAAjB;;AAIA,MAAI,CAAC6I,QAAD,IAAa9H,KAAK,CAAC7F,MAAN,IAAgB2N,QAAQ,CAACC,IAAT,CAAcxE,KAA/C,EAAsD;AACpD,WAAO+G,WAAK,CAACC,SAAN,CAAgBvK,KAAhB,EAAuBmK,EAAvB,EAA2B;AAAEK,MAAAA,QAAQ,EAAE;AAAZ,KAA3B,CAAP;AACD;;AAED,MAAQzC,IAAR,GAAiBD,QAAjB,CAAQC,IAAR;AAEA;;AACA,MAAI/H,KAAK,CAAC7F,MAAN,IAAgB4N,IAAI,CAACxE,KAAL,GAAawE,IAAI,CAACzM,IAAL,CAAUvB,MAA3C,EAAmD;AACjD,QAAMmI,OAAM,GAAG;AAAEjD,MAAAA,IAAI,EAAEe,KAAK,CAACf,IAAd;AAAoB9E,MAAAA,MAAM,EAAE4N,IAAI,CAACxE;AAAjC,KAAf;;AACA,QAAMkH,YAAW,GAAGH,WAAK,CAACC,SAAN,CAAgBrI,OAAhB,EAAwBiI,EAAxB,EAA4B;AAC9CK,MAAAA,QAAQ,EAAE;AADoC,KAA5B,CAApB;;AAIA,QAAI,CAACC,YAAL,EAAkB;AAChB,aAAO,IAAP;AACD;;AAED,WAAO;AACLxL,MAAAA,IAAI,EAAEwL,YAAW,CAACxL,IADb;AAEL9E,MAAAA,MAAM,EAAEsQ,YAAW,CAACtQ,MAAZ,GAAqB6F,KAAK,CAAC7F,MAA3B,GAAoC4N,IAAI,CAACxE;AAF5C,KAAP;AAID;;;AAGD,MAAMrB,MAAM,GAAG;AACbjD,IAAAA,IAAI,EAAEe,KAAK,CAACf,IADC;AAEb9E,IAAAA,MAAM,EAAE6F,KAAK,CAAC7F,MAAN,GAAe4N,IAAI,CAACzM,IAAL,CAAUvB,MAAzB,GAAkCgO,IAAI,CAACrE,GAAvC,GAA6CqE,IAAI,CAACxE;AAF7C,GAAf;AAIA,MAAMkH,WAAW,GAAGH,WAAK,CAACC,SAAN,CAAgBrI,MAAhB,EAAwBiI,EAAxB,EAA4B;AAC9CK,IAAAA,QAAQ,EAAE;AADoC,GAA5B,CAApB;;AAGA,MAAI,CAACC,WAAL,EAAkB;AAChB,WAAO,IAAP;AACD;;AAED,MACEN,EAAE,CAACO,IAAH,KAAY,YAAZ,IACAxC,UAAI,CAACmC,MAAL,CAAYF,EAAE,CAAClL,IAAf,EAAqBe,KAAK,CAACf,IAA3B,CADA,IAEAiD,MAAM,CAAC/H,MAAP,GAAgBgQ,EAAE,CAAC7J,QAFnB,IAGAyH,IAAI,CAACxE,KAAL,GAAa4G,EAAE,CAAC7J,QAJlB,EAKE;AACA,WAAOmK,WAAP;AACD;;AAED,SAAO;AACLxL,IAAAA,IAAI,EAAEwL,WAAW,CAACxL,IADb;AAEL9E,IAAAA,MAAM,EAAEsQ,WAAW,CAACtQ,MAAZ,GAAqB4N,IAAI,CAACzM,IAAL,CAAUvB,MAA/B,GAAwCgO,IAAI,CAACrE,GAA7C,GAAmDqE,IAAI,CAACxE;AAF3D,GAAP;AAID;SAEeoH,sBACd1V,QACAkL,OACAgK;AAEA,MAAMjI,MAAM,GAAGgI,qBAAqB,CAACjV,MAAD,EAASkL,KAAK,CAAC+B,MAAf,EAAuBiI,EAAvB,CAApC;;AACA,MAAI,CAACjI,MAAL,EAAa;AACX,WAAO,IAAP;AACD;;AAED,MAAIkC,WAAK,CAACG,WAAN,CAAkBpE,KAAlB,CAAJ,EAA8B;AAC5B,WAAO;AAAE+B,MAAAA,MAAM,EAANA,MAAF;AAAUb,MAAAA,KAAK,EAAEa;AAAjB,KAAP;AACD;;AAED,MAAMb,KAAK,GAAG6I,qBAAqB,CAACjV,MAAD,EAASkL,KAAK,CAACkB,KAAf,EAAsB8I,EAAtB,CAAnC;;AACA,MAAI,CAAC9I,KAAL,EAAY;AACV,WAAO,IAAP;AACD;;AAED,SAAO;AAAEa,IAAAA,MAAM,EAANA,MAAF;AAAUb,IAAAA,KAAK,EAALA;AAAV,GAAP;AACD;SAEeuJ,kBACd9C,UACAqC;AAEA,MAAQlL,IAAR,GAA2B6I,QAA3B,CAAQ7I,IAAR;AAAA,MAAc8I,IAAd,GAA2BD,QAA3B,CAAcC,IAAd;AAAA,MAAoB1K,EAApB,GAA2ByK,QAA3B,CAAoBzK,EAApB;;AAEA,UAAQ8M,EAAE,CAACO,IAAX;AACE,SAAK,aAAL;AAAoB;AAClB,YAAI,CAACxC,UAAI,CAACmC,MAAL,CAAYF,EAAE,CAAClL,IAAf,EAAqBA,IAArB,CAAD,IAA+BkL,EAAE,CAAChQ,MAAH,IAAa4N,IAAI,CAACrE,GAArD,EAA0D;AACxD,iBAAOoE,QAAP;AACD;;AAED,YAAIqC,EAAE,CAAChQ,MAAH,IAAa4N,IAAI,CAACxE,KAAtB,EAA6B;AAC3B,iBAAO;AACLwE,YAAAA,IAAI,EAAE;AACJxE,cAAAA,KAAK,EAAE4G,EAAE,CAAC7O,IAAH,CAAQvB,MAAR,GAAiBgO,IAAI,CAACxE,KADzB;AAEJG,cAAAA,GAAG,EAAEyG,EAAE,CAAC7O,IAAH,CAAQvB,MAAR,GAAiBgO,IAAI,CAACrE,GAFvB;AAGJpI,cAAAA,IAAI,EAAEyM,IAAI,CAACzM;AAHP,aADD;AAML+B,YAAAA,EAAE,EAAFA,EANK;AAOL4B,YAAAA,IAAI,EAAJA;AAPK,WAAP;AASD;;AAED,eAAO;AACL8I,UAAAA,IAAI,EAAE;AACJxE,YAAAA,KAAK,EAAEwE,IAAI,CAACxE,KADR;AAEJG,YAAAA,GAAG,EAAEqE,IAAI,CAACrE,GAAL,GAAWyG,EAAE,CAAC7O,IAAH,CAAQvB,MAFpB;AAGJuB,YAAAA,IAAI,EAAEyM,IAAI,CAACzM;AAHP,WADD;AAML+B,UAAAA,EAAE,EAAFA,EANK;AAOL4B,UAAAA,IAAI,EAAJA;AAPK,SAAP;AASD;;AACD,SAAK,aAAL;AAAoB;AAClB,YAAI,CAACiJ,UAAI,CAACmC,MAAL,CAAYF,EAAE,CAAClL,IAAf,EAAqBA,IAArB,CAAD,IAA+BkL,EAAE,CAAChQ,MAAH,IAAa4N,IAAI,CAACrE,GAArD,EAA0D;AACxD,iBAAOoE,QAAP;AACD;;AAED,YAAIqC,EAAE,CAAChQ,MAAH,GAAYgQ,EAAE,CAAC7O,IAAH,CAAQvB,MAApB,IAA8BgO,IAAI,CAACxE,KAAvC,EAA8C;AAC5C,iBAAO;AACLwE,YAAAA,IAAI,EAAE;AACJxE,cAAAA,KAAK,EAAEwE,IAAI,CAACxE,KAAL,GAAa4G,EAAE,CAAC7O,IAAH,CAAQvB,MADxB;AAEJ2J,cAAAA,GAAG,EAAEqE,IAAI,CAACrE,GAAL,GAAWyG,EAAE,CAAC7O,IAAH,CAAQvB,MAFpB;AAGJuB,cAAAA,IAAI,EAAEyM,IAAI,CAACzM;AAHP,aADD;AAML+B,YAAAA,EAAE,EAAFA,EANK;AAOL4B,YAAAA,IAAI,EAAJA;AAPK,WAAP;AASD;;AAED,eAAO;AACL8I,UAAAA,IAAI,EAAE;AACJxE,YAAAA,KAAK,EAAEwE,IAAI,CAACxE,KADR;AAEJG,YAAAA,GAAG,EAAEqE,IAAI,CAACrE,GAAL,GAAWyG,EAAE,CAAC7O,IAAH,CAAQvB,MAFpB;AAGJuB,YAAAA,IAAI,EAAEyM,IAAI,CAACzM;AAHP,WADD;AAML+B,UAAAA,EAAE,EAAFA,EANK;AAOL4B,UAAAA,IAAI,EAAJA;AAPK,SAAP;AASD;;AACD,SAAK,YAAL;AAAmB;AACjB,YAAI,CAACiJ,UAAI,CAACmC,MAAL,CAAYF,EAAE,CAAClL,IAAf,EAAqBA,IAArB,CAAD,IAA+BkL,EAAE,CAAC7J,QAAH,IAAeyH,IAAI,CAACrE,GAAvD,EAA4D;AAC1D,iBAAO;AACLqE,YAAAA,IAAI,EAAJA,IADK;AAEL1K,YAAAA,EAAE,EAAFA,EAFK;AAGL4B,YAAAA,IAAI,EAAEiJ,UAAI,CAACqC,SAAL,CAAetL,IAAf,EAAqBkL,EAArB,EAAyB;AAAEK,cAAAA,QAAQ,EAAE;AAAZ,aAAzB;AAHD,WAAP;AAKD;;AAED,YAAIL,EAAE,CAAC7J,QAAH,GAAcyH,IAAI,CAACxE,KAAvB,EAA8B;AAC5B,iBAAO;AACLwE,YAAAA,IAAI,EAAE;AACJxE,cAAAA,KAAK,EAAEwE,IAAI,CAACxE,KADR;AAEJG,cAAAA,GAAG,EAAEK,IAAI,CAACC,GAAL,CAASmG,EAAE,CAAC7J,QAAZ,EAAsByH,IAAI,CAACrE,GAA3B,CAFD;AAGJpI,cAAAA,IAAI,EAAEyM,IAAI,CAACzM;AAHP,aADD;AAML+B,YAAAA,EAAE,EAAFA,EANK;AAOL4B,YAAAA,IAAI,EAAJA;AAPK,WAAP;AASD;;AAED,eAAO;AACL8I,UAAAA,IAAI,EAAE;AACJxE,YAAAA,KAAK,EAAEwE,IAAI,CAACxE,KAAL,GAAa4G,EAAE,CAAC7J,QADnB;AAEJoD,YAAAA,GAAG,EAAEqE,IAAI,CAACrE,GAAL,GAAWyG,EAAE,CAAC7J,QAFf;AAGJhF,YAAAA,IAAI,EAAEyM,IAAI,CAACzM;AAHP,WADD;AAML+B,UAAAA,EAAE,EAAFA,EANK;AAOL4B,UAAAA,IAAI,EAAEiJ,UAAI,CAACqC,SAAL,CAAetL,IAAf,EAAqBkL,EAArB,EAAyB;AAAEK,YAAAA,QAAQ,EAAE;AAAZ,WAAzB;AAPD,SAAP;AASD;;AACD,SAAK,YAAL;AAAmB;AACjB,YAAI,CAACtC,UAAI,CAACmC,MAAL,CAAYF,EAAE,CAAClL,IAAf,EAAqBA,IAArB,CAAL,EAAiC;AAC/B,iBAAO;AACL8I,YAAAA,IAAI,EAAJA,IADK;AAEL1K,YAAAA,EAAE,EAAFA,EAFK;AAGL4B,YAAAA,IAAI,EAAEiJ,UAAI,CAACqC,SAAL,CAAetL,IAAf,EAAqBkL,EAArB;AAHD,WAAP;AAKD;;AAED,eAAO;AACLpC,UAAAA,IAAI,EAAE;AACJxE,YAAAA,KAAK,EAAEwE,IAAI,CAACxE,KAAL,GAAa4G,EAAE,CAAC7J,QADnB;AAEJoD,YAAAA,GAAG,EAAEqE,IAAI,CAACrE,GAAL,GAAWyG,EAAE,CAAC7J,QAFf;AAGJhF,YAAAA,IAAI,EAAEyM,IAAI,CAACzM;AAHP,WADD;AAML+B,UAAAA,EAAE,EAAFA,EANK;AAOL4B,UAAAA,IAAI,EAAEiJ,UAAI,CAACqC,SAAL,CAAetL,IAAf,EAAqBkL,EAArB;AAPD,SAAP;AASD;AAxGH;;AA2GA,MAAMU,OAAO,GAAG3C,UAAI,CAACqC,SAAL,CAAetL,IAAf,EAAqBkL,EAArB,CAAhB;;AACA,MAAI,CAACU,OAAL,EAAc;AACZ,WAAO,IAAP;AACD;;AAED,SAAO;AACL9C,IAAAA,IAAI,EAAJA,IADK;AAEL9I,IAAAA,IAAI,EAAE4L,OAFD;AAGLxN,IAAAA,EAAE,EAAFA;AAHK,GAAP;AAKD;;;;;AC5YD;;AACA,IAAMyN,aAAa,GAAG,EAAtB;;AAGA,IAAMC,WAAW,GAAG,GAApB;;AAGA,IAAMC,KAAK,GAAG,SAARA,KAAQ,KAAd;;;AAGA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACnS,KAAD;AAAA,SACrB,CAAAA,KAAK,SAAL,IAAAA,KAAK,WAAL,YAAAA,KAAK,CAAEoS,WAAP,CAAmBC,IAAnB,MAA4B,cADP;AAAA,CAAvB;;SA+BgBC;MACdnW,cAAAA;MACAoW,oCAAAA;MACAC,4BAAAA;AAEA,MAAIC,QAAQ,GAAuB,KAAnC;AACA,MAAIC,uBAAuB,GAAyC,IAApE;AACA,MAAIC,cAAc,GAAyC,IAA3D;AACA,MAAIC,eAAe,GAAyC,IAA5D;AAEA,MAAIC,SAAS,GAAG,CAAhB;AACA,MAAIC,kBAAkB,GAA8B,KAApD;;AAEA,MAAMC,qBAAqB,GAAG,SAAxBA,qBAAwB;AAC5B,QAAMC,gBAAgB,GAAGxT,2BAA2B,CAACiF,GAA5B,CAAgCtI,MAAhC,CAAzB;AACAqD,IAAAA,2BAA2B,UAA3B,CAAmCrD,MAAnC;;AAEA,QAAI6W,gBAAJ,EAAsB;AACpB,UAAQ7N,SAAR,GAAsBhJ,MAAtB,CAAQgJ,SAAR;AACA,UAAMiL,UAAU,GAAGe,cAAc,CAAChV,MAAD,EAAS6W,gBAAT,CAAjC;;AAIA,UAAI5C,UAAU,KAAK,CAACjL,SAAD,IAAc,CAACmG,WAAK,CAACiG,MAAN,CAAanB,UAAb,EAAyBjL,SAAzB,CAApB,CAAd,EAAwE;AACtEK,QAAAA,gBAAU,CAACyN,MAAX,CAAkB9W,MAAlB,EAA0BiU,UAA1B;AACD;AACF;AACF,GAdD;;AAgBA,MAAM8C,aAAa,GAAG,SAAhBA,aAAgB;AACpB,QAAMC,MAAM,GAAG5T,wBAAwB,CAACkF,GAAzB,CAA6BtI,MAA7B,CAAf;AACAoD,IAAAA,wBAAwB,UAAxB,CAAgCpD,MAAhC;;AACA,QAAI,CAACgX,MAAL,EAAa;AACX;AACD;;AAED,QAAIA,MAAM,CAAC9I,EAAX,EAAe;AACb,UAAM3G,MAAM,GAAG8N,WAAK,CAAC4B,OAAN,CAAcD,MAAM,CAAC9I,EAArB,IACXuG,cAAc,CAACzU,MAAD,EAASgX,MAAM,CAAC9I,EAAhB,CADH,GAEX8G,cAAc,CAAChV,MAAD,EAASgX,MAAM,CAAC9I,EAAhB,CAFlB;;AAIA,UAAI,CAAC3G,MAAL,EAAa;AACX;AACD;;AAED,UAAMiN,YAAW,GAAGpK,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqBuH,MAArB,CAApB;;AACA,UAAI,CAACvH,MAAM,CAACgJ,SAAR,IAAqB,CAACmG,WAAK,CAACiG,MAAN,CAAapV,MAAM,CAACgJ,SAApB,EAA+BwL,YAA/B,CAA1B,EAAuE;AACrEnL,QAAAA,gBAAU,CAACyN,MAAX,CAAkB9W,MAAlB,EAA0BuH,MAA1B;AACD;AACF;;AAEDyP,IAAAA,MAAM,CAACE,GAAP;AACD,GAvBD;;AAyBA,MAAMC,KAAK,GAAG,SAARA,KAAQ;AACZ,QAAIX,cAAJ,EAAoB;AAClBY,MAAAA,YAAY,CAACZ,cAAD,CAAZ;AACAA,MAAAA,cAAc,GAAG,IAAjB;AACD;;AAED,QAAIC,eAAJ,EAAqB;AACnBW,MAAAA,YAAY,CAACX,eAAD,CAAZ;AACAA,MAAAA,eAAe,GAAG,IAAlB;AACD;;AAED,QAAI,CAACY,eAAe,EAAhB,IAAsB,CAACC,gBAAgB,EAA3C,EAA+C;AAC7CV,MAAAA,qBAAqB;AACrB;AACD;;AAED,QAAI,CAACN,QAAL,EAAe;AACbA,MAAAA,QAAQ,GAAG,IAAX;AACAiB,MAAAA,UAAU,CAAC;AAAA,eAAOjB,QAAQ,GAAG,KAAlB;AAAA,OAAD,CAAV;AACD;;AAED,QAAIgB,gBAAgB,EAApB,EAAwB;AACtBhB,MAAAA,QAAQ,GAAG,QAAX;AACD;;AAED,QAAMkB,YAAY,GAChBxX,MAAM,CAACgJ,SAAP,IACAoB,YAAM,CAACqN,QAAP,CAAgBzX,MAAhB,EAAwBA,MAAM,CAACgJ,SAA/B,EAA0C;AAAEuM,MAAAA,QAAQ,EAAE;AAAZ,KAA1C,CAFF;AAGArS,IAAAA,oBAAoB,CAAC2F,GAArB,CAAyB7I,MAAzB,EAAiCA,MAAM,CAAC0X,KAAxC;AAEA3B,IAAAA,KAAK,CACH,OADG,EAEH3S,wBAAwB,CAACkF,GAAzB,CAA6BtI,MAA7B,CAFG,EAGHmD,uBAAuB,CAACmF,GAAxB,CAA4BtI,MAA5B,CAHG,CAAL;AAMA,QAAI2X,uBAAuB,GAAGN,eAAe,EAA7C;AAEA,QAAIvE,IAAJ;;AACA,WAAQA,IAAI,4BAAG3P,uBAAuB,CAACmF,GAAxB,CAA4BtI,MAA5B,CAAH,0DAAG,sBAAsC,CAAtC,CAAf,EAA0D;AAAA;;AACxD,UAAM4X,YAAY,GAAG3U,iCAAiC,CAACqF,GAAlC,CAAsCtI,MAAtC,CAArB;;AAEA,UAAI4X,YAAY,KAAKC,SAArB,EAAgC;AAC9B5U,QAAAA,iCAAiC,UAAjC,CAAyCjD,MAAzC;AACAA,QAAAA,MAAM,CAAC0X,KAAP,GAAeE,YAAf;AACD;;AAED,UAAIA,YAAY,IAAIjB,kBAAkB,KAAK,KAA3C,EAAkD;AAChDA,QAAAA,kBAAkB,GAAG,IAArB;AAED;;AAED,UAAMzL,KAAK,GAAGsJ,WAAW,CAAC1B,IAAD,CAAzB;;AACA,UAAI,CAAC9S,MAAM,CAACgJ,SAAR,IAAqB,CAACmG,WAAK,CAACiG,MAAN,CAAapV,MAAM,CAACgJ,SAApB,EAA+BkC,KAA/B,CAA1B,EAAiE;AAC/D7B,QAAAA,gBAAU,CAACyN,MAAX,CAAkB9W,MAAlB,EAA0BkL,KAA1B;AACD;;AAED,UAAI4H,IAAI,CAACA,IAAL,CAAUzM,IAAd,EAAoB;AAClB+D,QAAAA,YAAM,CAAC0N,UAAP,CAAkB9X,MAAlB,EAA0B8S,IAAI,CAACA,IAAL,CAAUzM,IAApC;AACD,OAFD,MAEO;AACL+D,QAAAA,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB;AACD,OAtBuD;AAyBxD;;;AACAmD,MAAAA,uBAAuB,CAAC0F,GAAxB,CACE7I,MADF,4BAEEmD,uBAAuB,CAACmF,GAAxB,CAA4BtI,MAA5B,CAFF,2DAEE,uBAAqCgY,MAArC,CACE;AAAA,YAAG5P,EAAH,SAAGA,EAAH;AAAA,eAAYA,EAAE,KAAK0K,IAAK,CAAC1K,EAAzB;AAAA,OADF,CAFF;;AAOA,UAAI,CAACwK,eAAe,CAAC5S,MAAD,EAAS8S,IAAT,CAApB,EAAoC;AAElC6E,QAAAA,uBAAuB,GAAG,KAA1B;AACAvU,QAAAA,wBAAwB,UAAxB,CAAgCpD,MAAhC;AACAkD,QAAAA,oBAAoB,UAApB,CAA4BlD,MAA5B;AACAsW,QAAAA,QAAQ,GAAG,QAAX,CALkC;AAQlC;;AACAjT,QAAAA,2BAA2B,UAA3B,CAAmCrD,MAAnC;AACAoW,QAAAA,4BAA4B,CAAC6B,MAA7B;AACA5B,QAAAA,oBAAoB,CAAC4B,MAArB;AACAT,QAAAA,YAAY,SAAZ,IAAAA,YAAY,WAAZ,YAAAA,YAAY,CAAEU,KAAd;AACD;AACF;;AAED,QAAMlP,SAAS,GAAGwO,YAAH,aAAGA,YAAH,uBAAGA,YAAY,CAAEU,KAAd,EAAlB;;AACA,QACElP,SAAS,IACT,CAAC3F,2BAA2B,CAACiF,GAA5B,CAAgCtI,MAAhC,CADD,KAEC,CAACA,MAAM,CAACgJ,SAAR,IAAqB,CAACmG,WAAK,CAACiG,MAAN,CAAapM,SAAb,EAAwBhJ,MAAM,CAACgJ,SAA/B,CAFvB,CADF,EAIE;AACAK,MAAAA,gBAAU,CAACyN,MAAX,CAAkB9W,MAAlB,EAA0BgJ,SAA1B;AACD;;AAED,QAAIsO,gBAAgB,EAApB,EAAwB;AACtBP,MAAAA,aAAa;AACb;AACD;AAGD;AACA;;;AACA,QAAIY,uBAAJ,EAA6B;AAE3BvB,MAAAA,4BAA4B;AAC7B;;AAEDA,IAAAA,4BAA4B,CAACe,KAA7B;AACAd,IAAAA,oBAAoB,CAACc,KAArB;AAEAP,IAAAA,qBAAqB;AAErB,QAAMuB,SAAS,GAAGjV,oBAAoB,CAACoF,GAArB,CAAyBtI,MAAzB,CAAlB;AACAkD,IAAAA,oBAAoB,UAApB,CAA4BlD,MAA5B;;AACA,QAAImY,SAAS,KAAKN,SAAlB,EAA6B;AAC3B7X,MAAAA,MAAM,CAAC0X,KAAP,GAAeS,SAAf;AACAnY,MAAAA,MAAM,CAACoY,QAAP;AACD;AACF,GAzHD;;AA2HA,MAAMC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAC3BC,MAD2B;AAG3B,QAAI/B,uBAAJ,EAA6B;AAC3Ba,MAAAA,YAAY,CAACb,uBAAD,CAAZ;AACD;;AAEDA,IAAAA,uBAAuB,GAAGgB,UAAU,CAAC;AACnC1U,MAAAA,YAAY,CAACgG,GAAb,CAAiB7I,MAAjB,EAAyB,KAAzB;AACAmX,MAAAA,KAAK;AACN,KAHmC,EAGjCtB,aAHiC,CAApC;AAID,GAXD;;AAaA,MAAM0C,sBAAsB,GAAG,SAAzBA,sBAAyB,CAC7BD,MAD6B;AAK7BzV,IAAAA,YAAY,CAACgG,GAAb,CAAiB7I,MAAjB,EAAyB,IAAzB;;AAEA,QAAIuW,uBAAJ,EAA6B;AAC3Ba,MAAAA,YAAY,CAACb,uBAAD,CAAZ;AACAA,MAAAA,uBAAuB,GAAG,IAA1B;AACD;AACF,GAXD;;AAaA,MAAMiC,2BAA2B,GAAG,SAA9BA,2BAA8B;QAACC,gFAAY;AAC/C,QAAMC,kBAAkB,GAAGpW,6BAA6B,CAACgG,GAA9B,CAAkCtI,MAAlC,CAA3B;;AACA,QAAI,CAAC0Y,kBAAL,EAAyB;AACvB;AACD;;AAED,QAAIrB,eAAe,MAAMoB,SAAzB,EAAoC;AAClCC,MAAAA,kBAAkB,CAACC,KAAnB,CAAyBjS,OAAzB,GAAmC,MAAnC;AACA;AACD;;AAEDgS,IAAAA,kBAAkB,CAACC,KAAnB,CAAyBC,cAAzB,CAAwC,SAAxC;AACD,GAZD;;AAcA,MAAMC,SAAS,GAAG,SAAZA,SAAY,CAAC7O,IAAD,EAAa8I,IAAb;;AAGhB,QAAMqC,YAAY,6BAAGhS,uBAAuB,CAACmF,GAAxB,CAA4BtI,MAA5B,CAAH,2EAA0C,EAA5D;AACAmD,IAAAA,uBAAuB,CAAC0F,GAAxB,CAA4B7I,MAA5B,EAAoCmV,YAApC;AAEA,QAAM5N,MAAM,GAAGnD,UAAI,CAACsQ,IAAL,CAAU1U,MAAV,EAAkBgK,IAAlB,CAAf;AACA,QAAM8O,GAAG,GAAG3D,YAAY,CAAC4D,SAAb,CAAuB,UAAAC,MAAM;AAAA,aAAI/F,UAAI,CAACmC,MAAL,CAAY4D,MAAM,CAAChP,IAAnB,EAAyBA,IAAzB,CAAJ;AAAA,KAA7B,CAAZ;;AACA,QAAI8O,GAAG,GAAG,CAAV,EAAa;AACX,UAAM7E,UAAU,GAAGL,mBAAmB,CAACrM,MAAM,CAAClB,IAAR,EAAcyM,IAAd,CAAtC;;AACA,UAAImB,UAAJ,EAAgB;AACdkB,QAAAA,YAAY,CAAC8D,IAAb,CAAkB;AAAEjP,UAAAA,IAAI,EAAJA,IAAF;AAAQ8I,UAAAA,IAAI,EAAJA,IAAR;AAAc1K,UAAAA,EAAE,EAAEsO,SAAS;AAA3B,SAAlB;AACD;;AAED8B,MAAAA,2BAA2B;AAC3B;AACD;;AAED,QAAMU,MAAM,GAAGhF,gBAAgB,CAAC3M,MAAM,CAAClB,IAAR,EAAc8O,YAAY,CAAC2D,GAAD,CAAZ,CAAkBhG,IAAhC,EAAsCA,IAAtC,CAA/B;;AACA,QAAI,CAACoG,MAAL,EAAa;AACX/D,MAAAA,YAAY,CAACgE,MAAb,CAAoBL,GAApB,EAAyB,CAAzB;AACAN,MAAAA,2BAA2B;AAC3B;AACD;;AAEDrD,IAAAA,YAAY,CAAC2D,GAAD,CAAZ,uCACK3D,YAAY,CAAC2D,GAAD,CADjB;AAEEhG,MAAAA,IAAI,EAAEoG;AAFR;AAID,GA7BD;;AA+BA,MAAME,cAAc,GAAG,SAAjBA,cAAiB,CACrBlC,GADqB;oFAEY;QAA/BhJ,WAAAA;;AAEFyI,IAAAA,kBAAkB,GAAG,KAArB;AAGAtT,IAAAA,2BAA2B,UAA3B,CAAmCrD,MAAnC;AACAoW,IAAAA,4BAA4B,CAAC6B,MAA7B;AACA5B,IAAAA,oBAAoB,CAAC4B,MAArB;;AAEA,QAAIX,gBAAgB,EAApB,EAAwB;AACtBH,MAAAA,KAAK;AACN;;AAED/T,IAAAA,wBAAwB,CAACyF,GAAzB,CAA6B7I,MAA7B,EAAqC;AAAEkO,MAAAA,EAAE,EAAFA,EAAF;AAAMgJ,MAAAA,GAAG,EAAHA;AAAN,KAArC;AAGA;AACA;;AACAT,IAAAA,eAAe,GAAGc,UAAU,CAACJ,KAAD,CAA5B;AACD,GArBD;;AAuBA,MAAMkC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAC3U,KAAD;;;AAC3B,QAAI8R,cAAJ,EAAoB;AAClBY,MAAAA,YAAY,CAACZ,cAAD,CAAZ;AACAA,MAAAA,cAAc,GAAG,IAAjB;AACD;;AAED,QAAmBf,IAAnB,GAA4B/Q,KAA5B,CAAQ4U,SAAR;AACA,QAAI9E,WAAW,GAAiB,IAAhC;AACA,QAAMjH,IAAI,GACP7I,KAAa,CAACsC,YAAd,IAA8BtC,KAAK,CAAC6I,IAApC,IAA4CsK,SAD/C;;AAGA,QACElB,kBAAkB,KAAK,KAAvB,IACAlB,IAAI,KAAK,YADT,IAEAA,IAAI,KAAK,uBAHX,EAIE;AACAkB,MAAAA,kBAAkB,GAAG,KAArB;AACD;;AAED,gCAA2BjS,KAAa,CAAC1C,eAAd,EAA3B;AAAA;AAAA,QAAKuX,iBAAL;;AACA,QAAIA,iBAAJ,EAAuB;AACrB/E,MAAAA,WAAW,GAAG/M,WAAW,CAACkE,YAAZ,CAAyB3L,MAAzB,EAAiCuZ,iBAAjC,EAAoD;AAChE3N,QAAAA,UAAU,EAAE,KADoD;AAEhEC,QAAAA,aAAa,EAAE;AAFiD,OAApD,CAAd;AAID;AAGD;;;AACA,QAAMnL,MAAM,GAAG+G,WAAW,CAACC,SAAZ,CAAsB1H,MAAtB,CAAf;AACA,QAAMiJ,YAAY,GAAGvI,MAAM,CAACwI,YAAP,EAArB;;AACA,QAAI,CAACsL,WAAD,IAAgBvL,YAApB,EAAkC;AAChCsQ,MAAAA,iBAAiB,GAAGtQ,YAApB;AACAuL,MAAAA,WAAW,GAAG/M,WAAW,CAACkE,YAAZ,CAAyB3L,MAAzB,EAAiCiJ,YAAjC,EAA+C;AAC3D2C,QAAAA,UAAU,EAAE,KAD+C;AAE3DC,QAAAA,aAAa,EAAE;AAF4C,OAA/C,CAAd;AAID;;AAED2I,IAAAA,WAAW,oBAAGA,WAAH,yDAAkBxU,MAAM,CAACgJ,SAApC;;AACA,QAAI,CAACwL,WAAL,EAAkB;AAChB;AACD;AAGD;AACA;AACA;AACA;;;AACA,QAAIgF,YAAY,GAAG,IAAnB;;AAEA,QAAI/D,IAAI,CAAC5G,UAAL,CAAgB,QAAhB,CAAJ,EAA+B;AAC7B,UAAIM,WAAK,CAACoD,UAAN,CAAiBiC,WAAjB,CAAJ,EAAmC;AACjC,2BAAqBrF,WAAK,CAACsK,KAAN,CAAYjF,WAAZ,CAArB;AAAA;AAAA,YAAOlG,MAAP;AAAA,YAAcG,IAAd;;AACA,YAAMiG,KAAI,GAAGtQ,UAAI,CAACsQ,IAAL,CAAU1U,MAAV,EAAkBsO,MAAK,CAACtE,IAAxB,CAAb;;AAEA,YAAI0K,KAAI,CAACrO,IAAL,CAAUvB,MAAV,KAAqBwJ,MAAK,CAACpJ,MAA3B,IAAqCuJ,IAAG,CAACvJ,MAAJ,KAAe,CAAxD,EAA2D;AACzD,cAAMgO,IAAI,GAAG9I,YAAM,CAAC8I,IAAP,CAAYlT,MAAZ,EAAoB;AAC/BkO,YAAAA,EAAE,EAAEI,MAAK,CAACtE,IADqB;AAE/B9C,YAAAA,KAAK,EAAEvD,UAAI,CAACoP;AAFmB,WAApB,CAAb;;AAIA,cAAIG,IAAI,IAAID,UAAI,CAACmC,MAAL,CAAYlC,IAAI,CAAC,CAAD,CAAhB,EAAqBzE,IAAG,CAACzE,IAAzB,CAAZ,EAA4C;AAC1CwK,YAAAA,WAAW,GAAG;AAAEvH,cAAAA,MAAM,EAAEwB,IAAV;AAAerC,cAAAA,KAAK,EAAEqC;AAAtB,aAAd;AACD;AACF;AACF;;AAED,UAAM3I,SAAS,GAAG2P,IAAI,CAACxE,QAAL,CAAc,UAAd,IAA4B,UAA5B,GAAyC,SAA3D;;AACA,0BAAqB9B,WAAK,CAACsK,KAAN,CAAYjF,WAAZ,CAArB;AAAA;AAAA,UAAOlG,KAAP;AAAA,UAAcG,GAAd;;AACA,yBAAqBrE,YAAM,CAACsK,IAAP,CAAY1U,MAAZ,EAAoBsO,KAAK,CAACtE,IAA1B,CAArB;AAAA;AAAA,UAAO0K,IAAP;AAAA,UAAa1K,IAAb;;AAEA,UAAM8I,IAAI,GAAG;AACXzM,QAAAA,IAAI,EAAE,EADK;AAEXiI,QAAAA,KAAK,EAAEA,KAAK,CAACpJ,MAFF;AAGXuJ,QAAAA,GAAG,EAAEA,GAAG,CAACvJ;AAHE,OAAb;AAKA,UAAMiQ,YAAY,GAAGhS,uBAAuB,CAACmF,GAAxB,CAA4BtI,MAA5B,CAArB;AACA,UAAM0Z,oBAAoB,GAAGvE,YAAH,aAAGA,YAAH,uBAAGA,YAAY,CAAEpN,IAAd,CAAmB,UAAAiR,MAAM;AAAA,eACpD/F,UAAI,CAACmC,MAAL,CAAY4D,MAAM,CAAChP,IAAnB,EAAyBA,IAAzB,CADoD;AAAA,OAAzB,CAA7B;AAGA,UAAMqJ,KAAK,GAAGqG,oBAAoB,GAC9B,CAACA,oBAAoB,CAAC5G,IAAtB,EAA4BA,IAA5B,CAD8B,GAE9B,CAACA,IAAD,CAFJ;AAGA,UAAMzM,IAAI,GAAG+M,eAAe,MAAf,UAAgBsB,IAAI,CAACrO,IAArB,SAA8BgN,KAA9B,EAAb;;AAEA,UAAIhN,IAAI,CAACvB,MAAL,KAAgB,CAApB,EAAuB;AACrB;AACA;AACA;AACA0U,QAAAA,YAAY,GAAG,KAAf;AACD;;AAED,UAAIrK,WAAK,CAACoD,UAAN,CAAiBiC,WAAjB,CAAJ,EAAmC;AACjC,YACEgF,YAAY,IACZvG,UAAI,CAACmC,MAAL,CAAYZ,WAAW,CAACvH,MAAZ,CAAmBjD,IAA/B,EAAqCwK,WAAW,CAACpI,KAAZ,CAAkBpC,IAAvD,CAFF,EAGE;AACA,cAAMe,KAAK,GAAG;AAAEf,YAAAA,IAAI,EAAEwK,WAAW,CAACvH,MAAZ,CAAmBjD,IAA3B;AAAiC9E,YAAAA,MAAM,EAAEoJ,KAAK,CAACpJ;AAA/C,WAAd;AACA,cAAMgG,KAAK,GAAGd,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqB+K,KAArB,EAA4BA,KAA5B,CAAd;AACA4O,UAAAA,gBAAgB,CAACzO,KAAD,CAAhB;AAEA,iBAAO2N,SAAS,CAACrE,WAAW,CAACvH,MAAZ,CAAmBjD,IAApB,EAA0B;AACxC3D,YAAAA,IAAI,EAAE,EADkC;AAExCoI,YAAAA,GAAG,EAAEA,GAAG,CAACvJ,MAF+B;AAGxCoJ,YAAAA,KAAK,EAAEA,KAAK,CAACpJ;AAH2B,WAA1B,CAAhB;AAKD;;AAED,eAAOkU,cAAc,CACnB;AAAA,iBAAMhP,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB,EAA8B;AAAE8F,YAAAA,SAAS,EAATA;AAAF,WAA9B,CAAN;AAAA,SADmB,EAEnB;AAAEoI,UAAAA,EAAE,EAAEsG;AAAN,SAFmB,CAArB;AAID;AACF;;AAED,YAAQiB,IAAR;AACE,WAAK,qBAAL;AACA,WAAK,aAAL;AACA,WAAK,cAAL;AAAqB;AACnB,iBAAO2D,cAAc,CAAC;AAAA,mBAAMhP,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB,CAAN;AAAA,WAAD,EAAsC;AACzDkO,YAAAA,EAAE,EAAEsG;AADqD,WAAtC,CAArB;AAGD;;AAED,WAAK,eAAL;AACA,WAAK,sBAAL;AAA6B;AAC3B,8BAAmBA,WAAnB;AAAA,cAAQvH,MAAR,iBAAQA,MAAR;;AACA,cAAIuM,YAAY,IAAIrK,WAAK,CAACG,WAAN,CAAkBkF,WAAlB,CAApB,EAAoD;AAClD,gBAAMoF,UAAU,GAAGxV,UAAI,CAACsQ,IAAL,CAAU1U,MAAV,EAAkBiN,MAAM,CAACjD,IAAzB,CAAnB;;AAEA,gBAAIiD,MAAM,CAAC/H,MAAP,GAAgB0U,UAAU,CAACvT,IAAX,CAAgBvB,MAApC,EAA4C;AAC1C,qBAAO+T,SAAS,CAAC5L,MAAM,CAACjD,IAAR,EAAc;AAC5B3D,gBAAAA,IAAI,EAAE,EADsB;AAE5BiI,gBAAAA,KAAK,EAAErB,MAAM,CAAC/H,MAFc;AAG5BuJ,gBAAAA,GAAG,EAAExB,MAAM,CAAC/H,MAAP,GAAgB;AAHO,eAAd,CAAhB;AAKD;AACF;;AAED,iBAAOkU,cAAc,CAAC;AAAA,mBAAMhP,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB,CAAN;AAAA,WAAD,EAAqC;AACxDkO,YAAAA,EAAE,EAAEsG;AADoD,WAArC,CAArB;AAGD;;AAED,WAAK,uBAAL;AAA8B;AAAA;;AAC5B,8BAAmBA,WAAnB;AAAA,cAAQvH,OAAR,iBAAQA,MAAR,CAD4B;AAI5B;AACA;;AACA,cAAM6M,eAAe,GAAGzV,cAAc,CAACkV,iBAAD,CAAd,GACpBA,iBAAiB,CAACjK,WADE,GAEpB,CAAC,wBAACiK,iBAAD,+CAAC,mBAAmBjH,SAApB,CAFL;;AAIA,cACEkH,YAAY,IACZM,eADA,IAEA3K,WAAK,CAACG,WAAN,CAAkBkF,WAAlB,CAFA,IAGAvH,OAAM,CAAC/H,MAAP,GAAgB,CAJlB,EAKE;AACA,mBAAO2T,SAAS,CAAC5L,OAAM,CAACjD,IAAR,EAAc;AAC5B3D,cAAAA,IAAI,EAAE,EADsB;AAE5BiI,cAAAA,KAAK,EAAErB,OAAM,CAAC/H,MAAP,GAAgB,CAFK;AAG5BuJ,cAAAA,GAAG,EAAExB,OAAM,CAAC/H;AAHgB,aAAd,CAAhB;AAKD;;AAED,iBAAOkU,cAAc,CAAC;AAAA,mBAAMhP,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,CAAN;AAAA,WAAD,EAAsC;AACzDkO,YAAAA,EAAE,EAAEsG;AADqD,WAAtC,CAArB;AAGD;;AAED,WAAK,sBAAL;AAA6B;AAC3B,iBAAO4E,cAAc,CACnB;AACEhP,YAAAA,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,EAA8B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA9B;AACA5P,YAAAA,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB,EAA6B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA7B;AACD,WAJkB,EAKnB;AAAE9L,YAAAA,EAAE,EAAEsG;AAAN,WALmB,CAArB;AAOD;;AAED,WAAK,wBAAL;AAA+B;AAC7B,iBAAO4E,cAAc,CACnB;AAAA,mBAAMhP,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,EAA8B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA9B,CAAN;AAAA,WADmB,EAEnB;AAAE9L,YAAAA,EAAE,EAAEsG;AAAN,WAFmB,CAArB;AAID;;AAED,WAAK,wBAAL;AAA+B;AAC7B,iBAAO4E,cAAc,CACnB;AAAA,mBAAMhP,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,EAA8B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA9B,CAAN;AAAA,WADmB,EAEnB;AAAE9L,YAAAA,EAAE,EAAEsG;AAAN,WAFmB,CAArB;AAID;;AAED,WAAK,uBAAL;AAA8B;AAC5B,iBAAO4E,cAAc,CACnB;AAAA,mBAAMhP,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB,EAA6B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA7B,CAAN;AAAA,WADmB,EAEnB;AAAE9L,YAAAA,EAAE,EAAEsG;AAAN,WAFmB,CAArB;AAID;;AAED,WAAK,uBAAL;AAA8B;AAC5B,iBAAO4E,cAAc,CACnB;AAAA,mBAAMhP,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB,EAA6B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA7B,CAAN;AAAA,WADmB,EAEnB;AAAE9L,YAAAA,EAAE,EAAEsG;AAAN,WAFmB,CAArB;AAID;;AAED,WAAK,oBAAL;AAA2B;AACzB,iBAAO4E,cAAc,CACnB;AAAA,mBAAMhP,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,EAA8B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA9B,CAAN;AAAA,WADmB,EAEnB;AAAE9L,YAAAA,EAAE,EAAEsG;AAAN,WAFmB,CAArB;AAID;;AAED,WAAK,mBAAL;AAA0B;AACxB,iBAAO4E,cAAc,CACnB;AAAA,mBAAMhP,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB,EAA6B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA7B,CAAN;AAAA,WADmB,EAEnB;AAAE9L,YAAAA,EAAE,EAAEsG;AAAN,WAFmB,CAArB;AAID;;AAED,WAAK,iBAAL;AAAwB;AACtB,iBAAO4E,cAAc,CAAC;AAAA,mBAAMhP,YAAM,CAAC6P,eAAP,CAAuBja,MAAvB,CAAN;AAAA,WAAD,EAAuC;AAC1DkO,YAAAA,EAAE,EAAEsG;AADsD,WAAvC,CAArB;AAGD;;AAED,WAAK,iBAAL;AAAwB;AACtB,iBAAO4E,cAAc,CAAC;AAAA,mBAAMhP,YAAM,CAAC8P,WAAP,CAAmBla,MAAnB,CAAN;AAAA,WAAD,EAAmC;AACtDkO,YAAAA,EAAE,EAAEsG;AADkD,WAAnC,CAArB;AAGD;;AACD,WAAK,uBAAL;AACA,WAAK,uBAAL;AACA,WAAK,uBAAL;AACA,WAAK,gBAAL;AACA,WAAK,iBAAL;AACA,WAAK,gBAAL;AACA,WAAK,uBAAL;AACA,WAAK,YAAL;AAAmB;AACjB,cAAIwB,cAAc,CAACzI,IAAD,CAAlB,EAA0B;AACxB,mBAAO6L,cAAc,CAAC;AAAA,qBAAM3R,WAAW,CAAC6F,UAAZ,CAAuBtN,MAAvB,EAA+BuN,IAA/B,CAAN;AAAA,aAAD,EAA6C;AAChEW,cAAAA,EAAE,EAAEsG;AAD4D,aAA7C,CAArB;AAGD;;AAED,cAAInO,KAAI,GAAGkH,IAAH,aAAGA,IAAH,cAAGA,IAAH,GAAW,EAAnB,CAPiB;AAUjB;;;AACA,cAAItK,iCAAiC,CAACqF,GAAlC,CAAsCtI,MAAtC,CAAJ,EAAmD;AACjDqG,YAAAA,KAAI,GAAGA,KAAI,CAAC8T,OAAL,CAAa,QAAb,EAAuB,EAAvB,CAAP;AACD,WAbgB;AAgBjB;AACA;;;AACA,cAAI1E,IAAI,KAAK,YAAT,IAAyB,YAAY9U,IAAZ,CAAiB0F,KAAjB,CAA7B,EAAqD;AACnDA,YAAAA,KAAI,GAAGA,KAAI,CAACqK,KAAL,CAAW,CAAX,EAAc,CAAC,CAAf,CAAP;AACD,WApBgB;AAuBjB;;;AACA,cAAIrK,KAAI,CAACuG,QAAL,CAAc,IAAd,CAAJ,EAAyB;AACvB,mBAAOwM,cAAc,CACnB;AACE,kBAAMgB,KAAK,GAAG/T,KAAI,CAAC9F,KAAL,CAAW,IAAX,CAAd;;AACA6Z,cAAAA,KAAK,CAACxJ,OAAN,CAAc,UAACyJ,IAAD,EAAO9U,CAAP;AACZ,oBAAI8U,IAAJ,EAAU;AACRjQ,kBAAAA,YAAM,CAAC0N,UAAP,CAAkB9X,MAAlB,EAA0Bqa,IAA1B;AACD;;AACD,oBAAI9U,CAAC,KAAK6U,KAAK,CAACtV,MAAN,GAAe,CAAzB,EAA4B;AAC1BsF,kBAAAA,YAAM,CAAC6P,eAAP,CAAuBja,MAAvB;AACD;AACF,eAPD;AAQD,aAXkB,EAYnB;AACEkO,cAAAA,EAAE,EAAEsG;AADN,aAZmB,CAArB;AAgBD;;AAED,cAAIvB,UAAI,CAACmC,MAAL,CAAYZ,WAAW,CAACvH,MAAZ,CAAmBjD,IAA/B,EAAqCwK,WAAW,CAACpI,KAAZ,CAAkBpC,IAAvD,CAAJ,EAAkE;AAChE,gCAAqBmF,WAAK,CAACsK,KAAN,CAAYjF,WAAZ,CAArB;AAAA;AAAA,gBAAOlG,OAAP;AAAA,gBAAcG,KAAd;;AAEA,gBAAMqE,KAAI,GAAG;AACXxE,cAAAA,KAAK,EAAEA,OAAK,CAACpJ,MADF;AAEXuJ,cAAAA,GAAG,EAAEA,KAAG,CAACvJ,MAFE;AAGXmB,cAAAA,IAAI,EAAJA;AAHW,aAAb,CAHgE;AAUhE;AACA;AACA;AACA;AACA;;AACA,gBAAIA,KAAI,IAAIsQ,kBAAR,IAA8BlB,IAAI,KAAK,uBAA3C,EAAoE;AAClE,kBAAM6E,YAAY,GAChB3D,kBAAkB,CAACrI,KAAnB,GAA2BqI,kBAAkB,CAACtQ,IAAnB,CAAwBkU,MAAxB,CAA+B,MAA/B,CAD7B;;AAEA,kBAAMC,YAAY,GAAG1H,KAAI,CAACxE,KAAL,GAAawE,KAAI,CAACzM,IAAL,CAAUkU,MAAV,CAAiB,MAAjB,CAAlC;;AAEA,kBACEC,YAAY,KAAKF,YAAY,GAAG,CAAhC,IACAxH,KAAI,CAACrE,GAAL,KACEkI,kBAAkB,CAACrI,KAAnB,GAA2BqI,kBAAkB,CAACtQ,IAAnB,CAAwBvB,MAHvD,EAIE;AAEAgO,gBAAAA,KAAI,CAACxE,KAAL,IAAc,CAAd;AACAqI,gBAAAA,kBAAkB,GAAG,IAArB;AACA8D,gBAAAA,aAAa;AACd,eATD,MASO;AACL9D,gBAAAA,kBAAkB,GAAG,KAArB;AACD;AACF,aAjBD,MAiBO,IAAIlB,IAAI,KAAK,YAAb,EAA2B;AAChC,kBAAIkB,kBAAkB,KAAK,IAA3B,EAAiC;AAC/BA,gBAAAA,kBAAkB,GAAG7D,KAArB;AACD,eAFD,MAEO,IACL6D,kBAAkB,IAClBxH,WAAK,CAACG,WAAN,CAAkBkF,WAAlB,CADA,IAEAmC,kBAAkB,CAAClI,GAAnB,GAAyBkI,kBAAkB,CAACtQ,IAAnB,CAAwBvB,MAAjD,KACEwJ,OAAK,CAACpJ,MAJH,EAKL;AACAyR,gBAAAA,kBAAkB,uCACbA,kBADa;AAEhBtQ,kBAAAA,IAAI,EAAEsQ,kBAAkB,CAACtQ,IAAnB,GAA0BA;AAFhB,kBAAlB;AAID,eAVM,MAUA;AACLsQ,gBAAAA,kBAAkB,GAAG,KAArB;AACD;AACF,aAhBM,MAgBA;AACLA,cAAAA,kBAAkB,GAAG,KAArB;AACD;;AAED,gBAAI6C,YAAJ,EAAkB;AAChBX,cAAAA,SAAS,CAACvK,OAAK,CAACtE,IAAP,EAAa8I,KAAb,CAAT;AACA;AACD;AACF;;AAED,iBAAOsG,cAAc,CAAC;AAAA,mBAAMhP,YAAM,CAAC0N,UAAP,CAAkB9X,MAAlB,EAA0BqG,KAA1B,CAAN;AAAA,WAAD,EAAwC;AAC3D6H,YAAAA,EAAE,EAAEsG;AADuD,WAAxC,CAArB;AAGD;AAvOH;AAyOD,GA5VD;;AA8VA,MAAM8C,gBAAgB,GAAG,SAAnBA,gBAAmB;AACvB,WAAO,CAAC,CAAClU,wBAAwB,CAACkF,GAAzB,CAA6BtI,MAA7B,CAAT;AACD,GAFD;;AAIA,MAAMqX,eAAe,GAAG,SAAlBA,eAAkB;;;AACtB,WAAO,CAAC,4BAAClU,uBAAuB,CAACmF,GAAxB,CAA4BtI,MAA5B,CAAD,mDAAC,uBAAqC8E,MAAtC,CAAR;AACD,GAFD;;AAIA,MAAM4V,iBAAiB,GAAG,SAApBA,iBAAoB;AACxB,WAAOpD,gBAAgB,MAAMD,eAAe,EAA5C;AACD,GAFD;;AAIA,MAAMsD,UAAU,GAAG,SAAbA,UAAa;AACjB,WAAOrE,QAAP;AACD,GAFD;;AAIA,MAAMqD,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACzO,KAAD;AACvB7H,IAAAA,2BAA2B,CAACwF,GAA5B,CAAgC7I,MAAhC,EAAwCkL,KAAxC;;AAEA,QAAIsL,cAAJ,EAAoB;AAClBY,MAAAA,YAAY,CAACZ,cAAD,CAAZ;AACAA,MAAAA,cAAc,GAAG,IAAjB;AACD;;AAED,QAAQxN,SAAR,GAAsBhJ,MAAtB,CAAQgJ,SAAR;;AACA,QAAI,CAACkC,KAAL,EAAY;AACV;AACD;;AAED,QAAM0P,WAAW,GACf,CAAC5R,SAAD,IAAc,CAACiK,UAAI,CAACmC,MAAL,CAAYpM,SAAS,CAACiE,MAAV,CAAiBjD,IAA7B,EAAmCkB,KAAK,CAAC+B,MAAN,CAAajD,IAAhD,CADjB;AAEA,QAAM6Q,iBAAiB,GACrB,CAAC7R,SAAD,IACA,CAACiK,UAAI,CAACmC,MAAL,CACCpM,SAAS,CAACiE,MAAV,CAAiBjD,IAAjB,CAAsB0G,KAAtB,CAA4B,CAA5B,EAA+B,CAAC,CAAhC,CADD,EAECxF,KAAK,CAAC+B,MAAN,CAAajD,IAAb,CAAkB0G,KAAlB,CAAwB,CAAxB,EAA2B,CAAC,CAA5B,CAFD,CAFH;;AAOA,QAAKkK,WAAW,IAAIjE,kBAAhB,IAAuCkE,iBAA3C,EAA8D;AAC5DlE,MAAAA,kBAAkB,GAAG,KAArB;AACD;;AAED,QAAIiE,WAAW,IAAIvD,eAAe,EAAlC,EAAsC;AACpCb,MAAAA,cAAc,GAAGe,UAAU,CAACJ,KAAD,EAAQrB,WAAR,CAA3B;AACD;AACF,GA7BD;;AA+BA,MAAMgF,WAAW,GAAG,SAAdA,WAAc;AAClB,QAAIxD,gBAAgB,MAAM,CAACD,eAAe,EAA1C,EAA8C;AAE5CF,MAAAA,KAAK;AACN;AACF,GALD;;AAOA,MAAM4D,aAAa,GAAG,SAAhBA,aAAgB,CAACC,CAAD;AACpB;AACA;AACA;AACA;AACA;AACA,QAAI,CAAC3D,eAAe,EAApB,EAAwB;AACtBmB,MAAAA,2BAA2B,CAAC,IAAD,CAA3B;AACAjB,MAAAA,UAAU,CAACiB,2BAAD,CAAV;AACD;AACF,GAVD;;AAYA,MAAMiC,aAAa,GAAG,SAAhBA,aAAgB;AACpB,QAAI,CAACnD,gBAAgB,EAArB,EAAyB;AACvBb,MAAAA,eAAe,GAAGc,UAAU,CAACJ,KAAD,CAA5B;AACD;AACF,GAJD;;AAMA,MAAM8D,kBAAkB,GAAG,SAArBA,kBAAqB,CAACC,SAAD;AACzB,QAAI7D,eAAe,MAAMC,gBAAgB,EAAzC,EAA6C;AAC3C;AACD;;AAED,QACE4D,SAAS,CAACC,IAAV,CAAe,UAAA9T,QAAQ;AAAA,aAAID,iBAAiB,CAACpH,MAAD,EAASqH,QAAT,EAAmB6T,SAAnB,CAArB;AAAA,KAAvB,CADF,EAEE;AAAA;;AACA;AACA;AACA,+BAAA5X,sBAAsB,CAACgF,GAAvB,CAA2BtI,MAA3B;AACD;AACF,GAZD;;AAcA,SAAO;AACLmX,IAAAA,KAAK,EAALA,KADK;AAELsD,IAAAA,aAAa,EAAbA,aAFK;AAILpD,IAAAA,eAAe,EAAfA,eAJK;AAKLC,IAAAA,gBAAgB,EAAhBA,gBALK;AAMLoD,IAAAA,iBAAiB,EAAjBA,iBANK;AAQLC,IAAAA,UAAU,EAAVA,UARK;AAULhB,IAAAA,gBAAgB,EAAhBA,gBAVK;AAWLtB,IAAAA,oBAAoB,EAApBA,oBAXK;AAYLE,IAAAA,sBAAsB,EAAtBA,sBAZK;AAaLc,IAAAA,oBAAoB,EAApBA,oBAbK;AAcL0B,IAAAA,aAAa,EAAbA,aAdK;AAgBLE,IAAAA,kBAAkB,EAAlBA,kBAhBK;AAiBLH,IAAAA,WAAW,EAAXA;AAjBK,GAAP;AAmBD;;SC1xBeM;AACd,MAAMC,YAAY,GAAGC,YAAM,CAAC,KAAD,CAA3B;AAEAC,EAAAA,eAAS,CAAC;AACRF,IAAAA,YAAY,CAACrK,OAAb,GAAuB,IAAvB;AACA,WAAO;AACLqK,MAAAA,YAAY,CAACrK,OAAb,GAAuB,KAAvB;AACD,KAFD;AAGD,GALQ,EAKN,EALM,CAAT;AAOA,SAAOqK,YAAY,CAACrK,OAApB;AACD;;ACVD;;;;AAIO,IAAMwK,yBAAyB,GAAG/Z,WAAW,GAChDga,qBADgD,GAEhDF,eAFG;;SCJSG,oBACdzW,MACA0W,UACArP;AAEA,kBAA2BsP,cAAQ,CAAC;AAAA,WAAM,IAAIC,gBAAJ,CAAqBF,QAArB,CAAN;AAAA,GAAD,CAAnC;AAAA;AAAA,MAAOG,gBAAP;;AAEAN,EAAAA,yBAAyB,CAAC;AACxB;AACA;AACAM,IAAAA,gBAAgB,CAACC,WAAjB;AACD,GAJwB,CAAzB;AAMAR,EAAAA,eAAS,CAAC;AACR,QAAI,CAACtW,IAAI,CAAC+L,OAAV,EAAmB;AACjB,YAAM,IAAI9Q,KAAJ,CAAU,wDAAV,CAAN;AACD;;AAED4b,IAAAA,gBAAgB,CAACE,OAAjB,CAAyB/W,IAAI,CAAC+L,OAA9B,EAAuC1E,OAAvC;AACA,WAAO;AAAA,aAAMwP,gBAAgB,CAACG,UAAjB,EAAN;AAAA,KAAP;AACD,GAPQ,EAON,CAACH,gBAAD,EAAmB7W,IAAnB,EAAyBqH,OAAzB,CAPM,CAAT;AAQD;;;;;;;ACND,IAAM4P,0BAAwB,GAAyB;AACrDC,EAAAA,OAAO,EAAE,IAD4C;AAErDC,EAAAA,SAAS,EAAE,IAF0C;AAGrDC,EAAAA,aAAa,EAAE;AAHsC,CAAvD;AAMO,IAAMC,sBAAsB,GAAG,CAACvb,UAAD,GAClC;AAAA,SAAM,IAAN;AAAA,CADkC,GAElC;MAAGkE,YAAAA;MAASqH;;AACV,MAAI,CAACvL,UAAL,EAAiB;AACf,WAAO,IAAP;AACD;;AAED,MAAMf,MAAM,GAAGD,cAAc,EAA7B;AACA,MAAMwc,SAAS,GAAGnB,YAAY,EAA9B;;AAEA,kBAAuBQ,cAAQ,CAAC;AAAA,WAC9BzF,yBAAyB;AACvBnW,MAAAA,MAAM,EAANA;AADuB,OAEpBsM,OAFoB,EADK;AAAA,GAAD,CAA/B;AAAA;AAAA,MAAOkQ,YAAP;;AAOAd,EAAAA,mBAAmB,CACjBzW,IADiB,EAEjBuX,YAAY,CAACvB,kBAFI,EAGjBiB,0BAHiB,CAAnB;AAMAlZ,EAAAA,wBAAwB,CAAC6F,GAAzB,CAA6B7I,MAA7B,EAAqCwc,YAAY,CAAC/B,aAAlD;;AACA,MAAI8B,SAAJ,EAAe;AACbC,IAAAA,YAAY,CAACrF,KAAb;AACD;;AAED,SAAOqF,YAAP;AACD,CA7BE;;;;ACrBA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,IAAD,EAAWC,IAAX;AAAA,SAC5BC,MAAM,CAACC,IAAP,CAAYH,IAAZ,EAAkB5X,MAAlB,KAA6B8X,MAAM,CAACC,IAAP,CAAYF,IAAZ,EAAkB7X,MAA/C,IACA8X,MAAM,CAACC,IAAP,CAAYH,IAAZ,EAAkBI,KAAlB,CACE,UAAA/Q,GAAG;AAAA,WAAI4Q,IAAI,CAACI,cAAL,CAAoBhR,GAApB,KAA4B2Q,IAAI,CAAC3Q,GAAD,CAAJ,KAAc4Q,IAAI,CAAC5Q,GAAD,CAAlD;AAAA,GADL,CAF4B;AAAA,CAAvB;;AAMP,IAAMiR,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAC9R,KAAD,EAAe+R,KAAf;AAC7B,EAAqE/R,KAArE,CAAQ+B,MAAR;AAAA,MAAqE/B,KAArE,CAA6BkB,KAA7B;AAAA,UAAmD8Q,aAAnD,4BAAqEhS,KAArE;;AACA,EAAqE+R,KAArE,CAAQhQ,MAAR;AAAA,MAAqEgQ,KAArE,CAA6B7Q,KAA7B;AAAA,UAAmD+Q,aAAnD,4BAAqEF,KAArE;;AAEA,SACE/R,KAAK,CAAC3H,kBAAD,CAAL,KAA8B0Z,KAAK,CAAC1Z,kBAAD,CAAnC,IACAkZ,cAAc,CAACS,aAAD,EAAgBC,aAAhB,CAFhB;AAID,CARD;AAUA;;;;;;;;;AAQO,IAAMC,yBAAyB,GAAG,SAA5BA,yBAA4B,CACvCC,IADuC,EAEvC5J,OAFuC;AAIvC,MAAI4J,IAAI,CAACvY,MAAL,KAAgB2O,OAAO,CAAC3O,MAA5B,EAAoC;AAClC,WAAO,KAAP;AACD;;AAED,OAAK,IAAIS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8X,IAAI,CAACvY,MAAzB,EAAiCS,CAAC,EAAlC,EAAsC;AACpC,QAAM2F,KAAK,GAAGmS,IAAI,CAAC9X,CAAD,CAAlB;AACA,QAAM0X,KAAK,GAAGxJ,OAAO,CAAClO,CAAD,CAArB;;AAEA,QAAI,CAAC4J,WAAK,CAACiG,MAAN,CAAalK,KAAb,EAAoB+R,KAApB,CAAD,IAA+B,CAACD,sBAAsB,CAAC9R,KAAD,EAAQ+R,KAAR,CAA1D,EAA0E;AACxE,aAAO,KAAP;AACD;AACF;;AAED,SAAO,IAAP;AACD,CAlBM;AAoBP;;;;;;;;AAQO,IAAMK,sBAAsB,GAAG,SAAzBA,sBAAyB,CACpCD,IADoC,EAEpC5J,OAFoC;AAIpC,MAAI4J,IAAI,CAACvY,MAAL,KAAgB2O,OAAO,CAAC3O,MAA5B,EAAoC;AAClC,WAAO,KAAP;AACD;;AAED,OAAK,IAAIS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8X,IAAI,CAACvY,MAAzB,EAAiCS,CAAC,EAAlC,EAAsC;AACpC,QAAM2F,KAAK,GAAGmS,IAAI,CAAC9X,CAAD,CAAlB;AACA,QAAM0X,KAAK,GAAGxJ,OAAO,CAAClO,CAAD,CAArB,CAFoC;;AAKpC,QACE2F,KAAK,CAAC+B,MAAN,CAAa/H,MAAb,KAAwB+X,KAAK,CAAChQ,MAAN,CAAa/H,MAArC,IACAgG,KAAK,CAACkB,KAAN,CAAYlH,MAAZ,KAAuB+X,KAAK,CAAC7Q,KAAN,CAAYlH,MADnC,IAEA,CAAC8X,sBAAsB,CAAC9R,KAAD,EAAQ+R,KAAR,CAHzB,EAIE;AACA,aAAO,KAAP;AACD;AACF;;AAED,SAAO,IAAP;AACD,CAvBM;;AC/CP;;;;AAIA,IAAMM,MAAM,GAAG,SAATA,MAAS,CAACC,KAAD;AAMb,MAAQpY,MAAR,GAAuCoY,KAAvC,CAAQpY,MAAR;AAAA,MAAgBsP,IAAhB,GAAuC8I,KAAvC,CAAgB9I,IAAhB;AAAA,MAAsB/O,MAAtB,GAAuC6X,KAAvC,CAAsB7X,MAAtB;AAAA,MAA8BU,IAA9B,GAAuCmX,KAAvC,CAA8BnX,IAA9B;AACA,MAAMrG,MAAM,GAAGD,cAAc,EAA7B;AACA,MAAMiK,IAAI,GAAGvC,WAAW,CAACwC,QAAZ,CAAqBjK,MAArB,EAA6BqG,IAA7B,CAAb;AACA,MAAMoX,UAAU,GAAGxK,UAAI,CAACtN,MAAL,CAAYqE,IAAZ,CAAnB;AACA,MAAM0T,iBAAiB,GAAGhJ,IAAI,CAACjR,uBAAD,CAAJ,KAAkC,IAA5D;AAGA;;AACA,MAAIzD,MAAM,CAACqK,MAAP,CAAc1E,MAAd,CAAJ,EAA2B;AACzB,wBAAOtF,uCAAA,CAACsd,eAAD;AAAiB7Y,MAAAA,MAAM,EAAEV,UAAI,CAACwZ,MAAL,CAAYjY,MAAZ,EAAoBb;KAA7C,CAAP;AACD;AAGD;AACA;;;AACA,MACE4P,IAAI,CAACrO,IAAL,KAAc,EAAd,IACAV,MAAM,CAACmM,QAAP,CAAgBnM,MAAM,CAACmM,QAAP,CAAgBhN,MAAhB,GAAyB,CAAzC,MAAgDuB,IADhD,IAEA,CAACrG,MAAM,CAACyK,QAAP,CAAgB9E,MAAhB,CAFD,IAGAyE,YAAM,CAACwT,MAAP,CAAc5d,MAAd,EAAsByd,UAAtB,MAAsC,EAJxC,EAKE;AACA,wBAAOpd,uCAAA,CAACsd,eAAD;AAAiBE,MAAAA,WAAW;AAACH,MAAAA,iBAAiB,EAAEA;KAAhD,CAAP;AACD;AAGD;AACA;;;AACA,MAAIhJ,IAAI,CAACrO,IAAL,KAAc,EAAlB,EAAsB;AACpB,wBAAOhG,uCAAA,CAACsd,eAAD;AAAiBD,MAAAA,iBAAiB,EAAEA;KAApC,CAAP;AACD;AAGD;;;AACA,MAAItY,MAAM,IAAIsP,IAAI,CAACrO,IAAL,CAAUqK,KAAV,CAAgB,CAAC,CAAjB,MAAwB,IAAtC,EAA4C;AAC1C,wBAAOrQ,uCAAA,CAACyd,UAAD;AAAYC,MAAAA,UAAU;AAAC1X,MAAAA,IAAI,EAAEqO,IAAI,CAACrO;KAAlC,CAAP;AACD;;AAED,sBAAOhG,uCAAA,CAACyd,UAAD;AAAYzX,IAAAA,IAAI,EAAEqO,IAAI,CAACrO;GAAvB,CAAP;AACD,CA5CD;AA8CA;;;;;AAGA,IAAMyX,UAAU,GAAG,SAAbA,UAAa,CAACN,KAAD;AACjB,MAAQnX,IAAR,GAAqCmX,KAArC,CAAQnX,IAAR;AAAA,0BAAqCmX,KAArC,CAAcO,UAAd;AAAA,MAAcA,UAAd,kCAA2B,KAA3B;AACA,MAAMC,GAAG,GAAG1C,YAAM,CAAkB,IAAlB,CAAlB;;AACA,MAAM2C,cAAc,GAAG,SAAjBA,cAAiB;AACrB,qBAAU5X,IAAV,aAAUA,IAAV,cAAUA,IAAV,GAAkB,EAAlB,SAAuB0X,UAAU,GAAG,IAAH,GAAU,EAA3C;AACD,GAFD;;AAGA,kBAAsBnC,cAAQ,CAACqC,cAAD,CAA9B;AAAA;AAAA,MAAOC,WAAP;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AACA1C,EAAAA,yBAAyB,CAAC;AACxB;AACA,QAAM2C,gBAAgB,GAAGF,cAAc,EAAvC;;AAEA,QAAID,GAAG,CAAChN,OAAJ,IAAegN,GAAG,CAAChN,OAAJ,CAAYvL,WAAZ,KAA4B0Y,gBAA/C,EAAiE;AAC/DH,MAAAA,GAAG,CAAChN,OAAJ,CAAYvL,WAAZ,GAA0B0Y,gBAA1B;AACD;AAGD;;AACD,GAVwB,CAAzB;AAaA;;AACA,sBAAO9d,uCAAA,CAAC+d,cAAD;AAAcJ,IAAAA,GAAG,EAAEA;GAAnB,EAAyBE,WAAzB,CAAP;AACD,CAhCD;;AAkCA,IAAME,cAAY,gBAAGC,UAAI,eACvBC,gBAAU,CAAwC,UAACd,KAAD,EAAQQ,GAAR;AAChD,sBACE3d,uCAAA,OAAA;;AAAwB2d,IAAAA,GAAG,EAAEA;GAA7B,EACGR,KAAK,CAAC1L,QADT,CADF;AAKD,CANS,CADa,CAAzB;AAUA;;;;AAIO,IAAM6L,eAAe,GAAG,SAAlBA,eAAkB,CAACH,KAAD;AAK7B,sBAAuEA,KAAvE,CAAQ1Y,MAAR;AAAA,MAAQA,MAAR,8BAAiB,CAAjB;AAAA,2BAAuE0Y,KAAvE,CAAoBK,WAApB;AAAA,MAAoBA,WAApB,mCAAkC,KAAlC;AAAA,8BAAuEL,KAAvE,CAAyCE,iBAAzC;AAAA,MAAyCA,iBAAzC,sCAA6D,KAA7D;AAEA,MAAMa,UAAU,GAAG;AACjB,6BAAyBV,WAAW,GAAG,GAAH,GAAS,GAD5B;AAEjB,yBAAqB/Y;AAFJ,GAAnB;;AAKA,MAAI4Y,iBAAJ,EAAuB;AACrBa,IAAAA,UAAU,CAAC,6BAAD,CAAV,GAA4C,IAA5C;AACD;;AAED,sBACEle,uCAAA,OAAA,oBAAUke,WAAV,EACG,CAACxd,UAAD,IAAe,CAAC8c,WAAhB,GAA8B,QAA9B,GAAyC,IAD5C,EAEGA,WAAW,gBAAGxd,uCAAA,KAAA,MAAA,CAAH,GAAY,IAF1B,CADF;AAMD,CAtBM;;AC1FP,SAASme,mCAAT,CACEC,yBADF,EAEEC,eAFF;AAIE,MAAID,yBAAyB,CAACzN,OAA9B,EAAuC;AACrCyN,IAAAA,yBAAyB,CAACzN,OAA1B,CAAkCiL,UAAlC;;AACA,QAAIyC,eAAJ,EAAqB;AACnBD,MAAAA,yBAAyB,CAACzN,OAA1B,GAAoC,IAApC;AACD;AACF;AACF;;AAID,SAAS2N,eAAT,CAAyBC,UAAzB;AACE,MAAIA,UAAU,CAAC5N,OAAf,EAAwB;AACtBoG,IAAAA,YAAY,CAACwH,UAAU,CAAC5N,OAAZ,CAAZ;AACA4N,IAAAA,UAAU,CAAC5N,OAAX,GAAqB,IAArB;AACD;AACF;AAED;;;;;AAGA,IAAM6N,IAAI,GAAG,SAAPA,IAAO,CAACrB,KAAD;AAQX,MACE9I,IADF,GAOI8I,KAPJ,CACE9I,IADF;AAAA,MAEEtP,MAFF,GAOIoY,KAPJ,CAEEpY,MAFF;AAAA,MAGEiB,IAHF,GAOImX,KAPJ,CAGEnX,IAHF;AAAA,MAIEV,MAJF,GAOI6X,KAPJ,CAIE7X,MAJF;AAAA,MAKEmZ,iBALF,GAOItB,KAPJ,CAKEsB,iBALF;AAAA,0BAOItB,KAPJ,CAMEuB,UANF;AAAA,MAMEA,UANF,kCAMe,UAACvB,KAAD;AAAA,wBAA4Bnd,uCAAA,CAAC2e,WAAD,oBAAiBxB,MAAjB,CAA5B;AAAA,GANf;AASA,MAAMxd,MAAM,GAAGD,cAAc,EAA7B;AACA,MAAM0e,yBAAyB,GAAGnD,YAAM,CAAwB,IAAxB,CAAxC;AACA,MAAM2D,cAAc,GAAG3D,YAAM,CAAqB,IAArB,CAA7B;;AACA,kBAA8CM,cAAQ,CAAC,KAAD,CAAtD;AAAA;AAAA,MAAOsD,eAAP;AAAA,MAAwBC,kBAAxB;;AACA,MAAMC,yBAAyB,GAAG9D,YAAM,CAAU,IAAV,CAAxC;AAEA,MAAM+D,sBAAsB,GAAGC,iBAAW,CACxC,UAACC,aAAD;AACEf,IAAAA,mCAAmC,CACjCC,yBADiC,EAEjCc,aAAa,IAAI,IAFgB,CAAnC;;AAKA,QAAIA,aAAa,IAAI,IAArB,EAA2B;AAAA;;AACzBjd,MAAAA,6BAA6B,UAA7B,CAAqCtC,MAArC;AACA,+BAAA0U,IAAI,CAAC8K,mBAAL,qFAAA9K,IAAI,EAAuB,IAAvB,CAAJ;AACD,KAHD,MAGO;AACLpS,MAAAA,6BAA6B,CAACuG,GAA9B,CAAkC7I,MAAlC,EAA0Cuf,aAA1C;;AAEA,UAAI,CAACd,yBAAyB,CAACzN,OAA/B,EAAwC;AACtC;AACA,YAAMyO,cAAc,GAAG/e,MAAM,CAAC+e,cAAP,IAAyBC,6BAAhD;AACAjB,QAAAA,yBAAyB,CAACzN,OAA1B,GAAoC,IAAIyO,cAAJ,CAAmB;;;AACrD,oCAAA/K,IAAI,CAAC8K,mBAAL,uFAAA9K,IAAI,EAAuB6K,aAAvB,CAAJ;AACD,SAFmC,CAApC;AAGD;;AACDd,MAAAA,yBAAyB,CAACzN,OAA1B,CAAkCgL,OAAlC,CAA0CuD,aAA1C;AACAN,MAAAA,cAAc,CAACjO,OAAf,GAAyBuO,aAAzB;AACD;AACF,GAvBuC,EAwBxC,CAACN,cAAD,EAAiBvK,IAAjB,EAAuB1U,MAAvB,CAxBwC,CAA1C;AA2BA,MAAI8R,QAAQ,gBACVzR,uCAAA,CAACkd,MAAD;AAAQnY,IAAAA,MAAM,EAAEA;AAAQsP,IAAAA,IAAI,EAAEA;AAAM/O,IAAAA,MAAM,EAAEA;AAAQU,IAAAA,IAAI,EAAEA;GAA1D,CADF;AAIA,MAAMsZ,iBAAiB,GAAGjL,IAAI,CAACnR,kBAAD,CAA9B;AACAgY,EAAAA,eAAS,CAAC;AACR,QAAIoE,iBAAJ,EAAuB;AACrB,UAAI,CAACP,yBAAyB,CAACpO,OAA/B,EAAwC;AACtC;AACAoO,QAAAA,yBAAyB,CAACpO,OAA1B,GAAoCuG,UAAU,CAAC;AAC7C4H,UAAAA,kBAAkB,CAAC,IAAD,CAAlB;AACAC,UAAAA,yBAAyB,CAACpO,OAA1B,GAAoC,IAApC;AACD,SAH6C,EAG3C,GAH2C,CAA9C;AAID;AACF,KARD,MAQO;AACL2N,MAAAA,eAAe,CAACS,yBAAD,CAAf;AACAD,MAAAA,kBAAkB,CAAC,KAAD,CAAlB;AACD;;AACD,WAAO;AAAA,aAAMR,eAAe,CAACS,yBAAD,CAArB;AAAA,KAAP;AACD,GAdQ,EAcN,CAACO,iBAAD,EAAoBR,kBAApB,CAdM,CAAT;;AAgBA,MAAIQ,iBAAiB,IAAIT,eAAzB,EAA0C;AACxC,QAAMU,gBAAgB,GAA2B;AAC/C9N,MAAAA,QAAQ,EAAE4C,IAAI,CAACmL,WADgC;AAE/CtB,MAAAA,UAAU,EAAE;AACV,kCAA0B,IADhB;AAEV5F,QAAAA,KAAK,EAAE;AACLtN,UAAAA,QAAQ,EAAE,UADL;AAELT,UAAAA,GAAG,EAAE,CAFA;AAGLkV,UAAAA,aAAa,EAAE,MAHV;AAILnV,UAAAA,KAAK,EAAE,MAJF;AAKLoV,UAAAA,QAAQ,EAAE,MALL;AAMLrZ,UAAAA,OAAO,EAAE,OANJ;AAOLsZ,UAAAA,OAAO,EAAE,OAPJ;AAQLC,UAAAA,UAAU,EAAE,MARP;AASLC,UAAAA,cAAc,EAAE,MATX;AAUL;AACAC,UAAAA,gBAAgB,EAAElf,SAAS,GAAG,SAAH,GAAe4W;AAXrC,SAFG;AAeVuI,QAAAA,eAAe,EAAE,KAfP;AAgBVpC,QAAAA,GAAG,EAAEqB;AAhBK;AAFmC,KAAjD;AAsBAvN,IAAAA,QAAQ,gBACNzR,uCAAA,CAACA,yBAAK,CAACggB,QAAP,MAAA,EACGvB,iBAAiB,CAACc,gBAAD,CADpB,EAEG9N,QAFH,CADF;AAMD;AAGD;AACA;;;AACA,MAAMyM,UAAU,GAEZ;AACF,uBAAmB;AADjB,GAFJ;AAMA,SAAOQ,UAAU,CAAC;AAAER,IAAAA,UAAU,EAAVA,UAAF;AAAczM,IAAAA,QAAQ,EAARA,QAAd;AAAwB4C,IAAAA,IAAI,EAAJA,IAAxB;AAA8BrO,IAAAA,IAAI,EAAJA;AAA9B,GAAD,CAAjB;AACD,CAhHD;;AAkHA,IAAMia,YAAY,gBAAGjgB,yBAAK,CAACge,IAAN,CAAWQ,IAAX,EAAiB,UAAC0B,IAAD,EAAOrN,IAAP;AACpC,SACEA,IAAI,CAACvN,MAAL,KAAgB4a,IAAI,CAAC5a,MAArB,IACAuN,IAAI,CAAC9N,MAAL,KAAgBmb,IAAI,CAACnb,MADrB,IAEA8N,IAAI,CAAC6L,UAAL,KAAoBwB,IAAI,CAACxB,UAFzB,IAGA7L,IAAI,CAAC4L,iBAAL,KAA2ByB,IAAI,CAACzB,iBAHhC,IAIA5L,IAAI,CAAC7M,IAAL,KAAcka,IAAI,CAACla,IAJnB,IAKA1C,UAAI,CAACyR,MAAL,CAAYlC,IAAI,CAACwB,IAAjB,EAAuB6L,IAAI,CAAC7L,IAA5B,CALA,IAMAxB,IAAI,CAACwB,IAAL,CAAUnR,kBAAV,MAAkCgd,IAAI,CAAC7L,IAAL,CAAUnR,kBAAV,CAPpC;AASD,CAVoB,CAArB;IAYayb,WAAW,GAAG,SAAdA,WAAc,CAACxB,KAAD;AACzB,MAAQe,UAAR,GAAiCf,KAAjC,CAAQe,UAAR;AAAA,MAAoBzM,QAApB,GAAiC0L,KAAjC,CAAoB1L,QAApB;AACA,sBAAOzR,uCAAA,OAAA,oBAAUke,WAAV,EAAuBzM,QAAvB,CAAP;AACD;;AChKD;;;;AAIA,IAAMnO,IAAI,GAAG,SAAPA,IAAO,CAAC6Z,KAAD;AAQX,MACEgD,WADF,GAOIhD,KAPJ,CACEgD,WADF;AAAA,MAEEpb,MAFF,GAOIoY,KAPJ,CAEEpY,MAFF;AAAA,MAGEO,MAHF,GAOI6X,KAPJ,CAGE7X,MAHF;AAAA,MAIEmZ,iBAJF,GAOItB,KAPJ,CAIEsB,iBAJF;AAAA,MAKEC,UALF,GAOIvB,KAPJ,CAKEuB,UALF;AAAA,MAME1Y,IANF,GAOImX,KAPJ,CAMEnX,IANF;AAQA,MAAMrG,MAAM,GAAGD,cAAc,EAA7B;AACA,MAAMie,GAAG,GAAG1C,YAAM,CAAyB,IAAzB,CAAlB;AACA,MAAMmF,MAAM,GAAGC,UAAS,CAACF,WAAV,CAAsBna,IAAtB,EAA4Bma,WAA5B,CAAf;AACA,MAAMzU,GAAG,GAAGtE,WAAW,CAACqE,OAAZ,CAAoB9L,MAApB,EAA4BqG,IAA5B,CAAZ;AACA,MAAMyL,QAAQ,GAAG,EAAjB;;AAEA,OAAK,IAAIvM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkb,MAAM,CAAC3b,MAA3B,EAAmCS,CAAC,EAApC,EAAwC;AACtC,QAAMmP,IAAI,GAAG+L,MAAM,CAAClb,CAAD,CAAnB;AAEAuM,IAAAA,QAAQ,CAACmH,IAAT,eACE5Y,uCAAA,CAACwe,YAAD;AACEzZ,MAAAA,MAAM,EAAEA,MAAM,IAAIG,CAAC,KAAKkb,MAAM,CAAC3b,MAAP,GAAgB;AACxCiH,MAAAA,GAAG,YAAKA,GAAG,CAAC3D,EAAT,cAAe7C,CAAf;AACHuZ,MAAAA,iBAAiB,EAAEA;AACnBpK,MAAAA,IAAI,EAAEA;AACNrO,MAAAA,IAAI,EAAEA;AACNV,MAAAA,MAAM,EAAEA;AACRoZ,MAAAA,UAAU,EAAEA;KAPd,CADF;AAWD;;;AAGD,MAAM4B,WAAW,GAAGrB,iBAAW,CAC7B,UAACsB,IAAD;AACE,QAAM5S,cAAc,GAAGtL,wBAAwB,CAAC4F,GAAzB,CAA6BtI,MAA7B,CAAvB;;AACA,QAAI4gB,IAAJ,EAAU;AACR5S,MAAAA,cAAc,SAAd,IAAAA,cAAc,WAAd,YAAAA,cAAc,CAAEnF,GAAhB,CAAoBkD,GAApB,EAAyB6U,IAAzB;AACApe,MAAAA,eAAe,CAACqG,GAAhB,CAAoBxC,IAApB,EAA0Bua,IAA1B;AACAre,MAAAA,eAAe,CAACsG,GAAhB,CAAoB+X,IAApB,EAA0Bva,IAA1B;AACD,KAJD,MAIO;AACL2H,MAAAA,cAAc,SAAd,IAAAA,cAAc,WAAd,YAAAA,cAAc,UAAd,CAAuBjC,GAAvB;AACAvJ,MAAAA,eAAe,UAAf,CAAuB6D,IAAvB;;AACA,UAAI2X,GAAG,CAAChN,OAAR,EAAiB;AACfzO,QAAAA,eAAe,UAAf,CAAuByb,GAAG,CAAChN,OAA3B;AACD;AACF;;AACDgN,IAAAA,GAAG,CAAChN,OAAJ,GAAc4P,IAAd;AACD,GAf4B,EAgB7B,CAAC5C,GAAD,EAAMhe,MAAN,EAAc+L,GAAd,EAAmB1F,IAAnB,CAhB6B,CAA/B;AAkBA,sBACEhG,uCAAA,OAAA;uBAAsB;AAAO2d,IAAAA,GAAG,EAAE2C;GAAlC,EACG7O,QADH,CADF;AAKD,CA9DD;;AAgEA,IAAMsM,YAAY,gBAAG/d,yBAAK,CAACge,IAAN,CAAW1a,IAAX,EAAiB,UAAC4c,IAAD,EAAOrN,IAAP;AACpC,SACEA,IAAI,CAACvN,MAAL,KAAgB4a,IAAI,CAAC5a,MAArB,IACAuN,IAAI,CAAC9N,MAAL,KAAgBmb,IAAI,CAACnb,MADrB,IAEA8N,IAAI,CAAC6L,UAAL,KAAoBwB,IAAI,CAACxB,UAFzB,IAGA7L,IAAI,CAAC4L,iBAAL,KAA2ByB,IAAI,CAACzB,iBAHhC,IAIA5L,IAAI,CAAC7M,IAAL,KAAcka,IAAI,CAACla,IAJnB,IAKAiX,sBAAsB,CAACpK,IAAI,CAACsN,WAAN,EAAmBD,IAAI,CAACC,WAAxB,CANxB;AAQD,CAToB,CAArB;;AC3DA;;;;AAIA,IAAMtW,OAAO,GAAG,SAAVA,OAAU,CAACsT,KAAD;AAQd,MACEgD,WADF,GAOIhD,KAPJ,CACEgD,WADF;AAAA,MAEE5O,OAFF,GAOI4L,KAPJ,CAEE5L,OAFF;AAAA,6BAOI4L,KAPJ,CAGEqD,aAHF;AAAA,MAGEA,aAHF,qCAGkB,UAACC,CAAD;AAAA,wBAA2BzgB,uCAAA,CAAC0gB,cAAD,oBAAoBD,EAApB,CAA3B;AAAA,GAHlB;AAAA,MAIEhC,iBAJF,GAOItB,KAPJ,CAIEsB,iBAJF;AAAA,MAKEC,UALF,GAOIvB,KAPJ,CAKEuB,UALF;AAAA,MAME/V,SANF,GAOIwU,KAPJ,CAMExU,SANF;AAQA,MAAMhJ,MAAM,GAAGD,cAAc,EAA7B;AACA,MAAMihB,QAAQ,GAAGC,WAAW,EAA5B;AACA,MAAMxW,QAAQ,GAAGzK,MAAM,CAACyK,QAAP,CAAgBmH,OAAhB,CAAjB;AACA,MAAM7F,GAAG,GAAGtE,WAAW,CAACqE,OAAZ,CAAoB9L,MAApB,EAA4B4R,OAA5B,CAAZ;AACA,MAAMoM,GAAG,GAAGsB,iBAAW,CACrB,UAACtB,GAAD;AACE;AACA,QAAMhQ,cAAc,GAAGtL,wBAAwB,CAAC4F,GAAzB,CAA6BtI,MAA7B,CAAvB;;AACA,QAAIge,GAAJ,EAAS;AACPhQ,MAAAA,cAAc,SAAd,IAAAA,cAAc,WAAd,YAAAA,cAAc,CAAEnF,GAAhB,CAAoBkD,GAApB,EAAyBiS,GAAzB;AACAxb,MAAAA,eAAe,CAACqG,GAAhB,CAAoB+I,OAApB,EAA6BoM,GAA7B;AACAzb,MAAAA,eAAe,CAACsG,GAAhB,CAAoBmV,GAApB,EAAyBpM,OAAzB;AACD,KAJD,MAIO;AACL5D,MAAAA,cAAc,SAAd,IAAAA,cAAc,WAAd,YAAAA,cAAc,UAAd,CAAuBjC,GAAvB;AACAvJ,MAAAA,eAAe,UAAf,CAAuBoP,OAAvB;AACD;AACF,GAZoB,EAarB,CAAC5R,MAAD,EAAS+L,GAAT,EAAc6F,OAAd,CAbqB,CAAvB;AAeA,MAAIE,QAAQ,GAAoBoP,WAAW,CAAC;AAC1CV,IAAAA,WAAW,EAAXA,WAD0C;AAE1Cvb,IAAAA,IAAI,EAAE2M,OAFoC;AAG1CiP,IAAAA,aAAa,EAAbA,aAH0C;AAI1C/B,IAAAA,iBAAiB,EAAjBA,iBAJ0C;AAK1CC,IAAAA,UAAU,EAAVA,UAL0C;AAM1C/V,IAAAA,SAAS,EAATA;AAN0C,GAAD,CAA3C;AAUA;;AACA,MAAMuV,UAAU,GAOZ;AACF,uBAAmB,SADjB;AAEFP,IAAAA,GAAG,EAAHA;AAFE,GAPJ;;AAYA,MAAIvT,QAAJ,EAAc;AACZ8T,IAAAA,UAAU,CAAC,mBAAD,CAAV,GAAkC,IAAlC;AACD;AAGD;;;AACA,MAAI,CAAC9T,QAAD,IAAaL,YAAM,CAAC+W,UAAP,CAAkBnhB,MAAlB,EAA0B4R,OAA1B,CAAjB,EAAqD;AACnD,QAAMvL,IAAI,GAAGjC,UAAI,CAACwZ,MAAL,CAAYhM,OAAZ,CAAb;AACA,QAAMwP,GAAG,GAAGC,gCAAY,CAAChb,IAAD,CAAxB;;AAEA,QAAI+a,GAAG,KAAK,KAAZ,EAAmB;AACjB7C,MAAAA,UAAU,CAAC6C,GAAX,GAAiBA,GAAjB;AACD;AACF;;;AAGD,MAAIhX,YAAM,CAACC,MAAP,CAAcrK,MAAd,EAAsB4R,OAAtB,CAAJ,EAAoC;AAClC2M,IAAAA,UAAU,CAAC,iBAAD,CAAV,GAAgC,IAAhC;;AAEA,QAAI,CAACyC,QAAD,IAAavW,QAAjB,EAA2B;AACzB8T,MAAAA,UAAU,CAAC6B,eAAX,GAA6B,KAA7B;AACD;;AAED,QAAMkB,GAAG,GAAG7W,QAAQ,GAAG,MAAH,GAAY,KAAhC;;AACA,sBAAiBrG,UAAI,CAACgK,KAAL,CAAWwD,OAAX,CAAjB;AAAA;AAAA;AAAA,QAAQvL,KAAR;;AAEAyL,IAAAA,QAAQ,gBACNzR,uCAAA,CAACihB,GAAD;;AAEE3I,MAAAA,KAAK,EAAE;AACL9N,QAAAA,MAAM,EAAE,GADH;AAEL0W,QAAAA,KAAK,EAAE,aAFF;AAGLC,QAAAA,OAAO,EAAE,MAHJ;AAILnW,QAAAA,QAAQ,EAAE;AAJL;KAFT,eASEhL,uCAAA,CAACsD,YAAD;AACEmb,MAAAA,iBAAiB,EAAEA;AACnB0B,MAAAA,WAAW,EAAE;AACbpb,MAAAA,MAAM,EAAE;AACRO,MAAAA,MAAM,EAAEiM;AACRvL,MAAAA,IAAI,EAAEA;KALR,CATF,CADF;AAoBApE,IAAAA,aAAa,CAAC4G,GAAd,CAAkBxC,KAAlB,EAAwB,CAAxB;AACAlE,IAAAA,cAAc,CAAC0G,GAAf,CAAmBxC,KAAnB,EAAyBuL,OAAzB;AACD;;AAED,SAAOiP,aAAa,CAAC;AAAEtC,IAAAA,UAAU,EAAVA,UAAF;AAAczM,IAAAA,QAAQ,EAARA,QAAd;AAAwBF,IAAAA,OAAO,EAAPA;AAAxB,GAAD,CAApB;AACD,CA7GD;;AA+GA,IAAM6P,eAAe,gBAAGphB,yBAAK,CAACge,IAAN,CAAWnU,OAAX,EAAoB,UAACqW,IAAD,EAAOrN,IAAP;AAC1C,SACEqN,IAAI,CAAC3O,OAAL,KAAiBsB,IAAI,CAACtB,OAAtB,IACA2O,IAAI,CAACM,aAAL,KAAuB3N,IAAI,CAAC2N,aAD5B,IAEAN,IAAI,CAACxB,UAAL,KAAoB7L,IAAI,CAAC6L,UAFzB,IAGAwB,IAAI,CAACzB,iBAAL,KAA2B5L,IAAI,CAAC4L,iBAHhC,IAIA1B,yBAAyB,CAACmD,IAAI,CAACC,WAAN,EAAmBtN,IAAI,CAACsN,WAAxB,CAJzB,KAKCD,IAAI,CAACvX,SAAL,KAAmBkK,IAAI,CAAClK,SAAxB,IACE,CAAC,CAACuX,IAAI,CAACvX,SAAP,IACC,CAAC,CAACkK,IAAI,CAAClK,SADR,IAECmG,WAAK,CAACiG,MAAN,CAAamL,IAAI,CAACvX,SAAlB,EAA6BkK,IAAI,CAAClK,SAAlC,CARJ,CADF;AAWD,CAZuB,CAAxB;AAcA;;;;IAIa+X,cAAc,GAAG,SAAjBA,cAAiB,CAACvD,KAAD;AAC5B,MAAQe,UAAR,GAA0Cf,KAA1C,CAAQe,UAAR;AAAA,MAAoBzM,QAApB,GAA0C0L,KAA1C,CAAoB1L,QAApB;AAAA,MAA8BF,OAA9B,GAA0C4L,KAA1C,CAA8B5L,OAA9B;AACA,MAAM5R,MAAM,GAAGD,cAAc,EAA7B;AACA,MAAMuhB,GAAG,GAAGthB,MAAM,CAACyK,QAAP,CAAgBmH,OAAhB,IAA2B,MAA3B,GAAoC,KAAhD;AACA,sBACEvR,uCAAA,CAACihB,GAAD,oBAAS/C;AAAY5F,IAAAA,KAAK,EAAE;AAAEtN,MAAAA,QAAQ,EAAE;AAAZ;IAA5B,EACGyG,QADH,CADF;AAKD;;AChKD;;;;AAIO,IAAM4P,eAAe,gBAAG5hB,mBAAa,CAC1C;AAAA,SAAM,EAAN;AAAA,CAD0C,CAArC;AAIP;;;;AAIO,IAAM6hB,WAAW,GAAG,SAAdA,WAAc;AACzB,SAAO1hB,gBAAU,CAACyhB,eAAD,CAAjB;AACD,CAFM;;ACbP;;;;AAIO,IAAME,eAAe,gBAAG9hB,mBAAa,CAAC,KAAD,CAArC;AAEP;;;;IAIa+hB,WAAW,GAAG,SAAdA,WAAc;AACzB,SAAO5hB,gBAAU,CAAC2hB,eAAD,CAAjB;AACD;;;;;;;ACED;;;;AAIA,IAAMV,WAAW,GAAG,SAAdA,WAAc,CAAC1D,KAAD;AAQlB,MACEgD,WADF,GAOIhD,KAPJ,CACEgD,WADF;AAAA,MAEEvb,IAFF,GAOIuY,KAPJ,CAEEvY,IAFF;AAAA,MAGE4b,aAHF,GAOIrD,KAPJ,CAGEqD,aAHF;AAAA,MAIE/B,iBAJF,GAOItB,KAPJ,CAIEsB,iBAJF;AAAA,MAKEC,UALF,GAOIvB,KAPJ,CAKEuB,UALF;AAAA,MAME/V,SANF,GAOIwU,KAPJ,CAMExU,SANF;AAQA,MAAM8Y,QAAQ,GAAGH,WAAW,EAA5B;AACA,MAAM3hB,MAAM,GAAGD,cAAc,EAA7B;AACA,MAAMiK,IAAI,GAAGvC,WAAW,CAACwC,QAAZ,CAAqBjK,MAArB,EAA6BiF,IAA7B,CAAb;AACA,MAAM6M,QAAQ,GAAG,EAAjB;AACA,MAAMiQ,WAAW,GACf7X,aAAO,CAACC,SAAR,CAAkBlF,IAAlB,KACA,CAACjF,MAAM,CAACyK,QAAP,CAAgBxF,IAAhB,CADD,IAEAmF,YAAM,CAAC+W,UAAP,CAAkBnhB,MAAlB,EAA0BiF,IAA1B,CAHF;;AAKA,OAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,IAAI,CAAC6M,QAAL,CAAchN,MAAlC,EAA0CS,CAAC,EAA3C,EAA+C;AAC7C,QAAMub,CAAC,GAAG9W,IAAI,CAACgY,MAAL,CAAYzc,CAAZ,CAAV;AACA,QAAM2C,CAAC,GAAGjD,IAAI,CAAC6M,QAAL,CAAcvM,CAAd,CAAV;AACA,QAAMwG,GAAG,GAAGtE,WAAW,CAACqE,OAAZ,CAAoB9L,MAApB,EAA4BkI,CAA5B,CAAZ;AACA,QAAMgD,KAAK,GAAGd,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqB8gB,CAArB,CAAd;AACA,QAAMmB,GAAG,GAAGjZ,SAAS,IAAImG,WAAK,CAAC+S,YAAN,CAAmBhX,KAAnB,EAA0BlC,SAA1B,CAAzB;AACA,QAAMmZ,EAAE,GAAGL,QAAQ,CAAC,CAAC5Z,CAAD,EAAI4Y,CAAJ,CAAD,CAAnB;;AAN6C,iDAQ3BN,WAR2B;AAAA;;AAAA;AAQ7C,0DAA+B;AAAA,YAApB4B,GAAoB;AAC7B,YAAMC,CAAC,GAAGlT,WAAK,CAAC+S,YAAN,CAAmBE,GAAnB,EAAwBlX,KAAxB,CAAV;;AAEA,YAAImX,CAAJ,EAAO;AACLF,UAAAA,EAAE,CAAClJ,IAAH,CAAQoJ,CAAR;AACD;AACF;AAd4C;AAAA;AAAA;AAAA;AAAA;;AAgB7C,QAAInY,aAAO,CAACC,SAAR,CAAkBjC,CAAlB,CAAJ,EAA0B;AACxB4J,MAAAA,QAAQ,CAACmH,IAAT,eACE5Y,uCAAA,CAACuhB,eAAe,CAACU,QAAjB;AAA0BvW,QAAAA,GAAG,qBAAcA,GAAG,CAAC3D,EAAlB;AAAwBvE,QAAAA,KAAK,EAAE,CAAC,CAACoe;OAA9D,eACE5hB,uCAAA,CAACkiB,eAAD;AACE/B,QAAAA,WAAW,EAAE2B;AACbvQ,QAAAA,OAAO,EAAE1J;AACT6D,QAAAA,GAAG,EAAEA,GAAG,CAAC3D;AACTyY,QAAAA,aAAa,EAAEA;AACf/B,QAAAA,iBAAiB,EAAEA;AACnBC,QAAAA,UAAU,EAAEA;AACZ/V,QAAAA,SAAS,EAAEiZ;OAPb,CADF,CADF;AAaD,KAdD,MAcO;AACLnQ,MAAAA,QAAQ,CAACmH,IAAT,eACE5Y,uCAAA,CAACmiB,YAAD;AACEhC,QAAAA,WAAW,EAAE2B;AACbpW,QAAAA,GAAG,EAAEA,GAAG,CAAC3D;AACThD,QAAAA,MAAM,EAAE2c,WAAW,IAAIxc,CAAC,KAAKN,IAAI,CAAC6M,QAAL,CAAchN,MAAd,GAAuB;AACpDa,QAAAA,MAAM,EAAEV;AACR6Z,QAAAA,iBAAiB,EAAEA;AACnBC,QAAAA,UAAU,EAAEA;AACZ1Y,QAAAA,IAAI,EAAE6B;OAPR,CADF;AAWD;;AAEDjG,IAAAA,aAAa,CAAC4G,GAAd,CAAkBX,CAAlB,EAAqB3C,CAArB;AACApD,IAAAA,cAAc,CAAC0G,GAAf,CAAmBX,CAAnB,EAAsBjD,IAAtB;AACD;;AAED,SAAO6M,QAAP;AACD,CA1ED;;AClBA;;;;AAIO,IAAM2Q,eAAe,gBAAG3iB,mBAAa,CAAC,KAAD,CAArC;AAEP;;;;IAIamhB,WAAW,GAAG,SAAdA,WAAc;AACzB,SAAOhhB,gBAAU,CAACwiB,eAAD,CAAjB;AACD;;ACAM,IAAMC,YAAY,gBAAG5iB,mBAAa,CAG/B,IAH+B,CAAlC;AAKP;;;;IAIa6iB,QAAQ,GAAG,SAAXA,QAAW;AACtB,MAAMC,OAAO,GAAG3iB,gBAAU,CAACyiB,YAAD,CAA1B;;AAEA,MAAI,CAACE,OAAL,EAAc;AACZ,UAAM,IAAI1iB,KAAJ,4EAAN;AAGD;;AAED,MAAQF,MAAR,GAAmB4iB,OAAnB,CAAQ5iB,MAAR;AACA,SAAOA,MAAP;AACD;IAEY6iB,aAAa,GAAG,SAAhBA,aAAgB;AAC3B,MAAMD,OAAO,GAAG3iB,gBAAU,CAACyiB,YAAD,CAA1B;;AAEA,MAAI,CAACE,OAAL,EAAc;AACZ,UAAM,IAAI1iB,KAAJ,4EAAN;AAGD;;AAED,SAAO0iB,OAAP;AACD;;SC1CeE;AACd,MAAM9iB,MAAM,GAAGD,cAAc,EAA7B;AAEA,MAAMgjB,iBAAiB,GAAGzH,YAAM,CAAU,KAAV,CAAhC;AACA,MAAM0H,mBAAmB,GAAG1H,YAAM,CAAS,CAAT,CAAlC;AAEA,MAAM2H,WAAW,GAAG3D,iBAAW,CAAC;AAC9B,QAAIyD,iBAAiB,CAAC/R,OAAtB,EAA+B;AAC7B;AACD;;AAED+R,IAAAA,iBAAiB,CAAC/R,OAAlB,GAA4B,IAA5B;AAEA,QAAMtQ,MAAM,GAAG+G,WAAW,CAACC,SAAZ,CAAsB1H,MAAtB,CAAf;AACAU,IAAAA,MAAM,CAACwiB,oBAAP,CAA4BF,mBAAmB,CAAChS,OAAhD;AAEAgS,IAAAA,mBAAmB,CAAChS,OAApB,GAA8BtQ,MAAM,CAACyiB,qBAAP,CAA6B;AACzDJ,MAAAA,iBAAiB,CAAC/R,OAAlB,GAA4B,KAA5B;AACD,KAF6B,CAA9B;AAGD,GAb8B,EAa5B,CAAChR,MAAD,CAb4B,CAA/B;AAeAub,EAAAA,eAAS,CAAC;AAAA,WAAM;AAAA,aAAM2H,oBAAoB,CAACF,mBAAmB,CAAChS,OAArB,CAA1B;AAAA,KAAN;AAAA,GAAD,EAAgE,EAAhE,CAAT;AAEA,SAAO;AACL+R,IAAAA,iBAAiB,EAAjBA,iBADK;AAELE,IAAAA,WAAW,EAAXA;AAFK,GAAP;AAID;;AC/BM,IAAMG,YAAY,GAAG,CAArB;;ACGP;;;;AAIA,IAAMC,OAAO,GAAG;AACdC,EAAAA,IAAI,EAAE,OADQ;AAEdC,EAAAA,OAAO,EAAE,CAAC,MAAD,EAAS,MAAT,EAAiB,OAAjB,EAA0B,IAA1B,EAAgC,WAAhC,EAA6C,OAA7C,CAFK;AAGdC,EAAAA,YAAY,EAAE,MAHA;AAIdC,EAAAA,WAAW,EAAE,OAJC;AAKdC,EAAAA,gBAAgB,EAAE,WALJ;AAMdC,EAAAA,eAAe,EAAE,YANH;AAOd5J,EAAAA,cAAc,EAAE,kBAPF;AAQdF,EAAAA,aAAa,EAAE,eARD;AASd+J,EAAAA,cAAc,EAAE,YATF;AAUdC,EAAAA,aAAa,EAAE,aAVD;AAWdC,EAAAA,MAAM,EAAE,OAXM;AAYd7J,EAAAA,eAAe,EAAE,aAZH;AAad8J,EAAAA,UAAU,EAAE,OAbE;AAcdC,EAAAA,IAAI,EAAE;AAdQ,CAAhB;AAiBA,IAAMC,aAAa,GAAG;AACpBC,EAAAA,gBAAgB,EAAE,QADE;AAEpBC,EAAAA,eAAe,EAAE,UAFG;AAGpBT,EAAAA,gBAAgB,EAAE,UAHE;AAIpBC,EAAAA,eAAe,EAAE,WAJG;AAKpB5J,EAAAA,cAAc,EAAE,CAAC,gBAAD,EAAmB,QAAnB,CALI;AAMpBF,EAAAA,aAAa,EAAE,CAAC,aAAD,EAAgB,QAAhB,CANK;AAOpBuK,EAAAA,kBAAkB,EAAE,sBAPA;AAQpBC,EAAAA,iBAAiB,EAAE,CAAC,mBAAD,EAAsB,QAAtB,CARC;AASpBC,EAAAA,kBAAkB,EAAE,sBATA;AAUpBC,EAAAA,iBAAiB,EAAE,mBAVC;AAWpBC,EAAAA,kBAAkB,EAAE,cAXA;AAYpBC,EAAAA,iBAAiB,EAAE,gBAZC;AAapBC,EAAAA,IAAI,EAAE,aAbc;AAcpBC,EAAAA,kBAAkB,EAAE;AAdA,CAAtB;AAiBA,IAAMC,eAAe,GAAG;AACtBN,EAAAA,kBAAkB,EAAE,uBADE;AAEtBC,EAAAA,iBAAiB,EAAE,oBAFG;AAGtBG,EAAAA,IAAI,EAAE,CAAC,QAAD,EAAW,cAAX;AAHgB,CAAxB;AAMA;;;;AAIA,IAAMG,MAAM,GAAG,SAATA,MAAS,CAAC9Y,GAAD;AACb,MAAM+Y,OAAO,GAAGzB,OAAO,CAACtX,GAAD,CAAvB;AACA,MAAMgZ,KAAK,GAAGd,aAAa,CAAClY,GAAD,CAA3B;AACA,MAAMiZ,OAAO,GAAGJ,eAAe,CAAC7Y,GAAD,CAA/B;AACA,MAAMkZ,SAAS,GAAGH,OAAO,IAAII,iBAAQ,CAACJ,OAAD,CAArC;AACA,MAAMK,OAAO,GAAGJ,KAAK,IAAIG,iBAAQ,CAACH,KAAD,CAAjC;AACA,MAAMK,SAAS,GAAGJ,OAAO,IAAIE,iBAAQ,CAACF,OAAD,CAArC;AAEA,SAAO,UAACtgB,KAAD;AACL,QAAIugB,SAAS,IAAIA,SAAS,CAACvgB,KAAD,CAA1B,EAAmC,OAAO,IAAP;AACnC,QAAI5D,QAAQ,IAAIqkB,OAAZ,IAAuBA,OAAO,CAACzgB,KAAD,CAAlC,EAA2C,OAAO,IAAP;AAC3C,QAAI,CAAC5D,QAAD,IAAaskB,SAAb,IAA0BA,SAAS,CAAC1gB,KAAD,CAAvC,EAAgD,OAAO,IAAP;AAChD,WAAO,KAAP;AACD,GALD;AAMD,CAdD;AAgBA;;;;;AAIA,cAAe;AACb2gB,EAAAA,MAAM,EAAER,MAAM,CAAC,MAAD,CADD;AAEbS,EAAAA,SAAS,EAAET,MAAM,CAAC,SAAD,CAFJ;AAGbU,EAAAA,cAAc,EAAEV,MAAM,CAAC,cAAD,CAHT;AAIbW,EAAAA,aAAa,EAAEX,MAAM,CAAC,aAAD,CAJR;AAKbY,EAAAA,gBAAgB,EAAEZ,MAAM,CAAC,gBAAD,CALX;AAMba,EAAAA,eAAe,EAAEb,MAAM,CAAC,eAAD,CANV;AAObc,EAAAA,oBAAoB,EAAEd,MAAM,CAAC,oBAAD,CAPf;AAQbe,EAAAA,mBAAmB,EAAEf,MAAM,CAAC,mBAAD,CARd;AASbgB,EAAAA,oBAAoB,EAAEhB,MAAM,CAAC,oBAAD,CATf;AAUbiB,EAAAA,mBAAmB,EAAEjB,MAAM,CAAC,mBAAD,CAVd;AAWbkB,EAAAA,gBAAgB,EAAElB,MAAM,CAAC,gBAAD,CAXX;AAYbmB,EAAAA,eAAe,EAAEnB,MAAM,CAAC,eAAD,CAZV;AAaboB,EAAAA,oBAAoB,EAAEpB,MAAM,CAAC,oBAAD,CAbf;AAcbqB,EAAAA,mBAAmB,EAAErB,MAAM,CAAC,mBAAD,CAdd;AAebsB,EAAAA,QAAQ,EAAEtB,MAAM,CAAC,QAAD,CAfH;AAgBbuB,EAAAA,kBAAkB,EAAEvB,MAAM,CAAC,kBAAD,CAhBb;AAiBbwB,EAAAA,iBAAiB,EAAExB,MAAM,CAAC,iBAAD,CAjBZ;AAkBbyB,EAAAA,kBAAkB,EAAEzB,MAAM,CAAC,kBAAD,CAlBb;AAmBb0B,EAAAA,iBAAiB,EAAE1B,MAAM,CAAC,iBAAD,CAnBZ;AAoBb2B,EAAAA,MAAM,EAAE3B,MAAM,CAAC,MAAD,CApBD;AAqBb4B,EAAAA,WAAW,EAAE5B,MAAM,CAAC,iBAAD,CArBN;AAsBb6B,EAAAA,YAAY,EAAE7B,MAAM,CAAC,YAAD,CAtBP;AAuBb8B,EAAAA,oBAAoB,EAAE9B,MAAM,CAAC,oBAAD,CAvBf;AAwBb+B,EAAAA,MAAM,EAAE/B,MAAM,CAAC,MAAD;AAxBD,CAAf;;;ACvEA,SAAS,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE;AAC1C,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,KAAK,CAAC;AAC3D,IAAI,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;AACnC,IAAI,IAAI,OAAO,IAAI,UAAU,EAAE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC1D,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;AAC9D,GAAG;AACH,CAAC;AACD;AACA,SAAS,YAAY,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;AAC5D,EAAE,IAAI,UAAU,EAAE,iBAAiB,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AACvE,EAAE,IAAI,WAAW,EAAE,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC/D,EAAE,OAAO,WAAW,CAAC;AACrB,CAAC;AACD;AACA,cAAc,GAAG,YAAY,CAAC;AAC9B,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACjB5E,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE;AAC/B,EAAE,cAAc,GAAG,eAAe,GAAG,MAAM,CAAC,cAAc,IAAI,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE;AAC7F,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC;AACpB,IAAI,OAAO,CAAC,CAAC;AACb,GAAG,CAAC;AACJ;AACA,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI,CAAC;AAC/E,EAAE,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,CAAC;AACD;AACA,cAAc,GAAG,eAAe,CAAC;AACjC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACT5E,SAAS,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE;AACzC,EAAE,IAAI,OAAO,UAAU,KAAK,UAAU,IAAI,UAAU,KAAK,IAAI,EAAE;AAC/D,IAAI,MAAM,IAAI,SAAS,CAAC,oDAAoD,CAAC,CAAC;AAC9E,GAAG;AACH;AACA,EAAE,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE;AACzE,IAAI,WAAW,EAAE;AACjB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,YAAY,EAAE,IAAI;AACxB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,UAAU,EAAE,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AACvD,CAAC;AACD;AACA,cAAc,GAAG,SAAS,CAAC;AAC3B,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;AClB5E,SAAS,OAAO,CAAC,GAAG,EAAE;AACtB,EAAE,yBAAyB,CAAC;AAC5B;AACA,EAAE,IAAI,OAAO,MAAM,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAC3E,IAAI,cAAc,GAAG,OAAO,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE;AACrD,MAAM,OAAO,OAAO,GAAG,CAAC;AACxB,KAAK,CAAC;AACN;AACA,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI,CAAC;AACjF,GAAG,MAAM;AACT,IAAI,cAAc,GAAG,OAAO,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE;AACrD,MAAM,OAAO,GAAG,IAAI,OAAO,MAAM,KAAK,UAAU,IAAI,GAAG,CAAC,WAAW,KAAK,MAAM,IAAI,GAAG,KAAK,MAAM,CAAC,SAAS,GAAG,QAAQ,GAAG,OAAO,GAAG,CAAC;AACnI,KAAK,CAAC;AACN;AACA,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI,CAAC;AACjF,GAAG;AACH;AACA,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AACtB,CAAC;AACD;AACA,cAAc,GAAG,OAAO,CAAC;AACzB,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACrB5E,SAAS,sBAAsB,CAAC,IAAI,EAAE;AACtC,EAAE,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE;AACvB,IAAI,MAAM,IAAI,cAAc,CAAC,2DAA2D,CAAC,CAAC;AAC1F,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD;AACA,cAAc,GAAG,sBAAsB,CAAC;AACxC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACT5E,IAAI,OAAO,GAAGgC,SAAwC,CAAC,SAAS,CAAC,CAAC;AAClE;AACkE;AAClE;AACA,SAAS,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE;AAChD,EAAE,IAAI,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,UAAU,CAAC,EAAE;AAC1E,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE;AAC9B,IAAI,MAAM,IAAI,SAAS,CAAC,0DAA0D,CAAC,CAAC;AACpF,GAAG;AACH;AACA,EAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC;AACrC,CAAC;AACD;AACA,cAAc,GAAG,0BAA0B,CAAC;AAC5C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;;ACf5E,SAAS,eAAe,CAAC,CAAC,EAAE;AAC5B,EAAE,cAAc,GAAG,eAAe,GAAG,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,GAAG,SAAS,eAAe,CAAC,CAAC,EAAE;AACjH,IAAI,OAAO,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACnD,GAAG,CAAC;AACJ,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI,CAAC;AAC/E,EAAE,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC;AACD;AACA,cAAc,GAAG,eAAe,CAAC;AACjC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;;;;;ACCrE,IAAMC,uBAAuB,GAAG,SAA1BA,uBAA0B,CACrC9mB,MADqC,EAErC+iB,iBAFqC;AAIrC,MAAIgE,iBAAiB,GAAqB,EAA1C;;AAEA,MAAMC,KAAK,GAAG,SAARA,KAAQ;AACZD,IAAAA,iBAAiB,GAAG,EAApB;AACD,GAFD;;AAIA,MAAME,iBAAiB,GAAG,SAApBA,iBAAoB,CAAC/L,SAAD;;;AACxB,QAAI,CAAC6H,iBAAiB,CAAC/R,OAAvB,EAAgC;AAC9B;AACD;;AAED,QAAMkW,gBAAgB,GAAGhM,SAAS,CAAClD,MAAV,CAAiB,UAAA3Q,QAAQ;AAAA,aAChDD,iBAAiB,CAACpH,MAAD,EAASqH,QAAT,EAAmB6T,SAAnB,CAD+B;AAAA,KAAzB,CAAzB;;AAIA,0BAAA6L,iBAAiB,EAAC9N,IAAlB,8CAA0BiO,gBAA1B;AACD,GAVD;;AAYA,WAASC,UAAT;AACE,QAAIJ,iBAAiB,CAACjiB,MAAlB,GAA2B,CAA/B,EAAkC;AAChCiiB,MAAAA,iBAAiB,CAACK,OAAlB,GAA4BxW,OAA5B,CAAoC,UAAAvJ,QAAQ;AAC1C,YAAIA,QAAQ,CAACoO,IAAT,KAAkB,eAAtB,EAAuC;AACrC;AACA;AACA;AACD;;AAEDpO,QAAAA,QAAQ,CAACY,YAAT,CAAsB2I,OAAtB,CAA8B,UAAA3L,IAAI;AAChCoC,UAAAA,QAAQ,CAACE,MAAT,CAAgB8f,YAAhB,CAA6BpiB,IAA7B,EAAmCoC,QAAQ,CAACigB,WAA5C;AACD,SAFD;AAIAjgB,QAAAA,QAAQ,CAACW,UAAT,CAAoB4I,OAApB,CAA4B,UAAA3L,IAAI;AAC9BoC,UAAAA,QAAQ,CAACE,MAAT,CAAgBuJ,WAAhB,CAA4B7L,IAA5B;AACD,SAFD;AAGD,OAdD,EADgC;;AAkBhC+hB,MAAAA,KAAK;AACN;AACF;;AAED,SAAO;AACLC,IAAAA,iBAAiB,EAAjBA,iBADK;AAELE,IAAAA,UAAU,EAAVA,UAFK;AAGLH,IAAAA,KAAK,EAALA;AAHK,GAAP;AAKD,CAlDM;;;;;ACFP,IAAM9K,wBAAwB,GAAyB;AACrDC,EAAAA,OAAO,EAAE,IAD4C;AAErDC,EAAAA,SAAS,EAAE,IAF0C;AAGrDC,EAAAA,aAAa,EAAE,IAHsC;AAIrDkL,EAAAA,qBAAqB,EAAE;AAJ8B,CAAvD;AAaA;;IACMC;;;;;AAAN;;;;;;AAEE,iBAAA,GAA6C,IAA7C;AAEQ,iBAAA,GAAoC,IAApC;AACA,0BAAA,GAA4C,IAA5C;;AA6CT;;;;WA3CC;;;AACE,UAAQviB,IAAR,GAAiB,KAAKuY,KAAtB,CAAQvY,IAAR;;AACA,UAAI,CAACA,IAAI,CAAC+L,OAAV,EAAmB;AACjB,cAAM,IAAI9Q,KAAJ,CAAU,wDAAV,CAAN;AACD;;AAED,oCAAK4b,gBAAL,gFAAuBE,OAAvB,CAA+B/W,IAAI,CAAC+L,OAApC,EAA6CkL,wBAA7C;AACD;;;WAED;AACE,UAAQ6G,iBAAR,GAA8B,KAAKvF,KAAnC,CAAQuF,iBAAR;AACA,UAAM/iB,MAAM,GAAG,KAAK4iB,OAApB;AAEA,WAAK6E,OAAL,GAAeX,uBAAuB,CAAC9mB,MAAD,EAAS+iB,iBAAT,CAAtC;AACA,WAAKjH,gBAAL,GAAwB,IAAID,gBAAJ,CAAqB,KAAK4L,OAAL,CAAaR,iBAAlC,CAAxB;AAEA,WAAKjL,OAAL;AACD;;;WAED;;;AACE,UAAM0L,gBAAgB,6BAAG,KAAK5L,gBAAR,2DAAG,uBAAuBC,WAAvB,EAAzB;;AACA,UAAI2L,gBAAJ,aAAIA,gBAAJ,eAAIA,gBAAgB,CAAE5iB,MAAtB,EAA8B;AAAA;;AAC5B,8BAAK2iB,OAAL,gEAAcR,iBAAd,CAAgCS,gBAAhC;AACD;;AAED,qCAAK5L,gBAAL,kFAAuBG,UAAvB;AACA,6BAAKwL,OAAL,kEAAcN,UAAd;AAEA,aAAO,IAAP;AACD;;;WAED;;;AACE,6BAAKM,OAAL,kEAAcT,KAAd;AACA,WAAKhL,OAAL;AACD;;;WAED;;;AACE,qCAAKF,gBAAL,kFAAuBG,UAAvB;AACD;;;WAED;AACE,aAAO,KAAKuB,KAAL,CAAW1L,QAAlB;AACD;;;;EAjD+B6V;;AACzBH,+BAAA,GAAc3nB,aAAd;AAmDF,IAAM+nB,UAAU,GAAmC7mB,UAAU,GAChEymB,mBADgE,GAEhE;AAAA,MAAG1V,QAAH,QAAGA,QAAH;AAAA,sBAAkBzR,uCAAA,mCAAA,MAAA,EAAGyR,QAAH,CAAlB;AAAA,CAFG;;;;;;;;;;;;;;;ACDP,IAAM+V,QAAQ,GAAG,SAAXA,QAAW,CAACrK,KAAD;AAAA,sBACfnd,uCAAA,CAACA,yBAAK,CAACggB,QAAP,MAAA,EAAiBa,WAAW,CAAC1D,KAAD,CAA5B,CADe;AAAA,CAAjB;AAoDA;;;;;IAIasK,QAAQ,GAAG,SAAXA,QAAW,CAACtK,KAAD;AACtB,MAAMuK,wBAAwB,GAAGzI,iBAAW,CAC1C,UAAC9B,KAAD;AAAA,wBAAmCnd,uCAAA,CAAC2nB,kBAAD,oBAAwBxK,MAAxB,CAAnC;AAAA,GAD0C,EAE1C,EAF0C,CAA5C;;AAIA,MACEyK,SADF,GAcIzK,KAdJ,CACEyK,SADF;AAAA,wBAcIzK,KAdJ,CAEEsE,QAFF;AAAA,MAEEA,QAFF,gCAEaoG,eAFb;AAAA,MAGoBC,qBAHpB,GAcI3K,KAdJ,CAGE4K,gBAHF;AAAA,MAIEvI,WAJF,GAcIrC,KAdJ,CAIEqC,WAJF;AAAA,wBAcIrC,KAdJ,CAKEwD,QALF;AAAA,MAKEA,QALF,gCAKa,KALb;AAAA,MAMEH,aANF,GAcIrD,KAdJ,CAMEqD,aANF;AAAA,MAOE9B,UAPF,GAcIvB,KAdJ,CAOEuB,UAPF;AAAA,8BAcIvB,KAdJ,CAQEsB,iBARF;AAAA,MAQEA,iBARF,sCAQsBiJ,wBARtB;AAAA,8BAcIvK,KAdJ,CASE6K,uBATF;AAAA,MASEA,uBATF,sCAS4BC,8BAT5B;AAAA,qBAcI9K,KAdJ,CAUE7E,KAVF;AAAA,MAUS4P,SAVT,6BAUqB,EAVrB;AAAA,kBAcI/K,KAdJ,CAWEgL,EAXF;AAAA,MAWMb,SAXN,0BAWkB,KAXlB;AAAA,8BAcInK,KAdJ,CAYEiL,oBAZF;AAAA,MAYEA,oBAZF,sCAYyB,KAZzB;AAAA,MAaKlK,UAbL,4BAcIf,KAdJ;;AAeA,MAAMxd,MAAM,GAAG2iB,QAAQ,EAAvB;;AAEA,kBAAsC/G,cAAQ,CAAC,KAAD,CAA9C;AAAA;AAAA,MAAOlO,WAAP;AAAA,MAAoBgb,cAApB;;AACA,MAAM1K,GAAG,GAAG1C,YAAM,CAAwB,IAAxB,CAAlB;AACA,MAAMqN,kBAAkB,GAAGrN,YAAM,CAAsB,EAAtB,CAAjC;;AACA,mBAAkDM,cAAQ,EAA1D;AAAA;AAAA,MAAOgN,iBAAP;AAAA,MAA0BC,oBAA1B;;AAIA,2BAA2C/F,iBAAiB,EAA5D;AAAA,MAAQG,WAAR,sBAAQA,WAAR;AAAA,MAAqBF,iBAArB,sBAAqBA,iBAArB;;AAEA,oBAAwB+F,gBAAU,CAAC,UAAAC,CAAC;AAAA,WAAIA,CAAC,GAAG,CAAR;AAAA,GAAF,EAAa,CAAb,CAAlC;AAAA;AAAA,MAASC,WAAT;;AACA1lB,EAAAA,sBAAsB,CAACuF,GAAvB,CAA2B7I,MAA3B,EAAmCgpB,WAAnC;;AAGArmB,EAAAA,YAAY,CAACkG,GAAb,CAAiB7I,MAAjB,EAAyBghB,QAAzB;;AAGA,MAAMiI,KAAK,GAAGC,aAAO,CACnB;AAAA,WAAO;AACLC,MAAAA,oBAAoB,EAAE,KADjB;AAELC,MAAAA,mBAAmB,EAAE,KAFhB;AAGLC,MAAAA,aAAa,EAAE,IAHV;AAILC,MAAAA,kBAAkB,EAAE;AAJf,KAAP;AAAA,GADmB,EAOnB,EAPmB,CAArB;AAWA;;AACA/N,EAAAA,eAAS,CAAC;AACR,QAAIyC,GAAG,CAAChN,OAAJ,IAAeiX,SAAnB,EAA8B;AAC5BjK,MAAAA,GAAG,CAAChN,OAAJ,CAAY5E,KAAZ;AACD;AACF,GAJQ,EAIN,CAAC6b,SAAD,CAJM,CAAT;AAMA;;;;;;;AAMA,MAAMsB,sBAAsB,GAAGjO,YAAM,EAArC;AAKA;AACA;AACA;AACA;;AACA,MAAMjF,oBAAoB,GAAG6S,aAAO,CAClC;AAAA,WACEM,4BAAQ,CAAC;AACP,UAAMC,mBAAmB,GAAGF,sBAAsB,CAACvY,OAAnD;;AACA,UACE,CAACjQ,UAAU,IAAI,CAAC0G,WAAW,CAACiG,WAAZ,CAAwB1N,MAAxB,CAAhB,MACC,CAACipB,KAAK,CAACG,mBAAP,IAA8BK,mBAA9B,aAA8BA,mBAA9B,eAA8BA,mBAAmB,CAAE9O,UAArB,EAD/B,KAEA,CAACsO,KAAK,CAACE,oBAHT,EAIE;AACA,YAAMxgB,IAAI,GAAGlB,WAAW,CAACmB,wBAAZ,CAAqC5I,MAArC,CAAb;AACA,YAAQ8I,aAAR,GAA0BH,IAA1B,CAAQG,aAAR;AACA,YAAML,EAAE,GAAGhB,WAAW,CAACiB,SAAZ,CAAsB1I,MAAtB,EAA8BA,MAA9B,CAAX;AACA,YAAMiJ,YAAY,GAAGN,IAAI,CAACO,YAAL,EAArB;;AAEA,YAAIJ,aAAa,KAAKL,EAAtB,EAA0B;AACxBwgB,UAAAA,KAAK,CAACI,aAAN,GAAsBvgB,aAAtB;AACAlG,UAAAA,UAAU,CAACiG,GAAX,CAAe7I,MAAf,EAAuB,IAAvB;AACD,SAHD,MAGO;AACL4C,UAAAA,UAAU,UAAV,CAAkB5C,MAAlB;AACD;;AAED,YAAI,CAACiJ,YAAL,EAAmB;AACjB,iBAAOI,gBAAU,CAACN,QAAX,CAAoB/I,MAApB,CAAP;AACD;;AAED,YAAQsE,UAAR,GAAkC2E,YAAlC,CAAQ3E,UAAR;AAAA,YAAoB+M,SAApB,GAAkCpI,YAAlC,CAAoBoI,SAApB;AAEA,YAAMqY,oBAAoB,GACxBjiB,WAAW,CAACsF,iBAAZ,CAA8B/M,MAA9B,EAAsCsE,UAAtC,KACAmD,WAAW,CAAC2F,6BAAZ,CAA0CpN,MAA1C,EAAkDsE,UAAlD,CAFF;AAIA,YAAMqlB,mBAAmB,GACvBliB,WAAW,CAACsF,iBAAZ,CAA8B/M,MAA9B,EAAsCqR,SAAtC,KACA5J,WAAW,CAAC2F,6BAAZ,CAA0CpN,MAA1C,EAAkDqR,SAAlD,CAFF;;AAIA,YAAIqY,oBAAoB,IAAIC,mBAA5B,EAAiD;AAC/C,cAAMze,KAAK,GAAGzD,WAAW,CAACkE,YAAZ,CAAyB3L,MAAzB,EAAiCiJ,YAAjC,EAA+C;AAC3D2C,YAAAA,UAAU,EAAE,KAD+C;AAE3DC,YAAAA,aAAa,EAAE;AAF4C,WAA/C,CAAd;;AAKA,cAAIX,KAAJ,EAAW;AACT,gBACE,CAACzD,WAAW,CAACiG,WAAZ,CAAwB1N,MAAxB,CAAD,IACA,EAACypB,mBAAD,aAACA,mBAAD,eAACA,mBAAmB,CAAE/O,iBAArB,EAAD,CADA,IAEA,EAAC+O,mBAAD,aAACA,mBAAD,eAACA,mBAAmB,CAAE9O,UAArB,EAAD,CAHF,EAIE;AACAtR,cAAAA,gBAAU,CAACyN,MAAX,CAAkB9W,MAAlB,EAA0BkL,KAA1B;AACD,aAND,MAMO;AACLue,cAAAA,mBAAmB,SAAnB,IAAAA,mBAAmB,WAAnB,YAAAA,mBAAmB,CAAE9P,gBAArB,CAAsCzO,KAAtC;AACD;AACF;AACF,SA5CD;;;AA+CA,YAAI8V,QAAQ,KAAK,CAAC0I,oBAAD,IAAyB,CAACC,mBAA/B,CAAZ,EAAiE;AAC/DtgB,UAAAA,gBAAU,CAACN,QAAX,CAAoB/I,MAApB;AACD;AACF;AACF,KAzDO,EAyDL,GAzDK,CADV;AAAA,GADkC,EA4DlC,CAACA,MAAD,EAASghB,QAAT,EAAmBiI,KAAnB,CA5DkC,CAApC;AA+DA,MAAM7S,4BAA4B,GAAG8S,aAAO,CAC1C;AAAA,WAAMU,4BAAQ,CAACvT,oBAAD,EAAuB,CAAvB,CAAd;AAAA,GAD0C,EAE1C,CAACA,oBAAD,CAF0C,CAA5C;AAKAkT,EAAAA,sBAAsB,CAACvY,OAAvB,GAAiCsL,sBAAsB,CAAC;AACtDrX,IAAAA,IAAI,EAAE+Y,GADgD;AAEtD3H,IAAAA,oBAAoB,EAApBA,oBAFsD;AAGtDD,IAAAA,4BAA4B,EAA5BA;AAHsD,GAAD,CAAvD;AAMAoF,EAAAA,yBAAyB,CAAC;;;AACxB;AACA,QAAI9a,MAAJ;;AACA,QAAIsd,GAAG,CAAChN,OAAJ,KAAgBtQ,MAAM,GAAGkD,cAAc,CAACoa,GAAG,CAAChN,OAAL,CAAvC,CAAJ,EAA2D;AACzD5O,MAAAA,gBAAgB,CAACyG,GAAjB,CAAqB7I,MAArB,EAA6BU,MAA7B;AACA2B,MAAAA,iBAAiB,CAACwG,GAAlB,CAAsB7I,MAAtB,EAA8Bge,GAAG,CAAChN,OAAlC;AACAxO,MAAAA,eAAe,CAACqG,GAAhB,CAAoB7I,MAApB,EAA4Bge,GAAG,CAAChN,OAAhC;AACAzO,MAAAA,eAAe,CAACsG,GAAhB,CAAoBmV,GAAG,CAAChN,OAAxB,EAAiChR,MAAjC;AACD,KALD,MAKO;AACLwC,MAAAA,eAAe,UAAf,CAAuBxC,MAAvB;AACD;;;AAGD,QAAQgJ,SAAR,GAAsBhJ,MAAtB,CAAQgJ,SAAR;AACA,QAAML,IAAI,GAAGlB,WAAW,CAACmB,wBAAZ,CAAqC5I,MAArC,CAAb;AACA,QAAMiJ,YAAY,GAAGN,IAAI,CAACO,YAAL,EAArB;;AAEA,QACE,CAACD,YAAD,IACA,CAACxB,WAAW,CAACkG,SAAZ,CAAsB3N,MAAtB,CADD,6BAEAupB,sBAAsB,CAACvY,OAFvB,kDAEA,sBAAgCsG,gBAAhC,EAHF,EAIE;AACA;AACD;;AAED,QAAMuS,eAAe,GAAG,SAAlBA,eAAkB,CAACC,WAAD;AACtB,UAAMC,eAAe,GAAG9gB,YAAY,CAACwM,IAAb,KAAsB,MAA9C;;AAGA,UAAI,CAACzM,SAAD,IAAc,CAAC+gB,eAAnB,EAAoC;AAClC;AACD;;;AAGD,UAAM1Y,SAAS,GAAGpI,YAAY,CAACoI,SAA/B;AACA,UAAI/M,UAAJ;AAGA;;AACA,UAAItD,UAAU,IAAIiI,YAAY,CAACE,UAAb,GAA0B,CAA5C,EAA+C;AAC7C,YAAMoI,UAAU,GAAGtI,YAAY,CAACuI,UAAb,CAAwB,CAAxB,CAAnB;AACA,YAAMC,SAAS,GAAGxI,YAAY,CAACuI,UAAb,CAAwBvI,YAAY,CAACE,UAAb,GAA0B,CAAlD,CAAlB,CAF6C;;AAK7C,YAAIoI,UAAU,CAACJ,cAAX,KAA8BE,SAAlC,EAA6C;AAC3C/M,UAAAA,UAAU,GAAGmN,SAAS,CAACY,YAAvB;AACD,SAFD,MAEO;AACL;AACA/N,UAAAA,UAAU,GAAGiN,UAAU,CAACJ,cAAxB;AACD;AACF,OAXD,MAWO;AACL7M,QAAAA,UAAU,GAAG2E,YAAY,CAAC3E,UAA1B;AACD;;;AAGD,UAAM0lB,aAAa,GAAG3nB,iBAAiB,CAACiG,GAAlB,CAAsBtI,MAAtB,CAAtB;AACA,UAAIiqB,uBAAuB,GAAG,KAA9B;;AACA,UACED,aAAa,CAACriB,QAAd,CAAuBrD,UAAvB,KACA0lB,aAAa,CAACriB,QAAd,CAAuB0J,SAAvB,CAFF,EAGE;AACA4Y,QAAAA,uBAAuB,GAAG,IAA1B;AACD;;;AAGD,UACEF,eAAe,IACfE,uBADA,IAEAjhB,SAFA,IAGA,CAAC8gB,WAJH,EAKE;AACA,YAAMI,UAAU,GAAGziB,WAAW,CAACkE,YAAZ,CAAyB3L,MAAzB,EAAiCiJ,YAAjC,EAA+C;AAChE2C,UAAAA,UAAU,EAAE,IADoD;AAGhE;AACA;AACAC,UAAAA,aAAa,EAAE;AALiD,SAA/C,CAAnB;;AAQA,YAAIqe,UAAU,IAAI/a,WAAK,CAACiG,MAAN,CAAa8U,UAAb,EAAyBlhB,SAAzB,CAAlB,EAAuD;AAAA;;AACrD,cAAI,CAACigB,KAAK,CAACK,kBAAX,EAA+B;AAC7B;AACD,WAHoD;;;AAMrD,6BACEhlB,UADF,iEACE,YAAYmI,aADd,kDACE,sBAA2BkC,YAA3B,CACE,6BADF,CADF,EAIE;AACA;AACD;AACF;AACF;AAGD;AACA;AACA;;;AACA,UAAI3F,SAAS,IAAI,CAACvB,WAAW,CAACuF,QAAZ,CAAqBhN,MAArB,EAA6BgJ,SAA7B,CAAlB,EAA2D;AACzDhJ,QAAAA,MAAM,CAACgJ,SAAP,GAAmBvB,WAAW,CAACkE,YAAZ,CAAyB3L,MAAzB,EAAiCiJ,YAAjC,EAA+C;AAChE2C,UAAAA,UAAU,EAAE,KADoD;AAEhEC,UAAAA,aAAa,EAAE;AAFiD,SAA/C,CAAnB;AAIA;AACD;;;AAGDod,MAAAA,KAAK,CAACG,mBAAN,GAA4B,IAA5B;AAEA,UAAMe,WAAW,GACfnhB,SAAS,IAAIvB,WAAW,CAACwH,UAAZ,CAAuBjP,MAAvB,EAA+BgJ,SAA/B,CADf;;AAGA,UAAImhB,WAAJ,EAAiB;AACf,YAAI1iB,WAAW,CAACiG,WAAZ,CAAwB1N,MAAxB,KAAmC,CAACe,UAAxC,EAAoD;AAClDkI,UAAAA,YAAY,CAACmhB,aAAb;AACD,SAFD,MAEO,IAAIjb,WAAK,CAACD,UAAN,CAAiBlG,SAAjB,CAAJ,EAAkC;AACvCC,UAAAA,YAAY,CAACohB,gBAAb,CACEF,WAAW,CAAC9X,YADd,EAEE8X,WAAW,CAACza,SAFd,EAGEya,WAAW,CAAChZ,cAHd,EAIEgZ,WAAW,CAAC3a,WAJd;AAMD,SAPM,MAOA;AACLvG,UAAAA,YAAY,CAACohB,gBAAb,CACEF,WAAW,CAAChZ,cADd,EAEEgZ,WAAW,CAAC3a,WAFd,EAGE2a,WAAW,CAAC9X,YAHd,EAIE8X,WAAW,CAACza,SAJd;AAMD;;AACD2Y,QAAAA,uBAAuB,CAACroB,MAAD,EAASmqB,WAAT,CAAvB;AACD,OAnBD,MAmBO;AACLlhB,QAAAA,YAAY,CAACG,eAAb;AACD;;AAED,aAAO+gB,WAAP;AACD,KAhHD;;;AAmHA,QAAIlhB,YAAY,CAACE,UAAb,IAA2B,CAA/B,EAAkC;AAChC0gB,MAAAA,eAAe;AAChB;;AAED,QAAMS,eAAe,GACnB,2BAAAf,sBAAsB,CAACvY,OAAvB,kFAAgC2J,UAAhC,QAAiD,QADnD;;AAGA,QAAI,CAAC5Z,UAAD,IAAe,CAACupB,eAApB,EAAqC;AACnC/S,MAAAA,UAAU,CAAC;AACT0R,QAAAA,KAAK,CAACG,mBAAN,GAA4B,KAA5B;AACD,OAFS,CAAV;AAGA;AACD;;AAED,QAAImB,SAAS,GAAyC,IAAtD;AACA,QAAMC,gBAAgB,GAAGrH,qBAAqB,CAAC;AAC7C,UAAImH,eAAJ,EAAqB;AACnB,YAAMG,kBAAkB,GAAG,SAArBA,kBAAqB,CAACX,WAAD;AACzB,cAAI;AACF,gBAAMrhB,EAAE,GAAGhB,WAAW,CAACiB,SAAZ,CAAsB1I,MAAtB,EAA8BA,MAA9B,CAAX;AACAyI,YAAAA,EAAE,CAAC2D,KAAH;AAEAyd,YAAAA,eAAe,CAACC,WAAD,CAAf;AACD,WALD,CAKE,OAAOY,CAAP,EAAU;AAEX;AACF,SATD,CADmB;AAanB;AACA;AACA;;;AACAD,QAAAA,kBAAkB;AAElBF,QAAAA,SAAS,GAAGhT,UAAU,CAAC;AACrB;AACA;AACA;AACAkT,UAAAA,kBAAkB,CAAC,IAAD,CAAlB;AACAxB,UAAAA,KAAK,CAACG,mBAAN,GAA4B,KAA5B;AACD,SANqB,CAAtB;AAOD;AACF,KA3B6C,CAA9C;AA6BA,WAAO;AACLlG,MAAAA,oBAAoB,CAACsH,gBAAD,CAApB;;AACA,UAAID,SAAJ,EAAe;AACbnT,QAAAA,YAAY,CAACmT,SAAD,CAAZ;AACD;AACF,KALD;AAMD,GA9LwB,CAAzB;AAiMA;AACA;AACA;;AACA,MAAMnC,gBAAgB,GAAG9I,iBAAW,CAClC,UAAC5a,KAAD;AACEue,IAAAA,WAAW;;AAEX,QACE,CAACjC,QAAD,IACAvZ,WAAW,CAACsF,iBAAZ,CAA8B/M,MAA9B,EAAsC0E,KAAK,CAAC6C,MAA5C,CADA,IAEA,CAACojB,iBAAiB,CAACjmB,KAAD,EAAQyjB,qBAAR,CAHpB,EAIE;AAAA;;AACA;AACA,UAAIoB,sBAAsB,CAACvY,OAA3B,EAAoC;AAClC,eAAOuY,sBAAsB,CAACvY,OAAvB,CAA+BqI,oBAA/B,CAAoD3U,KAApD,CAAP;AACD,OAJD;AAOA;AACA;;;AACA0R,MAAAA,4BAA4B,CAACe,KAA7B;AACAd,MAAAA,oBAAoB,CAACc,KAArB;AAEA,UAAQnO,SAAR,GAAsBhJ,MAAtB,CAAQgJ,SAAR;AACA,UAAmByM,IAAnB,GAA4B/Q,KAA5B,CAAQ4U,SAAR;AACA,UAAM/L,IAAI,GAAI7I,KAAa,CAACsC,YAAd,IAA8BtC,KAAK,CAAC6I,IAApC,IAA4CsK,SAA1D;AAEA,UAAM+S,mBAAmB,GACvBnV,IAAI,KAAK,uBAAT,IAAoCA,IAAI,KAAK,uBAD/C,CAhBA;AAoBA;;AACA,UAAImV,mBAAmB,IAAInjB,WAAW,CAACiG,WAAZ,CAAwB1N,MAAxB,CAA3B,EAA4D;AAC1D;AACD;;AAED,UAAI6qB,OAAM,GAAG,KAAb;;AACA,UACEpV,IAAI,KAAK,YAAT,IACAzM,SADA,IAEAmG,WAAK,CAACG,WAAN,CAAkBtG,SAAlB,CAFA;AAIA;AACA;AACAtE,MAAAA,KAAK,CAAC6I,IANN,IAOA7I,KAAK,CAAC6I,IAAN,CAAWzI,MAAX,KAAsB,CAPtB,IAQA,UAAUnE,IAAV,CAAe+D,KAAK,CAAC6I,IAArB,CARA;AAUA;AACA;AACAvE,MAAAA,SAAS,CAACiE,MAAV,CAAiB/H,MAAjB,KAA4B,CAb9B,EAcE;AAAA;;AACA2lB,QAAAA,OAAM,GAAG,IAAT,CADA;AAIA;;AACA,YAAI7qB,MAAM,CAAC0X,KAAX,EAAkB;AAChBmT,UAAAA,OAAM,GAAG,KAAT;AACD,SAPD;AAUA;;;AACA,YAAQ5d,MAAR,GAAmBjE,SAAnB,CAAQiE,MAAR;;AAEA,oCAAuBxF,WAAW,CAACwG,UAAZ,CAAuBjO,MAAvB,EAA+BiN,MAA/B,CAAvB;AAAA;AAAA,YAAOhI,IAAP;AAAA,YAAaC,MAAb;;AACA,YAAMZ,UAAU,0BAAGW,IAAI,CAACwH,aAAR,wDAAG,oBAAoBI,OAApB,CAA4B,GAA5B,CAAnB;AAEA,YAAMnM,MAAM,GAAG+G,WAAW,CAACC,SAAZ,CAAsB1H,MAAtB,CAAf;;AAEA,YACE6qB,OAAM,IACNvmB,UADA,IAEAmD,WAAW,CAACG,UAAZ,CAAuB5H,MAAvB,EAA+BsE,UAA/B,CAHF,EAIE;AAAA;;AACA;AACA,cAAMwmB,QAAQ,GAAGpqB,MAAH,aAAGA,MAAH,uBAAGA,MAAM,CAAEgB,QAAR,CACdqpB,gBADc,CACGzmB,UADH,EACe0mB,UAAU,CAACC,SAD1B,EAEdC,SAFc,EAAjB;;AAIA,cAAIJ,QAAQ,KAAK7lB,IAAb,IAAqB,0BAAA6lB,QAAQ,CAACrlB,WAAT,gFAAsBX,MAAtB,MAAiCI,MAA1D,EAAkE;AAChE2lB,YAAAA,OAAM,GAAG,KAAT;AACD;AACF,SA/BD;AAkCA;;;AACA,YACEA,OAAM,IACN5lB,IAAI,CAACwH,aADL,IAEA,CAAA/L,MAAM,SAAN,IAAAA,MAAM,WAAN,qCAAAA,MAAM,CAAEiG,gBAAR,CAAyB1B,IAAI,CAACwH,aAA9B,iFAA8C0e,UAA9C,MAA6D,KAH/D,EAIE;AACA,cAAMC,KAAK,GAAGhhB,YAAM,CAACwK,KAAP,CAAa5U,MAAb,EAAqB;AACjCkO,YAAAA,EAAE,EAAEjB,MAAM,CAACjD,IADsB;AAEjC9C,YAAAA,KAAK,EAAE,eAAAgB,CAAC;AAAA,qBAAIgC,aAAO,CAACC,SAAR,CAAkBjC,CAAlB,KAAwBkC,YAAM,CAACyK,OAAP,CAAe7U,MAAf,EAAuBkI,CAAvB,CAA5B;AAAA;AAFyB,WAArB,CAAd;;AAKA,cAAIkjB,KAAK,IAAIhnB,UAAI,CAACwZ,MAAL,CAAYwN,KAAK,CAAC,CAAD,CAAjB,EAAsBxe,QAAtB,CAA+B,IAA/B,CAAb,EAAmD;AACjDie,YAAAA,OAAM,GAAG,KAAT;AACD;AACF;AACF,OAzFD;AA4FA;AACA;;;AACA,UAAI,CAACpV,IAAI,CAAC5G,UAAL,CAAgB,QAAhB,CAAD,IAA8B4G,IAAI,CAAC5G,UAAL,CAAgB,UAAhB,CAAlC,EAA+D;AAC7D,oCAAuBnK,KAAa,CAAC1C,eAAd,EAAvB;AAAA;AAAA,YAAOwS,WAAP;;AAEA,YAAIA,WAAJ,EAAiB;AACf,cAAMtJ,KAAK,GAAGzD,WAAW,CAACkE,YAAZ,CAAyB3L,MAAzB,EAAiCwU,WAAjC,EAA8C;AAC1D5I,YAAAA,UAAU,EAAE,KAD8C;AAE1DC,YAAAA,aAAa,EAAE;AAF2C,WAA9C,CAAd;;AAKA,cAAI,CAAC7C,SAAD,IAAc,CAACmG,WAAK,CAACiG,MAAN,CAAapM,SAAb,EAAwBkC,KAAxB,CAAnB,EAAmD;AACjD2f,YAAAA,OAAM,GAAG,KAAT;AAEA,gBAAMrT,YAAY,GAChB,CAACoT,mBAAD,IACA5qB,MAAM,CAACgJ,SADP,IAEAoB,YAAM,CAACqN,QAAP,CAAgBzX,MAAhB,EAAwBA,MAAM,CAACgJ,SAA/B,CAHF;AAKAK,YAAAA,gBAAU,CAACyN,MAAX,CAAkB9W,MAAlB,EAA0BkL,KAA1B;;AAEA,gBAAIsM,YAAJ,EAAkB;AAChB1U,cAAAA,wBAAwB,CAAC+F,GAAzB,CAA6B7I,MAA7B,EAAqCwX,YAArC;AACD;AACF;AACF;AACF,OAtHD;AAyHA;;;AACA,UAAIoT,mBAAJ,EAAyB;AACvB;AACD;;AAED,UAAI,CAACC,OAAL,EAAa;AACXnmB,QAAAA,KAAK,CAAC2mB,cAAN;AACD,OAhID;AAmIA;;;AACA,UACEriB,SAAS,IACTmG,WAAK,CAACoD,UAAN,CAAiBvJ,SAAjB,CADA,IAEAyM,IAAI,CAAC5G,UAAL,CAAgB,QAAhB,CAHF,EAIE;AACA,YAAM/I,SAAS,GAAG2P,IAAI,CAACxE,QAAL,CAAc,UAAd,IAA4B,UAA5B,GAAyC,SAA3D;AACA7G,QAAAA,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB,EAA8B;AAAE8F,UAAAA,SAAS,EAATA;AAAF,SAA9B;AACA;AACD;;AAED,cAAQ2P,IAAR;AACE,aAAK,qBAAL;AACA,aAAK,aAAL;AACA,aAAK,cAAL;AAAqB;AACnBrL,YAAAA,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB;AACA;AACD;;AAED,aAAK,eAAL;AACA,aAAK,sBAAL;AAA6B;AAC3BoK,YAAAA,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB;AACA;AACD;;AAED,aAAK,uBAAL;AAA8B;AAC5BoK,YAAAA,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB;AACA;AACD;;AAED,aAAK,sBAAL;AAA6B;AAC3BoK,YAAAA,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,EAA8B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA9B;AACA5P,YAAAA,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB,EAA6B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA7B;AACA;AACD;;AAED,aAAK,wBAAL;AAA+B;AAC7B5P,YAAAA,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,EAA8B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA9B;AACA;AACD;;AAED,aAAK,wBAAL;AAA+B;AAC7B5P,YAAAA,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,EAA8B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA9B;AACA;AACD;;AAED,aAAK,uBAAL;AAA8B;AAC5B5P,YAAAA,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB,EAA6B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA7B;AACA;AACD;;AAED,aAAK,uBAAL;AAA8B;AAC5B5P,YAAAA,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB,EAA6B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA7B;AACA;AACD;;AAED,aAAK,oBAAL;AAA2B;AACzB5P,YAAAA,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,EAA8B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA9B;AACA;AACD;;AAED,aAAK,mBAAL;AAA0B;AACxB5P,YAAAA,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB,EAA6B;AAAEga,cAAAA,IAAI,EAAE;AAAR,aAA7B;AACA;AACD;;AAED,aAAK,iBAAL;AACE5P,UAAAA,YAAM,CAAC6P,eAAP,CAAuBja,MAAvB;AACA;;AAEF,aAAK,iBAAL;AAAwB;AACtBoK,YAAAA,YAAM,CAAC8P,WAAP,CAAmBla,MAAnB;AACA;AACD;;AAED,aAAK,uBAAL;AACA,aAAK,gBAAL;AACA,aAAK,iBAAL;AACA,aAAK,gBAAL;AACA,aAAK,uBAAL;AACA,aAAK,YAAL;AAAmB;AACjB,gBAAIyV,IAAI,KAAK,uBAAb,EAAsC;AACpC;AACA;AACA;AACA;AACA;AACA,kBAAIhO,WAAW,CAACiG,WAAZ,CAAwB1N,MAAxB,CAAJ,EAAqC;AACnC0oB,gBAAAA,cAAc,CAAC,KAAD,CAAd;AACA7lB,gBAAAA,YAAY,CAACgG,GAAb,CAAiB7I,MAAjB,EAAyB,KAAzB;AACD;AACF,aAXgB;AAcjB;AACA;;;AACA,gBAAI,CAAAuN,IAAI,SAAJ,IAAAA,IAAI,WAAJ,YAAAA,IAAI,CAAE0I,WAAN,CAAkBC,IAAlB,MAA2B,cAA/B,EAA+C;AAC7CzO,cAAAA,WAAW,CAAC6F,UAAZ,CAAuBtN,MAAvB,EAA+BuN,IAA/B;AACD,aAFD,MAEO,IAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;AACnC;AACA;AACA,kBAAIsd,OAAJ,EAAY;AACVlC,gBAAAA,kBAAkB,CAAC3X,OAAnB,CAA2BiI,IAA3B,CAAgC;AAAA,yBAC9B7O,YAAM,CAAC0N,UAAP,CAAkB9X,MAAlB,EAA0BuN,IAA1B,CAD8B;AAAA,iBAAhC;AAGD,eAJD,MAIO;AACLnD,gBAAAA,YAAM,CAAC0N,UAAP,CAAkB9X,MAAlB,EAA0BuN,IAA1B;AACD;AACF;;AAED;AACD;AApGH,OA9IA;;;AAsPA,UAAM+d,SAAS,4BAAGxoB,wBAAwB,CAACwF,GAAzB,CAA6BtI,MAA7B,CAAH,0DAAG,sBAAsCkY,KAAtC,EAAlB;AACApV,MAAAA,wBAAwB,UAAxB,CAAgC9C,MAAhC;;AAEA,UACEsrB,SAAS,KACR,CAACtrB,MAAM,CAACgJ,SAAR,IAAqB,CAACmG,WAAK,CAACiG,MAAN,CAAapV,MAAM,CAACgJ,SAApB,EAA+BsiB,SAA/B,CADd,CADX,EAGE;AACAjiB,QAAAA,gBAAU,CAACyN,MAAX,CAAkB9W,MAAlB,EAA0BsrB,SAA1B;AACD;AACF;AACF,GAxQiC,EAyQlC,CACEtrB,MADF,EAEEqW,oBAFF,EAGE4M,WAHF,EAIEkF,qBAJF,EAKEnH,QALF,EAME5K,4BANF,CAzQkC,CAApC;AAmRA,MAAMuK,WAAW,GAAGrB,iBAAW,CAC7B,UAAAra,IAAI;AACF,QAAIA,IAAI,IAAI,IAAZ,EAAkB;AAChBoR,MAAAA,oBAAoB,CAAC4B,MAArB;AACA7B,MAAAA,4BAA4B,CAAC6B,MAA7B;AAEA5V,MAAAA,iBAAiB,UAAjB,CAAyBrC,MAAzB;AACAwC,MAAAA,eAAe,UAAf,CAAuBxC,MAAvB;;AAEA,UAAIge,GAAG,CAAChN,OAAJ,IAAepP,wBAAnB,EAA6C;AAC3C;AACAoc,QAAAA,GAAG,CAAChN,OAAJ,CAAYua,mBAAZ,CAAgC,aAAhC,EAA+CnD,gBAA/C;AACD;AACF,KAXD,MAWO;AACL;AACA;AACA;AACA;AACA,UAAIxmB,wBAAJ,EAA8B;AAC5B;AACAqD,QAAAA,IAAI,CAACumB,gBAAL,CAAsB,aAAtB,EAAqCpD,gBAArC;AACD;AACF;;AAEDpK,IAAAA,GAAG,CAAChN,OAAJ,GAAc/L,IAAd;AACD,GAzB4B,EA0B7B,CACEoR,oBADF,EAEED,4BAFF,EAGEpW,MAHF,EAIEooB,gBAJF,CA1B6B,CAA/B;AAmCA;AACA;AACA;AACA;;AACA5M,EAAAA,yBAAyB,CAAC;AACxB,QAAM9a,MAAM,GAAG+G,WAAW,CAACC,SAAZ,CAAsB1H,MAAtB,CAAf;AAEAU,IAAAA,MAAM,CAACgB,QAAP,CAAgB8pB,gBAAhB,CACE,iBADF,EAEEpV,4BAFF;AAKA,WAAO;AACL1V,MAAAA,MAAM,CAACgB,QAAP,CAAgB6pB,mBAAhB,CACE,iBADF,EAEEnV,4BAFF;AAID,KALD;AAMD,GAdwB,EActB,CAACA,4BAAD,CAdsB,CAAzB;AAgBA,MAAMoK,WAAW,GAAGsB,QAAQ,CAAC,CAAC9hB,MAAD,EAAS,EAAT,CAAD,CAA5B;AAEA,MAAMkf,eAAe,GACnBW,WAAW,IACX7f,MAAM,CAAC8R,QAAP,CAAgBhN,MAAhB,KAA2B,CAD3B,IAEAyB,KAAK,CAACC,IAAN,CAAWpC,UAAI,CAACgK,KAAL,CAAWpO,MAAX,CAAX,EAA+B8E,MAA/B,KAA0C,CAF1C,IAGAV,UAAI,CAACwZ,MAAL,CAAY5d,MAAZ,MAAwB,EAHxB,IAIA,CAAC0N,WALH;AAOA,MAAM+d,wBAAwB,GAAGnM,iBAAW,CAC1C,UAACC,aAAD;AACE,QAAIA,aAAa,IAAIL,eAArB,EAAsC;AAAA;;AACpC2J,MAAAA,oBAAoB,0BAACtJ,aAAa,CAAChV,qBAAd,EAAD,0DAAC,sBAAuCM,MAAxC,CAApB;AACD,KAFD,MAEO;AACLge,MAAAA,oBAAoB,CAAChR,SAAD,CAApB;AACD;AACF,GAPyC,EAQ1C,CAACqH,eAAD,CAR0C,CAA5C;;AAWA,MAAIA,eAAJ,EAAqB;AAAA;;AACnB,QAAM5Q,KAAK,GAAGlE,YAAM,CAACkE,KAAP,CAAatO,MAAb,EAAqB,EAArB,CAAd;AACAwgB,IAAAA,WAAW,CAACvH,IAAZ,6DACG1V,kBADH,EACwB,IADxB,qDAEEsc,WAFF,6DAGuB4L,wBAHvB,gDAIUnd,KAJV,+CAKSA,KALT;AAOD;;AAED,MAAQoJ,KAAR,GAAkB1X,MAAlB,CAAQ0X,KAAR;AACAuR,EAAAA,KAAK,CAACK,kBAAN,GAA2B,KAA3B;;AAEA,MAAItpB,MAAM,CAACgJ,SAAP,IAAoBmG,WAAK,CAACG,WAAN,CAAkBtP,MAAM,CAACgJ,SAAzB,CAApB,IAA2D0O,KAA/D,EAAsE;AACpE,QAAQzK,MAAR,GAAmBjN,MAAM,CAACgJ,SAA1B,CAAQiE,MAAR;AACA,QAAMyH,IAAI,GAAGtQ,UAAI,CAACsQ,IAAL,CAAU1U,MAAV,EAAkBiN,MAAM,CAACjD,IAAzB,CAAb;;AACA,IAA0B0K,IAA1B,CAAQrO,IAAR;AAAA,YAAiBqlB,IAAjB,4BAA0BhX,IAA1B,cAHoE;AAMpE;;;AACA,QAAI,CAAC/Q,UAAI,CAACyR,MAAL,CAAYV,IAAZ,EAAkBgD,KAAlB,EAAiC;AAAEiU,MAAAA,KAAK,EAAE;AAAT,KAAjC,CAAL,EAAwD;AACtD1C,MAAAA,KAAK,CAACK,kBAAN,GAA2B,IAA3B;AAEA,UAAMsC,KAAK,GAAGhP,MAAM,CAACiP,WAAP,CACZjP,MAAM,CAACC,IAAP,CAAY6O,IAAZ,EAAkBI,GAAlB,CAAsB,UAAAC,IAAI;AAAA,eAAI,CAACA,IAAD,EAAO,IAAP,CAAJ;AAAA,OAA1B,CADY,CAAd;AAIAvL,MAAAA,WAAW,CAACvH,IAAZ,qEACGxV,uBADH,EAC6B,IAD7B,GAEKmoB,KAFL,GAGKlU,KAHL;AAKEzK,QAAAA,MAAM,EAANA,MALF;AAMEb,QAAAA,KAAK,EAAEa;AANT;AAQD;AACF;AAGD;;;AACAsO,EAAAA,eAAS,CAAC;AACRhE,IAAAA,UAAU,CAAC;AACT,UAAQvO,SAAR,GAAsBhJ,MAAtB,CAAQgJ,SAAR;;AACA,UAAIA,SAAJ,EAAe;AACb,YAAQiE,OAAR,GAAmBjE,SAAnB,CAAQiE,MAAR;;AACA,YAAM5G,KAAI,GAAGjC,UAAI,CAACsQ,IAAL,CAAU1U,MAAV,EAAkBiN,OAAM,CAACjD,IAAzB,CAAb,CAFa;AAKb;;;AACA,YAAI0N,KAAK,IAAI,CAAC/T,UAAI,CAACyR,MAAL,CAAY/O,KAAZ,EAAkBqR,KAAlB,EAAiC;AAAEiU,UAAAA,KAAK,EAAE;AAAT,SAAjC,CAAd,EAAiE;AAC/D1oB,UAAAA,iCAAiC,CAAC4F,GAAlC,CAAsC7I,MAAtC,EAA8C0X,KAA9C;AACA;AACD;AACF;;AAEDzU,MAAAA,iCAAiC,UAAjC,CAAyCjD,MAAzC;AACD,KAfS,CAAV;AAgBD,GAjBQ,CAAT;AAmBA,sBACEK,uCAAA,CAACoiB,eAAe,CAACH,QAAjB;AAA0Bze,IAAAA,KAAK,EAAEmd;GAAjC,eACE3gB,uCAAA,CAACqhB,eAAe,CAACY,QAAjB;AAA0Bze,IAAAA,KAAK,EAAEie;GAAjC,eACEzhB,uCAAA,CAACunB,UAAD;AAAY3iB,IAAAA,IAAI,EAAE+Y;AAAK+E,IAAAA,iBAAiB,EAAEA;GAA1C,eACE1iB,uCAAA,CAACsnB,SAAD;AACEqE,IAAAA,IAAI,EAAEhL,QAAQ,GAAGnJ,SAAH,GAAe;sBACbmJ,QAAQ,GAAGnJ,SAAH,GAAe;KACnC0G;AACJ;AACA;AACA;AACA;AACA;AACA0N,IAAAA,UAAU,EACRrqB,wBAAwB,IAAI,CAACH,WAA7B,GACI8c,UAAU,CAAC0N,UADf,GAEI;AAENC,IAAAA,WAAW,EACTtqB,wBAAwB,IAAI,CAACH,WAA7B,GACI8c,UAAU,CAAC2N,WADf,GAEI;AAENC,IAAAA,cAAc,EACZvqB,wBAAwB,IAAI,CAACH,WAA7B,GACI8c,UAAU,CAAC4N,cADf,GAEI;;uBAGU;AAChB;AACA/L,IAAAA,eAAe,EAAE,CAACY;AAClB;AACA;AACA;AACAoL,IAAAA,MAAM,EAAE,CAAC;AACTC,IAAAA,8BAA8B;AAC9BrO,IAAAA,GAAG,EAAE2C;AACLhI,IAAAA,KAAK,sCACC8P,oBAAoB,GACpB,EADoB;AAGlB;AACApd,MAAAA,QAAQ,EAAE,UAJQ;AAKlB;AACA8f,MAAAA,UAAU,EAAE,UANM;AAOlB;AACAmB,MAAAA,QAAQ,EAAE;AARQ,OAUd1D,iBAAiB,GACjB;AAAE2D,MAAAA,SAAS,EAAE3D;AAAb,KADiB,GAEjB,EAZc,CADrB,GAgBAL,SAhBA;AAkBLiE,IAAAA,aAAa,EAAElN,iBAAW,CACxB,UAAC5a,KAAD;AACE;AACA;AACA;AACA,UACE,CAAC9C,wBAAD,IACA,CAACof,QADD,IAEA,CAACyL,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAACiO,aAAnB,CAFf,IAGA/kB,WAAW,CAAC0F,mBAAZ,CAAgCnN,MAAhC,EAAwC0E,KAAK,CAAC6C,MAA9C,CAJF,EAKE;AACA7C,QAAAA,KAAK,CAAC2mB,cAAN;;AACA,YAAI,CAAC5jB,WAAW,CAACiG,WAAZ,CAAwB1N,MAAxB,CAAL,EAAsC;AACpC,cAAMqG,MAAI,GAAI3B,KAAa,CAAC6I,IAA5B;AACAnD,UAAAA,YAAM,CAAC0N,UAAP,CAAkB9X,MAAlB,EAA0BqG,MAA1B;AACD;AACF;AACF,KAjBuB,EAkBxB,CAACkY,UAAU,CAACiO,aAAZ,EAA2BxsB,MAA3B,EAAmCghB,QAAnC,CAlBwB;AAoB1B0L,IAAAA,OAAO,EAAEpN,iBAAW,CAClB,UAAC5a,KAAD;AACE,UAAI+nB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAACmO,OAAnB,CAAlB,EAA+C;AAC7C;AACD;;AAED,UAAInD,sBAAsB,CAACvY,OAA3B,EAAoC;AAClCuY,QAAAA,sBAAsB,CAACvY,OAAvB,CAA+B8J,WAA/B;AACA;AACD;AAGD;AACA;AACA;;;mDACiB6N,kBAAkB,CAAC3X;;;;AAApC,4DAA6C;AAAA,cAAlCkE,EAAkC;AAC3CA,UAAAA,EAAE;AACH;;;;;;;AACDyT,MAAAA,kBAAkB,CAAC3X,OAAnB,GAA6B,EAA7B;AACD,KAnBiB,EAoBlB,CAACuN,UAAU,CAACmO,OAAZ,CApBkB;AAsBpBC,IAAAA,MAAM,EAAErN,iBAAW,CACjB,UAAC5a,KAAD;AACE,UACEsc,QAAQ,IACRiI,KAAK,CAACG,mBADN,IAEA,CAAC3hB,WAAW,CAAC0F,mBAAZ,CAAgCnN,MAAhC,EAAwC0E,KAAK,CAAC6C,MAA9C,CAFD,IAGAklB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAACoO,MAAnB,CAJhB,EAKE;AACA;AACD;AAGD;AACA;AACA;;;AACA,UAAMhkB,IAAI,GAAGlB,WAAW,CAACmB,wBAAZ,CAAqC5I,MAArC,CAAb;;AACA,UAAIipB,KAAK,CAACI,aAAN,KAAwB1gB,IAAI,CAACG,aAAjC,EAAgD;AAC9C;AACD;;AAED,UAAQ8jB,aAAR,GAA0BloB,KAA1B,CAAQkoB,aAAR;AACA,UAAMnkB,EAAE,GAAGhB,WAAW,CAACiB,SAAZ,CAAsB1I,MAAtB,EAA8BA,MAA9B,CAAX;AAGA;AACA;;AACA,UAAI4sB,aAAa,KAAKnkB,EAAtB,EAA0B;AACxB;AACD;AAGD;;;AACA,UACEtE,YAAY,CAACyoB,aAAD,CAAZ,IACAA,aAAa,CAACje,YAAd,CAA2B,mBAA3B,CAFF,EAGE;AACA;AACD;AAGD;AACA;;;AACA,UACEie,aAAa,IAAI,IAAjB,IACA3oB,SAAS,CAAC2oB,aAAD,CADT,IAEAnlB,WAAW,CAACG,UAAZ,CAAuB5H,MAAvB,EAA+B4sB,aAA/B,CAHF,EAIE;AACA,YAAM3nB,IAAI,GAAGwC,WAAW,CAACsC,WAAZ,CAAwB/J,MAAxB,EAAgC4sB,aAAhC,CAAb;;AAEA,YAAI1iB,aAAO,CAACC,SAAR,CAAkBlF,IAAlB,KAA2B,CAACjF,MAAM,CAACqK,MAAP,CAAcpF,IAAd,CAAhC,EAAqD;AACnD;AACD;AACF;AAGD;AACA;;;AACA,UAAIhE,SAAJ,EAAe;AACb,YAAMgI,YAAY,GAAGN,IAAI,CAACO,YAAL,EAArB;AACAD,QAAAA,YAAY,SAAZ,IAAAA,YAAY,WAAZ,YAAAA,YAAY,CAAEG,eAAd;AACD;;AAEDxG,MAAAA,UAAU,UAAV,CAAkB5C,MAAlB;AACD,KA/DgB,EAgEjB,CACEghB,QADF,EAEEiI,KAAK,CAACG,mBAFR,EAGEH,KAAK,CAACI,aAHR,EAIErpB,MAJF,EAKEue,UAAU,CAACoO,MALb,CAhEiB;AAwEnBE,IAAAA,OAAO,EAAEvN,iBAAW,CAClB,UAAC5a,KAAD;AACE,UACE+C,WAAW,CAAC4F,SAAZ,CAAsBrN,MAAtB,EAA8B0E,KAAK,CAAC6C,MAApC,KACA,CAACklB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAACsO,OAAnB,CADf,IAEA5oB,SAAS,CAACS,KAAK,CAAC6C,MAAP,CAHX,EAIE;AACA,YAAMtC,IAAI,GAAGwC,WAAW,CAACsC,WAAZ,CAAwB/J,MAAxB,EAAgC0E,KAAK,CAAC6C,MAAtC,CAAb;AACA,YAAMyC,IAAI,GAAGvC,WAAW,CAACwC,QAAZ,CAAqBjK,MAArB,EAA6BiF,IAA7B,CAAb,CAFA;AAKA;AACA;AACA;;AACA,YACE,CAACmF,YAAM,CAAC8C,OAAP,CAAelN,MAAf,EAAuBgK,IAAvB,CAAD,IACA5F,UAAI,CAACkE,GAAL,CAAStI,MAAT,EAAiBgK,IAAjB,MAA2B/E,IAF7B,EAGE;AACA;AACD;;AAED,YAAIP,KAAK,CAACooB,MAAN,KAAiB1J,YAAjB,IAAiCpZ,IAAI,CAAClF,MAAL,IAAe,CAApD,EAAuD;AACrD,cAAIioB,SAAS,GAAG/iB,IAAhB;;AACA,cACE,EAAEE,aAAO,CAACC,SAAR,CAAkBlF,IAAlB,KAA2BmF,YAAM,CAACyK,OAAP,CAAe7U,MAAf,EAAuBiF,IAAvB,CAA7B,CADF,EAEE;AAAA;;AACA,gBAAMmmB,KAAK,GAAGhhB,YAAM,CAACwK,KAAP,CAAa5U,MAAb,EAAqB;AACjCkH,cAAAA,KAAK,EAAE,eAAAgB,CAAC;AAAA,uBACNgC,aAAO,CAACC,SAAR,CAAkBjC,CAAlB,KAAwBkC,YAAM,CAACyK,OAAP,CAAe7U,MAAf,EAAuBkI,CAAvB,CADlB;AAAA,eADyB;AAGjCgG,cAAAA,EAAE,EAAElE;AAH6B,aAArB,CAAd;AAMA+iB,YAAAA,SAAS,cAAG3B,KAAH,aAAGA,KAAH,uBAAGA,KAAK,CAAG,CAAH,CAAR,6CAAiBphB,IAAI,CAAC0G,KAAL,CAAW,CAAX,EAAc,CAAd,CAA1B;AACD;;AAED,cAAMxF,KAAK,GAAGd,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqB+sB,SAArB,CAAd;AACA1jB,UAAAA,gBAAU,CAACyN,MAAX,CAAkB9W,MAAlB,EAA0BkL,KAA1B;AACA;AACD;;AAED,YAAI8V,QAAJ,EAAc;AACZ;AACD;;AAED,YAAM1S,MAAK,GAAGlE,YAAM,CAACkE,KAAP,CAAatO,MAAb,EAAqBgK,IAArB,CAAd;;AACA,YAAMyE,GAAG,GAAGrE,YAAM,CAACqE,GAAP,CAAWzO,MAAX,EAAmBgK,IAAnB,CAAZ;AACA,YAAMgjB,SAAS,GAAG5iB,YAAM,QAAN,CAAYpK,MAAZ,EAAoB;AAAEkO,UAAAA,EAAE,EAAEI;AAAN,SAApB,CAAlB;AACA,YAAM2e,OAAO,GAAG7iB,YAAM,QAAN,CAAYpK,MAAZ,EAAoB;AAAEkO,UAAAA,EAAE,EAAEO;AAAN,SAApB,CAAhB;;AAEA,YACEue,SAAS,IACTC,OADA,IAEAha,UAAI,CAACmC,MAAL,CAAY4X,SAAS,CAAC,CAAD,CAArB,EAA0BC,OAAO,CAAC,CAAD,CAAjC,CAHF,EAIE;AACA,cAAM/hB,MAAK,GAAGd,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqBsO,MAArB,CAAd;;AACAjF,UAAAA,gBAAU,CAACyN,MAAX,CAAkB9W,MAAlB,EAA0BkL,MAA1B;AACD;AACF;AACF,KA1DiB,EA2DlB,CAAClL,MAAD,EAASue,UAAU,CAACsO,OAApB,EAA6B7L,QAA7B,CA3DkB;AA6DpBkM,IAAAA,gBAAgB,EAAE5N,iBAAW,CAC3B,UAAC5a,KAAD;AACE,UAAI+C,WAAW,CAAC0F,mBAAZ,CAAgCnN,MAAhC,EAAwC0E,KAAK,CAAC6C,MAA9C,CAAJ,EAA2D;AAAA;;AACzD,YAAIE,WAAW,CAACiG,WAAZ,CAAwB1N,MAAxB,CAAJ,EAAqC;AACnC0oB,UAAAA,cAAc,CAAC,KAAD,CAAd;AACA7lB,UAAAA,YAAY,CAACgG,GAAb,CAAiB7I,MAAjB,EAAyB,KAAzB;AACD;;AAED,kCAAAupB,sBAAsB,CAACvY,OAAvB,kFAAgCqH,oBAAhC,CAAqD3T,KAArD;;AAEA,YACE+nB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAAC2O,gBAAnB,CAAd,IACAnsB,UAFF,EAGE;AACA;AACD,SAbwD;AAgBzD;AACA;AACA;;;AACA,YACE,CAACE,SAAD,IACA,CAACK,iBADD,IAEA,CAACd,MAFD,IAGA,CAACgB,gBAHD,IAIA,CAACD,YAJD,IAKAmD,KAAK,CAAC6I,IANR,EAOE;AACA,cAAM4f,gBAAgB,GAAGlqB,iCAAiC,CAACqF,GAAlC,CACvBtI,MADuB,CAAzB;AAGAiD,UAAAA,iCAAiC,UAAjC,CAAyCjD,MAAzC,EAJA;;AAOA,cAAImtB,gBAAgB,KAAKtV,SAAzB,EAAoC;AAClC3U,YAAAA,oBAAoB,CAAC2F,GAArB,CAAyB7I,MAAzB,EAAiCA,MAAM,CAAC0X,KAAxC;AACA1X,YAAAA,MAAM,CAAC0X,KAAP,GAAeyV,gBAAf;AACD;;AAED/iB,UAAAA,YAAM,CAAC0N,UAAP,CAAkB9X,MAAlB,EAA0B0E,KAAK,CAAC6I,IAAhC;AAEA,cAAM4K,SAAS,GAAGjV,oBAAoB,CAACoF,GAArB,CAAyBtI,MAAzB,CAAlB;AACAkD,UAAAA,oBAAoB,UAApB,CAA4BlD,MAA5B;;AACA,cAAImY,SAAS,KAAKN,SAAlB,EAA6B;AAC3B7X,YAAAA,MAAM,CAAC0X,KAAP,GAAeS,SAAf;AACD;AACF;AACF;AACF,KAjD0B,EAkD3B,CAACoG,UAAU,CAAC2O,gBAAZ,EAA8BltB,MAA9B,CAlD2B;AAoD7BotB,IAAAA,mBAAmB,EAAE9N,iBAAW,CAC9B,UAAC5a,KAAD;AACE,UACE+C,WAAW,CAAC0F,mBAAZ,CAAgCnN,MAAhC,EAAwC0E,KAAK,CAAC6C,MAA9C,KACA,CAACklB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAAC6O,mBAAnB,CAFjB,EAGE;AACA,YAAI,CAAC3lB,WAAW,CAACiG,WAAZ,CAAwB1N,MAAxB,CAAL,EAAsC;AACpC0oB,UAAAA,cAAc,CAAC,IAAD,CAAd;AACA7lB,UAAAA,YAAY,CAACgG,GAAb,CAAiB7I,MAAjB,EAAyB,IAAzB;AACD;AACF;AACF,KAX6B,EAY9B,CAACue,UAAU,CAAC6O,mBAAZ,EAAiCptB,MAAjC,CAZ8B;AAchCqtB,IAAAA,kBAAkB,EAAE/N,iBAAW,CAC7B,UAAC5a,KAAD;AACE,UAAI+C,WAAW,CAAC0F,mBAAZ,CAAgCnN,MAAhC,EAAwC0E,KAAK,CAAC6C,MAA9C,CAAJ,EAA2D;AAAA;;AACzD,kCAAAgiB,sBAAsB,CAACvY,OAAvB,kFAAgCuH,sBAAhC,CAAuD7T,KAAvD;;AAEA,YACE+nB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAAC8O,kBAAnB,CAAd,IACAtsB,UAFF,EAGE;AACA;AACD;;AAED2nB,QAAAA,cAAc,CAAC,IAAD,CAAd;AAEA,YAAQ1f,SAAR,GAAsBhJ,MAAtB,CAAQgJ,SAAR;;AACA,YAAIA,SAAJ,EAAe;AACb,cAAImG,WAAK,CAACoD,UAAN,CAAiBvJ,SAAjB,CAAJ,EAAiC;AAC/BoB,YAAAA,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB;AACA;AACD;;AACD,cAAMstB,MAAM,GAAGljB,YAAM,CAACwK,KAAP,CAAa5U,MAAb,EAAqB;AAClCkH,YAAAA,KAAK,EAAE,eAAAgB,CAAC;AAAA,qBACNgC,aAAO,CAACC,SAAR,CAAkBjC,CAAlB,KAAwBkC,YAAM,CAACK,QAAP,CAAgBzK,MAAhB,EAAwBkI,CAAxB,CADlB;AAAA,aAD0B;AAGlCuK,YAAAA,IAAI,EAAE;AAH4B,WAArB,CAAf;;AAKA,cAAI6a,MAAJ,EAAY;AACV,yCAAuBA,MAAvB;AAAA,gBAASC,UAAT;;AACA,gBAAInjB,YAAM,CAACojB,KAAP,CAAaxtB,MAAb,EAAqBgJ,SAAS,CAACiE,MAA/B,EAAuCsgB,UAAvC,CAAJ,EAAwD;AACtD,kBAAMxiB,KAAK,GAAGX,YAAM,CAACa,KAAP,CAAajL,MAAb,EAAqButB,UAArB,CAAd;AACAlkB,cAAAA,gBAAU,CAACokB,YAAX,CAAwBztB,MAAxB,EAAgC;AAC9BiN,gBAAAA,MAAM,EAAElC,KADsB;AAE9BqB,gBAAAA,KAAK,EAAErB;AAFuB,eAAhC;AAID;AACF;AACF;AACF;AACF,KArC4B,EAsC7B,CAACwT,UAAU,CAAC8O,kBAAZ,EAAgCrtB,MAAhC,CAtC6B;AAwC/B0tB,IAAAA,MAAM,EAAEpO,iBAAW,CACjB,UAAC5a,KAAD;AACE,UACE+C,WAAW,CAAC0F,mBAAZ,CAAgCnN,MAAhC,EAAwC0E,KAAK,CAAC6C,MAA9C,KACA,CAACklB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAACmP,MAAnB,CADf,IAEA,CAACC,qBAAqB,CAACjpB,KAAD,CAHxB,EAIE;AACAA,QAAAA,KAAK,CAAC2mB,cAAN;AACA5jB,QAAAA,WAAW,CAACqG,eAAZ,CACE9N,MADF,EAEE0E,KAAK,CAACC,aAFR,EAGE,MAHF;AAKD;AACF,KAdgB,EAejB,CAAC4Z,UAAU,CAACmP,MAAZ,EAAoB1tB,MAApB,CAfiB;AAiBnB4tB,IAAAA,KAAK,EAAEtO,iBAAW,CAChB,UAAC5a,KAAD;AACE,UACE,CAACsc,QAAD,IACAvZ,WAAW,CAAC0F,mBAAZ,CAAgCnN,MAAhC,EAAwC0E,KAAK,CAAC6C,MAA9C,CADA,IAEA,CAACklB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAACqP,KAAnB,CAFf,IAGA,CAACD,qBAAqB,CAACjpB,KAAD,CAJxB,EAKE;AACAA,QAAAA,KAAK,CAAC2mB,cAAN;AACA5jB,QAAAA,WAAW,CAACqG,eAAZ,CACE9N,MADF,EAEE0E,KAAK,CAACC,aAFR,EAGE,KAHF;AAKA,YAAQqE,SAAR,GAAsBhJ,MAAtB,CAAQgJ,SAAR;;AAEA,YAAIA,SAAJ,EAAe;AACb,cAAImG,WAAK,CAACoD,UAAN,CAAiBvJ,SAAjB,CAAJ,EAAiC;AAC/BoB,YAAAA,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB;AACD,WAFD,MAEO;AACL,gBAAMiF,IAAI,GAAGb,UAAI,CAACuB,MAAL,CAAY3F,MAAZ,EAAoBgJ,SAAS,CAACiE,MAAV,CAAiBjD,IAArC,CAAb;;AACA,gBAAII,YAAM,CAACC,MAAP,CAAcrK,MAAd,EAAsBiF,IAAtB,CAAJ,EAAiC;AAC/BoE,cAAAA,gBAAU,UAAV,CAAkBrJ,MAAlB;AACD;AACF;AACF;AACF;AACF,KA3Be,EA4BhB,CAACghB,QAAD,EAAWhhB,MAAX,EAAmBue,UAAU,CAACqP,KAA9B,CA5BgB;AA8BlBC,IAAAA,UAAU,EAAEvO,iBAAW,CACrB,UAAC5a,KAAD;AACE,UACE+C,WAAW,CAAC4F,SAAZ,CAAsBrN,MAAtB,EAA8B0E,KAAK,CAAC6C,MAApC,KACA,CAACklB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAACsP,UAAnB,CAFjB,EAGE;AACA;AACA;AACA;AACA,YAAM5oB,IAAI,GAAGwC,WAAW,CAACsC,WAAZ,CAAwB/J,MAAxB,EAAgC0E,KAAK,CAAC6C,MAAtC,CAAb;;AAEA,YAAI2C,aAAO,CAACC,SAAR,CAAkBlF,IAAlB,KAA2BmF,YAAM,CAACC,MAAP,CAAcrK,MAAd,EAAsBiF,IAAtB,CAA/B,EAA4D;AAC1DP,UAAAA,KAAK,CAAC2mB,cAAN;AACD;AACF;AACF,KAfoB,EAgBrB,CAAC9M,UAAU,CAACsP,UAAZ,EAAwB7tB,MAAxB,CAhBqB;AAkBvB8tB,IAAAA,WAAW,EAAExO,iBAAW,CACtB,UAAC5a,KAAD;AACE,UACE,CAACsc,QAAD,IACAvZ,WAAW,CAAC4F,SAAZ,CAAsBrN,MAAtB,EAA8B0E,KAAK,CAAC6C,MAApC,CADA,IAEA,CAACklB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAACuP,WAAnB,CAHjB,EAIE;AACA,YAAM7oB,IAAI,GAAGwC,WAAW,CAACsC,WAAZ,CAAwB/J,MAAxB,EAAgC0E,KAAK,CAAC6C,MAAtC,CAAb;AACA,YAAMyC,IAAI,GAAGvC,WAAW,CAACwC,QAAZ,CAAqBjK,MAArB,EAA6BiF,IAA7B,CAAb;AACA,YAAM8oB,SAAS,GACZ7jB,aAAO,CAACC,SAAR,CAAkBlF,IAAlB,KAA2BmF,YAAM,CAACC,MAAP,CAAcrK,MAAd,EAAsBiF,IAAtB,CAA5B,IACAmF,YAAM,QAAN,CAAYpK,MAAZ,EAAoB;AAAEkO,UAAAA,EAAE,EAAElE,IAAN;AAAY2I,UAAAA,KAAK,EAAE;AAAnB,SAApB,CAFF,CAHA;AAQA;;AACA,YAAIob,SAAJ,EAAe;AACb,cAAM7iB,KAAK,GAAGd,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqBgK,IAArB,CAAd;AACAX,UAAAA,gBAAU,CAACyN,MAAX,CAAkB9W,MAAlB,EAA0BkL,KAA1B;AACD;;AAED+d,QAAAA,KAAK,CAACE,oBAAN,GAA6B,IAA7B;AAEA1hB,QAAAA,WAAW,CAACqG,eAAZ,CACE9N,MADF,EAEE0E,KAAK,CAACsC,YAFR,EAGE,MAHF;AAKD;AACF,KA5BqB,EA6BtB,CAACga,QAAD,EAAWhhB,MAAX,EAAmBue,UAAU,CAACuP,WAA9B,EAA2C7E,KAA3C,CA7BsB;AA+BxB+E,IAAAA,MAAM,EAAE1O,iBAAW,CACjB,UAAC5a,KAAD;AACE,UACE,CAACsc,QAAD,IACAvZ,WAAW,CAAC4F,SAAZ,CAAsBrN,MAAtB,EAA8B0E,KAAK,CAAC6C,MAApC,CADA,IAEA,CAACklB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAACyP,MAAnB,CAHjB,EAIE;AACAtpB,QAAAA,KAAK,CAAC2mB,cAAN,GADA;;AAIA,YAAM4C,YAAY,GAAGjuB,MAAM,CAACgJ,SAA5B,CAJA;;AAOA,YAAMkC,KAAK,GAAGzD,WAAW,CAACgC,cAAZ,CAA2BzJ,MAA3B,EAAmC0E,KAAnC,CAAd;AACA,YAAM6I,IAAI,GAAG7I,KAAK,CAACsC,YAAnB;AAEAqC,QAAAA,gBAAU,CAACyN,MAAX,CAAkB9W,MAAlB,EAA0BkL,KAA1B;;AAEA,YAAI+d,KAAK,CAACE,oBAAV,EAAgC;AAC9B,cACE8E,YAAY,IACZ,CAAC9e,WAAK,CAACiG,MAAN,CAAa6Y,YAAb,EAA2B/iB,KAA3B,CADD,IAEA,CAACd,YAAM,QAAN,CAAYpK,MAAZ,EAAoB;AAAEkO,YAAAA,EAAE,EAAEhD,KAAN;AAAayH,YAAAA,KAAK,EAAE;AAApB,WAApB,CAHH,EAIE;AACAtJ,YAAAA,gBAAU,UAAV,CAAkBrJ,MAAlB,EAA0B;AACxBkO,cAAAA,EAAE,EAAE+f;AADoB,aAA1B;AAGD;AACF;;AAEDxmB,QAAAA,WAAW,CAAC6F,UAAZ,CAAuBtN,MAAvB,EAA+BuN,IAA/B,EAxBA;AA2BA;;AACA,YAAI,CAAC9F,WAAW,CAACkG,SAAZ,CAAsB3N,MAAtB,CAAL,EAAoC;AAClCyH,UAAAA,WAAW,CAAC2E,KAAZ,CAAkBpM,MAAlB;AACD;AACF;;AAEDipB,MAAAA,KAAK,CAACE,oBAAN,GAA6B,KAA7B;AACD,KAxCgB,EAyCjB,CAACnI,QAAD,EAAWhhB,MAAX,EAAmBue,UAAU,CAACyP,MAA9B,EAAsC/E,KAAtC,CAzCiB;AA2CnBiF,IAAAA,SAAS,EAAE5O,iBAAW,CACpB,UAAC5a,KAAD;AACE,UACE,CAACsc,QAAD,IACAiI,KAAK,CAACE,oBADN,IAEA5K,UAAU,CAAC2P,SAFX,IAGAzmB,WAAW,CAAC4F,SAAZ,CAAsBrN,MAAtB,EAA8B0E,KAAK,CAAC6C,MAApC,CAJF,EAKE;AACAgX,QAAAA,UAAU,CAAC2P,SAAX,CAAqBxpB,KAArB;AACD;AAGD;AACA;;;AACAukB,MAAAA,KAAK,CAACE,oBAAN,GAA6B,KAA7B;AACD,KAfmB,EAgBpB,CAACnI,QAAD,EAAWiI,KAAX,EAAkB1K,UAAlB,EAA8Bve,MAA9B,CAhBoB;AAkBtBmuB,IAAAA,OAAO,EAAE7O,iBAAW,CAClB,UAAC5a,KAAD;AACE,UACE,CAACsc,QAAD,IACA,CAACiI,KAAK,CAACG,mBADP,IAEA3hB,WAAW,CAACsF,iBAAZ,CAA8B/M,MAA9B,EAAsC0E,KAAK,CAAC6C,MAA5C,CAFA,IAGA,CAACklB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAAC4P,OAAnB,CAJjB,EAKE;AACA,YAAM1lB,EAAE,GAAGhB,WAAW,CAACiB,SAAZ,CAAsB1I,MAAtB,EAA8BA,MAA9B,CAAX;AACA,YAAM2I,IAAI,GAAGlB,WAAW,CAACmB,wBAAZ,CAAqC5I,MAArC,CAAb;AACAipB,QAAAA,KAAK,CAACI,aAAN,GAAsB1gB,IAAI,CAACG,aAA3B,CAHA;AAMA;AACA;;AACA,YAAI9H,UAAU,IAAI0D,KAAK,CAAC6C,MAAN,KAAiBkB,EAAnC,EAAuC;AACrCA,UAAAA,EAAE,CAAC2D,KAAH;AACA;AACD;;AAEDxJ,QAAAA,UAAU,CAACiG,GAAX,CAAe7I,MAAf,EAAuB,IAAvB;AACD;AACF,KAtBiB,EAuBlB,CAACghB,QAAD,EAAWiI,KAAX,EAAkBjpB,MAAlB,EAA0Bue,UAAU,CAAC4P,OAArC,CAvBkB;AAyBpBC,IAAAA,SAAS,EAAE9O,iBAAW,CACpB,UAAC5a,KAAD;AACE,UACE,CAACsc,QAAD,IACAvZ,WAAW,CAACsF,iBAAZ,CAA8B/M,MAA9B,EAAsC0E,KAAK,CAAC6C,MAA5C,CAFF,EAGE;AAAA;;AACA,kCAAAgiB,sBAAsB,CAACvY,OAAvB,kFAAgC+J,aAAhC,CAA8CrW,KAA9C;AAEA,YAAQgF,WAAR,GAAwBhF,KAAxB,CAAQgF,WAAR,CAHA;AAMA;AACA;;AACA,YACEjC,WAAW,CAACiG,WAAZ,CAAwB1N,MAAxB,KACA0J,WAAW,CAACgE,WAAZ,KAA4B,KAF9B,EAGE;AACA7K,UAAAA,YAAY,CAACgG,GAAb,CAAiB7I,MAAjB,EAAyB,KAAzB;AACA0oB,UAAAA,cAAc,CAAC,KAAD,CAAd;AACD;;AAED,YACE+D,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAAC6P,SAAnB,CAAd,IACA3mB,WAAW,CAACiG,WAAZ,CAAwB1N,MAAxB,CAFF,EAGE;AACA;AACD;;AAED,YAAQgJ,SAAR,GAAsBhJ,MAAtB,CAAQgJ,SAAR;AACA,YAAM4I,OAAO,GACX5R,MAAM,CAAC8R,QAAP,CACE9I,SAAS,KAAK,IAAd,GAAqBA,SAAS,CAACoD,KAAV,CAAgBpC,IAAhB,CAAqB,CAArB,CAArB,GAA+C,CADjD,CADF;AAIA,YAAMqkB,KAAK,GAAGhN,gCAAY,CAACjd,UAAI,CAACwZ,MAAL,CAAYhM,OAAZ,CAAD,CAAZ,KAAuC,KAArD,CA5BA;AA+BA;AACA;AACA;;AACA,YAAI0c,OAAO,CAAC9H,MAAR,CAAe9c,WAAf,CAAJ,EAAiC;AAC/BhF,UAAAA,KAAK,CAAC2mB,cAAN;AACA,cAAMkD,kBAAkB,GAAQvuB,MAAhC;;AAEA,cAAI,OAAOuuB,kBAAkB,CAAC7J,IAA1B,KAAmC,UAAvC,EAAmD;AACjD6J,YAAAA,kBAAkB,CAAC7J,IAAnB;AACD;;AAED;AACD;;AAED,YAAI4J,OAAO,CAAC1H,MAAR,CAAeld,WAAf,CAAJ,EAAiC;AAC/BhF,UAAAA,KAAK,CAAC2mB,cAAN;AACA,cAAMkD,mBAAkB,GAAQvuB,MAAhC;;AAEA,cAAI,OAAOuuB,mBAAkB,CAACvK,IAA1B,KAAmC,UAAvC,EAAmD;AACjDuK,YAAAA,mBAAkB,CAACvK,IAAnB;AACD;;AAED;AACD,SAtDD;AAyDA;AACA;AACA;;;AACA,YAAIsK,OAAO,CAAClI,kBAAR,CAA2B1c,WAA3B,CAAJ,EAA6C;AAC3ChF,UAAAA,KAAK,CAAC2mB,cAAN;AACAhiB,UAAAA,gBAAU,CAACmlB,IAAX,CAAgBxuB,MAAhB,EAAwB;AAAEga,YAAAA,IAAI,EAAE,MAAR;AAAgBoN,YAAAA,OAAO,EAAE;AAAzB,WAAxB;AACA;AACD;;AAED,YAAIkH,OAAO,CAACjI,iBAAR,CAA0B3c,WAA1B,CAAJ,EAA4C;AAC1ChF,UAAAA,KAAK,CAAC2mB,cAAN;AACAhiB,UAAAA,gBAAU,CAACmlB,IAAX,CAAgBxuB,MAAhB,EAAwB;AAAEga,YAAAA,IAAI,EAAE;AAAR,WAAxB;AACA;AACD;;AAED,YAAIsU,OAAO,CAACrI,oBAAR,CAA6Bvc,WAA7B,CAAJ,EAA+C;AAC7ChF,UAAAA,KAAK,CAAC2mB,cAAN;AACAhiB,UAAAA,gBAAU,CAACmlB,IAAX,CAAgBxuB,MAAhB,EAAwB;AACtBga,YAAAA,IAAI,EAAE,MADgB;AAEtBlP,YAAAA,IAAI,EAAE,OAFgB;AAGtBsc,YAAAA,OAAO,EAAE;AAHa,WAAxB;AAKA;AACD;;AAED,YAAIkH,OAAO,CAACpI,mBAAR,CAA4Bxc,WAA5B,CAAJ,EAA8C;AAC5ChF,UAAAA,KAAK,CAAC2mB,cAAN;AACAhiB,UAAAA,gBAAU,CAACmlB,IAAX,CAAgBxuB,MAAhB,EAAwB;AAAEga,YAAAA,IAAI,EAAE,MAAR;AAAgBlP,YAAAA,IAAI,EAAE;AAAtB,WAAxB;AACA;AACD,SAtFD;AAyFA;AACA;AACA;AACA;;;AACA,YAAIwjB,OAAO,CAAC/I,cAAR,CAAuB7b,WAAvB,CAAJ,EAAyC;AACvChF,UAAAA,KAAK,CAAC2mB,cAAN;;AAEA,cAAIriB,SAAS,IAAImG,WAAK,CAACG,WAAN,CAAkBtG,SAAlB,CAAjB,EAA+C;AAC7CK,YAAAA,gBAAU,CAACmlB,IAAX,CAAgBxuB,MAAhB,EAAwB;AAAEonB,cAAAA,OAAO,EAAE,CAACiH;AAAZ,aAAxB;AACD,WAFD,MAEO;AACLhlB,YAAAA,gBAAU,CAAColB,QAAX,CAAoBzuB,MAApB,EAA4B;AAAE8K,cAAAA,IAAI,EAAE;AAAR,aAA5B;AACD;;AAED;AACD;;AAED,YAAIwjB,OAAO,CAAC9I,aAAR,CAAsB9b,WAAtB,CAAJ,EAAwC;AACtChF,UAAAA,KAAK,CAAC2mB,cAAN;;AAEA,cAAIriB,SAAS,IAAImG,WAAK,CAACG,WAAN,CAAkBtG,SAAlB,CAAjB,EAA+C;AAC7CK,YAAAA,gBAAU,CAACmlB,IAAX,CAAgBxuB,MAAhB,EAAwB;AAAEonB,cAAAA,OAAO,EAAEiH;AAAX,aAAxB;AACD,WAFD,MAEO;AACLhlB,YAAAA,gBAAU,CAAColB,QAAX,CAAoBzuB,MAApB,EAA4B;AAAE8K,cAAAA,IAAI,EAAE;AAAR,aAA5B;AACD;;AAED;AACD;;AAED,YAAIwjB,OAAO,CAAChI,kBAAR,CAA2B5c,WAA3B,CAAJ,EAA6C;AAC3ChF,UAAAA,KAAK,CAAC2mB,cAAN;;AAEA,cAAIriB,SAAS,IAAImG,WAAK,CAACoD,UAAN,CAAiBvJ,SAAjB,CAAjB,EAA8C;AAC5CK,YAAAA,gBAAU,CAAColB,QAAX,CAAoBzuB,MAApB,EAA4B;AAAE8K,cAAAA,IAAI,EAAE;AAAR,aAA5B;AACD;;AAEDzB,UAAAA,gBAAU,CAACmlB,IAAX,CAAgBxuB,MAAhB,EAAwB;AAAEga,YAAAA,IAAI,EAAE,MAAR;AAAgBoN,YAAAA,OAAO,EAAE,CAACiH;AAA1B,WAAxB;AACA;AACD;;AAED,YAAIC,OAAO,CAAC/H,iBAAR,CAA0B7c,WAA1B,CAAJ,EAA4C;AAC1ChF,UAAAA,KAAK,CAAC2mB,cAAN;;AAEA,cAAIriB,SAAS,IAAImG,WAAK,CAACoD,UAAN,CAAiBvJ,SAAjB,CAAjB,EAA8C;AAC5CK,YAAAA,gBAAU,CAAColB,QAAX,CAAoBzuB,MAApB,EAA4B;AAAE8K,cAAAA,IAAI,EAAE;AAAR,aAA5B;AACD;;AAEDzB,UAAAA,gBAAU,CAACmlB,IAAX,CAAgBxuB,MAAhB,EAAwB;AAAEga,YAAAA,IAAI,EAAE,MAAR;AAAgBoN,YAAAA,OAAO,EAAEiH;AAAzB,WAAxB;AACA;AACD,SAzID;AA4IA;AACA;;;AACA,YAAI,CAACzsB,wBAAL,EAA+B;AAC7B;AACA;AACA,cACE0sB,OAAO,CAACjJ,MAAR,CAAe3b,WAAf,KACA4kB,OAAO,CAACnI,QAAR,CAAiBzc,WAAjB,CADA,IAEA4kB,OAAO,CAAC3H,oBAAR,CAA6Bjd,WAA7B,CAHF,EAIE;AACAhF,YAAAA,KAAK,CAAC2mB,cAAN;AACA;AACD;;AAED,cAAIiD,OAAO,CAAC7H,WAAR,CAAoB/c,WAApB,CAAJ,EAAsC;AACpChF,YAAAA,KAAK,CAAC2mB,cAAN;AACAjhB,YAAAA,YAAM,CAAC6P,eAAP,CAAuBja,MAAvB;AACA;AACD;;AAED,cAAIsuB,OAAO,CAAC5H,YAAR,CAAqBhd,WAArB,CAAJ,EAAuC;AACrChF,YAAAA,KAAK,CAAC2mB,cAAN;AACAjhB,YAAAA,YAAM,CAAC8P,WAAP,CAAmBla,MAAnB;AACA;AACD;;AAED,cAAIsuB,OAAO,CAAC7I,gBAAR,CAAyB/b,WAAzB,CAAJ,EAA2C;AACzChF,YAAAA,KAAK,CAAC2mB,cAAN;;AAEA,gBAAIriB,SAAS,IAAImG,WAAK,CAACoD,UAAN,CAAiBvJ,SAAjB,CAAjB,EAA8C;AAC5CoB,cAAAA,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB,EAA8B;AAAE8F,gBAAAA,SAAS,EAAE;AAAb,eAA9B;AACD,aAFD,MAEO;AACLsE,cAAAA,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB;AACD;;AAED;AACD;;AAED,cAAIsuB,OAAO,CAAC5I,eAAR,CAAwBhc,WAAxB,CAAJ,EAA0C;AACxChF,YAAAA,KAAK,CAAC2mB,cAAN;;AAEA,gBAAIriB,SAAS,IAAImG,WAAK,CAACoD,UAAN,CAAiBvJ,SAAjB,CAAjB,EAA8C;AAC5CoB,cAAAA,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB,EAA8B;AAAE8F,gBAAAA,SAAS,EAAE;AAAb,eAA9B;AACD,aAFD,MAEO;AACLsE,cAAAA,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB;AACD;;AAED;AACD;;AAED,cAAIsuB,OAAO,CAAC3I,oBAAR,CAA6Bjc,WAA7B,CAAJ,EAA+C;AAC7ChF,YAAAA,KAAK,CAAC2mB,cAAN;;AAEA,gBAAIriB,SAAS,IAAImG,WAAK,CAACoD,UAAN,CAAiBvJ,SAAjB,CAAjB,EAA8C;AAC5CoB,cAAAA,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB,EAA8B;AAAE8F,gBAAAA,SAAS,EAAE;AAAb,eAA9B;AACD,aAFD,MAEO;AACLsE,cAAAA,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,EAA8B;AAAEga,gBAAAA,IAAI,EAAE;AAAR,eAA9B;AACD;;AAED;AACD;;AAED,cAAIsU,OAAO,CAAC1I,mBAAR,CAA4Blc,WAA5B,CAAJ,EAA8C;AAC5ChF,YAAAA,KAAK,CAAC2mB,cAAN;;AAEA,gBAAIriB,SAAS,IAAImG,WAAK,CAACoD,UAAN,CAAiBvJ,SAAjB,CAAjB,EAA8C;AAC5CoB,cAAAA,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB,EAA8B;AAAE8F,gBAAAA,SAAS,EAAE;AAAb,eAA9B;AACD,aAFD,MAEO;AACLsE,cAAAA,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB,EAA6B;AAAEga,gBAAAA,IAAI,EAAE;AAAR,eAA7B;AACD;;AAED;AACD;;AAED,cAAIsU,OAAO,CAACzI,oBAAR,CAA6Bnc,WAA7B,CAAJ,EAA+C;AAC7ChF,YAAAA,KAAK,CAAC2mB,cAAN;;AAEA,gBAAIriB,SAAS,IAAImG,WAAK,CAACoD,UAAN,CAAiBvJ,SAAjB,CAAjB,EAA8C;AAC5CoB,cAAAA,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB,EAA8B;AAAE8F,gBAAAA,SAAS,EAAE;AAAb,eAA9B;AACD,aAFD,MAEO;AACLsE,cAAAA,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,EAA8B;AAAEga,gBAAAA,IAAI,EAAE;AAAR,eAA9B;AACD;;AAED;AACD;;AAED,cAAIsU,OAAO,CAACxI,mBAAR,CAA4Bpc,WAA5B,CAAJ,EAA8C;AAC5ChF,YAAAA,KAAK,CAAC2mB,cAAN;;AAEA,gBAAIriB,SAAS,IAAImG,WAAK,CAACoD,UAAN,CAAiBvJ,SAAjB,CAAjB,EAA8C;AAC5CoB,cAAAA,YAAM,CAAC2N,cAAP,CAAsB/X,MAAtB,EAA8B;AAAE8F,gBAAAA,SAAS,EAAE;AAAb,eAA9B;AACD,aAFD,MAEO;AACLsE,cAAAA,YAAM,CAACyP,aAAP,CAAqB7Z,MAArB,EAA6B;AAAEga,gBAAAA,IAAI,EAAE;AAAR,eAA7B;AACD;;AAED;AACD;AACF,SA/FD,MA+FO;AACL,cAAI7Y,SAAS,IAAIF,SAAjB,EAA4B;AAC1B;AACA;AACA,gBACE+H,SAAS,KACRslB,OAAO,CAAC7I,gBAAR,CAAyB/b,WAAzB,KACC4kB,OAAO,CAAC5I,eAAR,CAAwBhc,WAAxB,CAFO,CAAT,IAGAyF,WAAK,CAACG,WAAN,CAAkBtG,SAAlB,CAJF,EAKE;AACA,kBAAM0lB,WAAW,GAAGtqB,UAAI,CAACuB,MAAL,CAClB3F,MADkB,EAElBgJ,SAAS,CAACiE,MAAV,CAAiBjD,IAFC,CAApB;;AAKA,kBACEE,aAAO,CAACC,SAAR,CAAkBukB,WAAlB,KACAtkB,YAAM,CAACC,MAAP,CAAcrK,MAAd,EAAsB0uB,WAAtB,CADA,KAECtkB,YAAM,CAACK,QAAP,CAAgBzK,MAAhB,EAAwB0uB,WAAxB,KACCtkB,YAAM,CAACyK,OAAP,CAAe7U,MAAf,EAAuB0uB,WAAvB,CAHF,CADF,EAKE;AACAhqB,gBAAAA,KAAK,CAAC2mB,cAAN;AACAjhB,gBAAAA,YAAM,CAAC2P,cAAP,CAAsB/Z,MAAtB,EAA8B;AAAEga,kBAAAA,IAAI,EAAE;AAAR,iBAA9B;AAEA;AACD;AACF;AACF;AACF;AACF;AACF,KAhRmB,EAiRpB,CAACgH,QAAD,EAAWhhB,MAAX,EAAmBue,UAAU,CAAC6P,SAA9B,CAjRoB;AAmRtBO,IAAAA,OAAO,EAAErP,iBAAW,CAClB,UAAC5a,KAAD;AACE,UACE,CAACsc,QAAD,IACAvZ,WAAW,CAACsF,iBAAZ,CAA8B/M,MAA9B,EAAsC0E,KAAK,CAAC6C,MAA5C,CADA,IAEA,CAACklB,cAAc,CAAC/nB,KAAD,EAAQ6Z,UAAU,CAACoQ,OAAnB,CAHjB,EAIE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YACE,CAAC/sB,wBAAD,IACA6C,oBAAoB,CAACC,KAAK,CAACgF,WAAP,CADpB,IAEAzI,SAHF,EAIE;AACAyD,UAAAA,KAAK,CAAC2mB,cAAN;AACA5jB,UAAAA,WAAW,CAAC6F,UAAZ,CAAuBtN,MAAvB,EAA+B0E,KAAK,CAACC,aAArC;AACD;AACF;AACF,KAvBiB,EAwBlB,CAACqc,QAAD,EAAWhhB,MAAX,EAAmBue,UAAU,CAACoQ,OAA9B,CAxBkB;IAtxBtB,eAizBEtuB,uCAAA,CAACwnB,QAAD;AACErH,IAAAA,WAAW,EAAEA;AACbvb,IAAAA,IAAI,EAAEjF;AACN6gB,IAAAA,aAAa,EAAEA;AACf/B,IAAAA,iBAAiB,EAAEA;AACnBC,IAAAA,UAAU,EAAEA;AACZ/V,IAAAA,SAAS,EAAEhJ,MAAM,CAACgJ;GANpB,CAjzBF,CADF,CADF,CADF,CADF;AAk0BD;AAgBD;;;;IAIagf,kBAAkB,GAAG,SAArBA,kBAAqB;AAAA,MAChCzJ,UADgC,QAChCA,UADgC;AAAA,MAEhCzM,QAFgC,QAEhCA,QAFgC;AAAA;AAAA;AAIhC;AACA;AACAzR,IAAAA,uCAAA,OAAA,oBAAUke,WAAV,EACGzM,QADH,EAEG/Q,UAAU,iBAAIV,uCAAA,KAAA,MAAA,CAFjB;AANgC;AAAA;AAYlC;;;;AAIO,IAAM6nB,eAAe,GAAkC,SAAjDA,eAAiD;AAAA,SAAM,EAAN;AAAA,CAAvD;AAEP;;;;AAIA,IAAMI,8BAA8B,GAAG,SAAjCA,8BAAiC,CACrCtoB,MADqC,EAErCmL,QAFqC;AAIrC;AACA;AACA,MACEA,QAAQ,CAACZ,qBAAT,KACC,CAACvK,MAAM,CAACgJ,SAAR,IACEhJ,MAAM,CAACgJ,SAAP,IAAoBmG,WAAK,CAACG,WAAN,CAAkBtP,MAAM,CAACgJ,SAAzB,CAFvB,CADF,EAIE;AACA,QAAM4lB,MAAM,GAAGzjB,QAAQ,CAACgG,cAAT,CAAwB1E,aAAvC;AACAmiB,IAAAA,MAAM,CAACrkB,qBAAP,GAA+BY,QAAQ,CAACZ,qBAAT,CAA+BskB,IAA/B,CAAoC1jB,QAApC,CAA/B;AACA2jB,IAAAA,kCAAc,CAACF,MAAD,EAAS;AACrBG,MAAAA,UAAU,EAAE;AADS,KAAT,CAAd,CAHA;;AAQA,WAAOH,MAAM,CAACrkB,qBAAd;AACD;AACF,CApBD;AAsBA;;;;;AAIO,IAAMkiB,cAAc,GAAG,SAAjBA,cAAiB,CAG5B/nB,KAH4B,EAI5BsqB,OAJ4B;AAM5B,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,KAAP;AACD;AAED;;;AACA,MAAMC,yBAAyB,GAAGD,OAAO,CAACtqB,KAAD,CAAzC;;AAEA,MAAIuqB,yBAAyB,IAAI,IAAjC,EAAuC;AACrC,WAAOA,yBAAP;AACD;;AAED,SAAOvqB,KAAK,CAACwqB,kBAAN,MAA8BxqB,KAAK,CAACyqB,oBAAN,EAArC;AACD,CAlBM;AAoBP;;;;AAGO,IAAMxB,qBAAqB,GAAG,SAAxBA,qBAAwB,CAGnCjpB,KAHmC;AAKnC,SACET,SAAS,CAACS,KAAK,CAAC6C,MAAP,CAAT,KACC7C,KAAK,CAAC6C,MAAN,YAAwB6nB,gBAAxB,IACC1qB,KAAK,CAAC6C,MAAN,YAAwB8nB,mBAF1B,CADF;AAKD,CAVM;AAYP;;;;AAIO,IAAM1E,iBAAiB,GAAG,SAApBA,iBAAoB,CAC/BjmB,KAD+B,EAE/BsqB,OAF+B;AAI/B,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,KAAP;AACD;AAGD;;;AACA,MAAMC,yBAAyB,GAAGD,OAAO,CAACtqB,KAAD,CAAzC;;AAEA,MAAIuqB,yBAAyB,IAAI,IAAjC,EAAuC;AACrC,WAAOA,yBAAP;AACD;;AAED,SAAOvqB,KAAK,CAAC4qB,gBAAb;AACD,CAjBM;;AC3xDP;;;;AAIO,IAAMC,cAAc,gBAAGzvB,mBAAa,CAAC,KAAD,CAApC;AAEP;;;;IAIa0vB,UAAU,GAAG,SAAbA,UAAa;AACxB,SAAOvvB,gBAAU,CAACsvB,cAAD,CAAjB;AACD;;ACHD,SAASE,OAAT,CAAiBC,KAAjB;AACE,SAAOA,KAAK,YAAYxvB,KAAxB;AACD;AAGD;;;;;AAIO,IAAMyvB,oBAAoB,gBAAG7vB,mBAAa,CAG9C,EAH8C,CAA1C;;AAKP,IAAM8vB,WAAW,GAAG,SAAdA,WAAc,CAACzb,CAAD,EAASC,CAAT;AAAA,SAAoBD,CAAC,KAAKC,CAA1B;AAAA,CAApB;AAEA;;;;;;;;;;;SASgByb,iBACd1hB;MACA2hB,iFAAsCF;;AAEtC,oBAAwB9G,gBAAU,CAAC,UAAAC,CAAC;AAAA,WAAIA,CAAC,GAAG,CAAR;AAAA,GAAF,EAAa,CAAb,CAAlC;AAAA;AAAA,MAASC,WAAT;;AACA,MAAMpG,OAAO,GAAG3iB,gBAAU,CAAC0vB,oBAAD,CAA1B;;AACA,MAAI,CAAC/M,OAAL,EAAc;AACZ,UAAM,IAAI1iB,KAAJ,oFAAN;AAGD;;AACD,MAAQ6vB,QAAR,GAAuCnN,OAAvC,CAAQmN,QAAR;AAAA,MAAkBvE,gBAAlB,GAAuC5I,OAAvC,CAAkB4I,gBAAlB;AAEA,MAAMwE,+BAA+B,GAAG1U,YAAM,EAA9C;AACA,MAAM2U,cAAc,GAAG3U,YAAM,CAAwB;AAAA,WAAM,IAAN;AAAA,GAAxB,CAA7B;AACA,MAAM4U,mBAAmB,GAAG5U,YAAM,CAAK,IAAL,CAAlC;AACA,MAAI6U,aAAJ;;AAEA,MAAI;AACF,QACEhiB,QAAQ,KAAK8hB,cAAc,CAACjf,OAA5B,IACAgf,+BAA+B,CAAChf,OAFlC,EAGE;AACAmf,MAAAA,aAAa,GAAGhiB,QAAQ,CAAC4hB,QAAQ,EAAT,CAAxB;AACD,KALD,MAKO;AACLI,MAAAA,aAAa,GAAGD,mBAAmB,CAAClf,OAApC;AACD;AACF,GATD,CASE,OAAOtE,GAAP,EAAY;AACZ,QAAIsjB,+BAA+B,CAAChf,OAAhC,IAA2Cye,OAAO,CAAC/iB,GAAD,CAAtD,EAA6D;AAC3DA,MAAAA,GAAG,CAACC,OAAJ,uEAA2EqjB,+BAA+B,CAAChf,OAAhC,CAAwCof,KAAnH;AACD;;AAED,UAAM1jB,GAAN;AACD;;AACD8O,EAAAA,yBAAyB,CAAC;AACxByU,IAAAA,cAAc,CAACjf,OAAf,GAAyB7C,QAAzB;AACA+hB,IAAAA,mBAAmB,CAAClf,OAApB,GAA8Bmf,aAA9B;AACAH,IAAAA,+BAA+B,CAAChf,OAAhC,GAA0C6G,SAA1C;AACD,GAJwB,CAAzB;AAMA2D,EAAAA,yBAAyB,CACvB;AACE,aAAS6U,eAAT;AACE,UAAI;AACF,YAAMC,gBAAgB,GAAGL,cAAc,CAACjf,OAAf,CAAuB+e,QAAQ,EAA/B,CAAzB;;AAEA,YAAID,UAAU,CAACQ,gBAAD,EAAmBJ,mBAAmB,CAAClf,OAAvC,CAAd,EAA+D;AAC7D;AACD;;AAEDkf,QAAAA,mBAAmB,CAAClf,OAApB,GAA8Bsf,gBAA9B;AACD,OARD,CAQE,OAAO5jB,GAAP,EAAY;AACZ;AACA;AACA;AACA;AACAsjB,QAAAA,+BAA+B,CAAChf,OAAhC,GAA0CtE,GAA1C;AACD;;AAEDsc,MAAAA,WAAW;AACZ;;AAED,QAAMuH,WAAW,GAAG/E,gBAAgB,CAAC6E,eAAD,CAApC;AAEAA,IAAAA,eAAe;AAEf,WAAO;AAAA,aAAME,WAAW,EAAjB;AAAA,KAAP;AACD,GA3BsB;AA6BvB,GAAC/E,gBAAD,EAAmBuE,QAAnB,CA7BuB,CAAzB;AAgCA,SAAOI,aAAP;AACD;AAED;;;;SAGgBK,mBAAmBxwB;AACjC,MAAMywB,cAAc,GAAGnV,YAAM,CAAwB,EAAxB,CAAN,CAAkCtK,OAAzD;AACA,MAAM0f,QAAQ,GAAGpV,YAAM,CAEpB;AACDtb,IAAAA,MAAM,EAANA;AADC,GAFoB,CAAN,CAIdgR,OAJH;AAKA,MAAMoH,QAAQ,GAAGkH,iBAAW,CAC1B,UAACtf,MAAD;AACE0wB,IAAAA,QAAQ,CAAC1wB,MAAT,GAAkBA,MAAlB;AACAywB,IAAAA,cAAc,CAAC7f,OAAf,CAAuB,UAAC+f,QAAD;AAAA,aACrBA,QAAQ,CAAC3wB,MAAD,CADa;AAAA,KAAvB;AAGD,GANyB,EAO1B,CAACywB,cAAD,EAAiBC,QAAjB,CAP0B,CAA5B;AAUA,MAAME,eAAe,GAAG1H,aAAO,CAAC;AAC9B,WAAO;AACL6G,MAAAA,QAAQ,EAAE;AAAA,eAAMW,QAAQ,CAAC1wB,MAAf;AAAA,OADL;AAELwrB,MAAAA,gBAAgB,EAAE,0BAAC7P,QAAD;AAChB8U,QAAAA,cAAc,CAACxX,IAAf,CAAoB0C,QAApB;AACA,eAAO;AACL8U,UAAAA,cAAc,CAACtX,MAAf,CAAsBsX,cAAc,CAACI,OAAf,CAAuBlV,QAAvB,CAAtB,EAAwD,CAAxD;AACD,SAFD;AAGD;AAPI,KAAP;AASD,GAV8B,EAU5B,CAAC8U,cAAD,EAAiBC,QAAjB,CAV4B,CAA/B;AAWA,SAAO;AAAEE,IAAAA,eAAe,EAAfA,eAAF;AAAmBxY,IAAAA,QAAQ,EAARA;AAAnB,GAAP;AACD;;;ACjID;;;;;IAKa0Y,KAAK,GAAG,SAARA,KAAQ,CAACtT,KAAD;AAMnB,MAAQxd,MAAR,GAA8Dwd,KAA9D,CAAQxd,MAAR;AAAA,MAAgB8R,QAAhB,GAA8D0L,KAA9D,CAAgB1L,QAAhB;AAAA,MAA0BsG,QAA1B,GAA8DoF,KAA9D,CAA0BpF,QAA1B;AAAA,MAAoC2Y,YAApC,GAA8DvT,KAA9D,CAAoCuT,YAApC;AAAA,MAAqDrF,IAArD,4BAA8DlO,KAA9D;;AAEA,wBAA8Bnd,yBAAK,CAACub,QAAN,CAAkC;AAC9D,QAAI,CAACxX,UAAI,CAAC4sB,UAAL,CAAgBD,YAAhB,CAAL,EAAoC;AAClC,YAAM,IAAI7wB,KAAJ,iFACqEgM,cAAQ,CAACC,SAAT,CACvE4kB,YADuE,CADrE,EAAN;AAKD;;AACD,QAAI,CAAC3mB,YAAM,CAAC4B,QAAP,CAAgBhM,MAAhB,CAAL,EAA8B;AAC5B,YAAM,IAAIE,KAAJ,kDACsCgM,cAAQ,CAACC,SAAT,CAAmBnM,MAAnB,CADtC,EAAN;AAGD;;AACDA,IAAAA,MAAM,CAAC8R,QAAP,GAAkBif,YAAlB;AACAnU,IAAAA,MAAM,CAACqU,MAAP,CAAcjxB,MAAd,EAAsB0rB,IAAtB;AACA,WAAO;AAAEwF,MAAAA,CAAC,EAAE,CAAL;AAAQlxB,MAAAA,MAAM,EAANA;AAAR,KAAP;AACD,GAhB6B,CAA9B;AAAA;AAAA,MAAO4iB,OAAP;AAAA,MAAgBuO,UAAhB;;AAkBA,4BAGIX,kBAAkB,CAACxwB,MAAD,CAHtB;AAAA,MACE4wB,eADF,uBACEA,eADF;AAAA,MAEYQ,oBAFZ,uBAEEhZ,QAFF;;AAKA,MAAMiZ,eAAe,GAAG/R,iBAAW,CAAC;AAClC,QAAIlH,QAAJ,EAAc;AACZA,MAAAA,QAAQ,CAACpY,MAAM,CAAC8R,QAAR,CAAR;AACD;;AAEDqf,IAAAA,UAAU,CAAC,UAAAG,WAAW;AAAA,aAAK;AACzBJ,QAAAA,CAAC,EAAEI,WAAW,CAACJ,CAAZ,GAAgB,CADM;AAEzBlxB,QAAAA,MAAM,EAANA;AAFyB,OAAL;AAAA,KAAZ,CAAV;AAIAoxB,IAAAA,oBAAoB,CAACpxB,MAAD,CAApB;AACD,GAVkC,EAUhC,CAACA,MAAD,EAASoxB,oBAAT,EAA+BhZ,QAA/B,CAVgC,CAAnC;AAYAmD,EAAAA,eAAS,CAAC;AACRxY,IAAAA,mBAAmB,CAAC8F,GAApB,CAAwB7I,MAAxB,EAAgCqxB,eAAhC;AAEA,WAAO;AACLtuB,MAAAA,mBAAmB,CAAC8F,GAApB,CAAwB7I,MAAxB,EAAgC,cAAhC;AACD,KAFD;AAGD,GANQ,EAMN,CAACA,MAAD,EAASqxB,eAAT,CANM,CAAT;;AAQA,kBAAkCzV,cAAQ,CAACnU,WAAW,CAACkG,SAAZ,CAAsB3N,MAAtB,CAAD,CAA1C;AAAA;AAAA,MAAO2N,SAAP;AAAA,MAAkB4jB,YAAlB;;AAEAhW,EAAAA,eAAS,CAAC;AACRgW,IAAAA,YAAY,CAAC9pB,WAAW,CAACkG,SAAZ,CAAsB3N,MAAtB,CAAD,CAAZ;AACD,GAFQ,EAEN,CAACA,MAAD,CAFM,CAAT;AAIAwb,EAAAA,yBAAyB,CAAC;AACxB,QAAMgW,EAAE,GAAG,SAALA,EAAK;AAAA,aAAMD,YAAY,CAAC9pB,WAAW,CAACkG,SAAZ,CAAsB3N,MAAtB,CAAD,CAAlB;AAAA,KAAX;;AACA,QAAIG,mBAAmB,IAAI,EAA3B,EAA+B;AAC7B;AACA;AACA;AACAuB,MAAAA,QAAQ,CAAC8pB,gBAAT,CAA0B,SAA1B,EAAqCgG,EAArC;AACA9vB,MAAAA,QAAQ,CAAC8pB,gBAAT,CAA0B,UAA1B,EAAsCgG,EAAtC;AACA,aAAO;AACL9vB,QAAAA,QAAQ,CAAC6pB,mBAAT,CAA6B,SAA7B,EAAwCiG,EAAxC;AACA9vB,QAAAA,QAAQ,CAAC6pB,mBAAT,CAA6B,UAA7B,EAAyCiG,EAAzC;AACD,OAHD;AAID,KAVD,MAUO;AACL9vB,MAAAA,QAAQ,CAAC8pB,gBAAT,CAA0B,OAA1B,EAAmCgG,EAAnC,EAAuC,IAAvC;AACA9vB,MAAAA,QAAQ,CAAC8pB,gBAAT,CAA0B,MAA1B,EAAkCgG,EAAlC,EAAsC,IAAtC;AACA,aAAO;AACL9vB,QAAAA,QAAQ,CAAC6pB,mBAAT,CAA6B,OAA7B,EAAsCiG,EAAtC,EAA0C,IAA1C;AACA9vB,QAAAA,QAAQ,CAAC6pB,mBAAT,CAA6B,MAA7B,EAAqCiG,EAArC,EAAyC,IAAzC;AACD,OAHD;AAID;AACF,GApBwB,EAoBtB,EApBsB,CAAzB;AAsBA,sBACEnxB,uCAAA,CAACsvB,oBAAoB,CAACrN,QAAtB;AAA+Bze,IAAAA,KAAK,EAAE+sB;GAAtC,eACEvwB,uCAAA,CAACqiB,YAAY,CAACJ,QAAd;AAAuBze,IAAAA,KAAK,EAAE+e;GAA9B,eACEviB,uCAAA,CAACR,aAAa,CAACyiB,QAAf;AAAwBze,IAAAA,KAAK,EAAE+e,OAAO,CAAC5iB;GAAvC,eACEK,uCAAA,CAACkvB,cAAc,CAACjN,QAAhB;AAAyBze,IAAAA,KAAK,EAAE8J;GAAhC,EACGmE,QADH,CADF,CADF,CADF,CADF;AAWD;;ACzGD;;;;;IAKa2f,SAAS,GAAG,SAAZA,SAAY;AACvB,MAAMzxB,MAAM,GAAGC,gBAAU,CAACJ,aAAD,CAAzB;;AAEA,MAAI,CAACG,MAAL,EAAa;AACX,UAAM,IAAIE,KAAJ,6EAAN;AAGD;;AAED,SAAOF,MAAP;AACD;;ACfD;;;;;IAIa0xB,iBAAiB,GAAG,SAApBA,iBAAoB;AAC/B,SAAO7B,gBAAgB,CAAC,UAAA7vB,MAAM;AAAA,WAAIA,MAAM,CAACgJ,SAAX;AAAA,GAAP,EAA6B2oB,gBAA7B,CAAvB;AACD;;AAED,IAAMA,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACxd,CAAD,EAAmBC,CAAnB;AACvB,MAAI,CAACD,CAAD,IAAM,CAACC,CAAX,EAAc,OAAO,IAAP;AACd,MAAI,CAACD,CAAD,IAAM,CAACC,CAAX,EAAc,OAAO,KAAP;AACd,SAAOjF,WAAK,CAACiG,MAAN,CAAajB,CAAb,EAAgBC,CAAhB,CAAP;AACD,CAJD;;ACZA;;;;AAOA,IAAMwd,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACtnB,IAAD,EAAgBunB,WAAhB;AACvB,MAAMC,MAAM,GAAG,CAACD,WAAW,CAACjnB,GAAZ,GAAkBinB,WAAW,CAACE,MAA/B,IAAyC,CAAxD;AAEA,SAAOznB,IAAI,CAACM,GAAL,IAAYknB,MAAZ,IAAsBxnB,IAAI,CAACynB,MAAL,IAAeD,MAA5C;AACD,CAJD;;AAMA,IAAME,iBAAiB,GAAG,SAApBA,iBAAoB,CACxBhyB,MADwB,EAExBiyB,MAFwB,EAGxBC,MAHwB;AAKxB,MAAMC,KAAK,GAAG1qB,WAAW,CAACwH,UAAZ,CAAuBjP,MAAvB,EAA+BiyB,MAA/B,EAAuC1nB,qBAAvC,EAAd;AACA,MAAM6nB,KAAK,GAAG3qB,WAAW,CAACwH,UAAZ,CAAuBjP,MAAvB,EAA+BkyB,MAA/B,EAAuC3nB,qBAAvC,EAAd;AAEA,SAAOqnB,gBAAgB,CAACO,KAAD,EAAQC,KAAR,CAAhB,IAAkCR,gBAAgB,CAACQ,KAAD,EAAQD,KAAR,CAAzD;AACD,CATD;AAWA;;;;;;;;;;AAQO,IAAME,oBAAoB,GAAG,SAAvBA,oBAAuB,CAClCryB,MADkC,EAElCsyB,WAFkC;AAIlC,MAAMC,mBAAmB,GAAGnoB,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqBmP,WAAK,CAACV,GAAN,CAAU6jB,WAAV,CAArB,CAA5B;AACA,MAAME,SAAS,GAAGjsB,KAAK,CAACC,IAAN,CAAW4D,YAAM,CAACooB,SAAP,CAAiBxyB,MAAjB,EAAyB;AAAEkO,IAAAA,EAAE,EAAEokB;AAAN,GAAzB,CAAX,CAAlB;AAEA,MAAI5nB,IAAI,GAAG,CAAX;AACA,MAAI+nB,KAAK,GAAGD,SAAS,CAAC1tB,MAAtB;AACA,MAAIgtB,MAAM,GAAGhjB,IAAI,CAAC4jB,KAAL,CAAWD,KAAK,GAAG,CAAnB,CAAb;;AAEA,MACET,iBAAiB,CACfhyB,MADe,EAEfoK,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqBwyB,SAAS,CAAC9nB,IAAD,CAA9B,CAFe,EAGf6nB,mBAHe,CADnB,EAME;AACA,WAAOnoB,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqBwyB,SAAS,CAAC9nB,IAAD,CAA9B,EAAsC6nB,mBAAtC,CAAP;AACD;;AAED,MAAIC,SAAS,CAAC1tB,MAAV,GAAmB,CAAvB,EAA0B;AACxB,WAAOsF,YAAM,CAACc,KAAP,CACLlL,MADK,EAELwyB,SAAS,CAACA,SAAS,CAAC1tB,MAAV,GAAmB,CAApB,CAFJ,EAGLytB,mBAHK,CAAP;AAKD;;AAED,SAAOT,MAAM,KAAKU,SAAS,CAAC1tB,MAArB,IAA+BgtB,MAAM,KAAKpnB,IAAjD,EAAuD;AACrD,QACEsnB,iBAAiB,CACfhyB,MADe,EAEfoK,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqBwyB,SAAS,CAACV,MAAD,CAA9B,CAFe,EAGfS,mBAHe,CADnB,EAME;AACAE,MAAAA,KAAK,GAAGX,MAAR;AACD,KARD,MAQO;AACLpnB,MAAAA,IAAI,GAAGonB,MAAP;AACD;;AAEDA,IAAAA,MAAM,GAAGhjB,IAAI,CAAC4jB,KAAL,CAAW,CAAChoB,IAAI,GAAG+nB,KAAR,IAAiB,CAA5B,CAAT;AACD;;AAED,SAAOroB,YAAM,CAACc,KAAP,CAAalL,MAAb,EAAqBwyB,SAAS,CAACC,KAAD,CAA9B,EAAuCF,mBAAvC,CAAP;AACD,CA9CM;;;;;;;;;;;ACQP;;;;;;;;;IASaI,SAAS,GAAG,SAAZA,SAAY,CACvB3yB,MADuB;MAEvB4yB,yFAAqB;AAErB,MAAMlI,CAAC,GAAG1qB,MAAV;AACA,MAAQ6yB,KAAR,GAAiEnI,CAAjE,CAAQmI,KAAR;AAAA,MAAeza,QAAf,GAAiEsS,CAAjE,CAAetS,QAAf;AAAA,MAAyB2B,cAAzB,GAAiE2Q,CAAjE,CAAyB3Q,cAAzB;AAAA,MAAyC+Y,OAAzC,GAAiEpI,CAAjE,CAAyCoI,OAAzC;AAAA,MAAkDC,UAAlD,GAAiErI,CAAjE,CAAkDqI,UAAlD;AAGA;;AACArwB,EAAAA,wBAAwB,CAACmG,GAAzB,CAA6B6hB,CAA7B,EAAgC,IAAIxoB,OAAJ,EAAhC;;AAEAwoB,EAAAA,CAAC,CAACoI,OAAF,GAAY,UAAC/mB,GAAD,EAAMlI,KAAN;;;AACV,6BAAAb,wBAAwB,CAACsF,GAAzB,CAA6BoiB,CAA7B;;AAEA,QACE,CAACznB,iCAAiC,CAACqF,GAAlC,CAAsCoiB,CAAtC,CAAD,6BACAvnB,uBAAuB,CAACmF,GAAxB,CAA4BoiB,CAA5B,CADA,kDACA,sBAAgC5lB,MAFlC,EAGE;AACA;AACA;AACA7B,MAAAA,iCAAiC,CAAC4F,GAAlC,CAAsC6hB,CAAtC,EAAyC,IAAzC;AACD;;AAEDxnB,IAAAA,oBAAoB,UAApB,CAA4BwnB,CAA5B;AAEAoI,IAAAA,OAAO,CAAC/mB,GAAD,EAAMlI,KAAN,CAAP;AACD,GAfD;;AAiBA6mB,EAAAA,CAAC,CAACqI,UAAF,GAAe,UAAAhnB,GAAG;;;AAChB,QACE,CAAC9I,iCAAiC,CAACqF,GAAlC,CAAsCoiB,CAAtC,CAAD,8BACAvnB,uBAAuB,CAACmF,GAAxB,CAA4BoiB,CAA5B,CADA,mDACA,uBAAgC5lB,MAFlC,EAGE;AACA;AACA;AACA7B,MAAAA,iCAAiC,CAAC4F,GAAlC,CAAsC6hB,CAAtC,EAAyC,IAAzC;AACD;;AAEDxnB,IAAAA,oBAAoB,UAApB,CAA4BwnB,CAA5B;AAEAqI,IAAAA,UAAU,CAAChnB,GAAD,CAAV;AACD,GAbD;;AAeA2e,EAAAA,CAAC,CAAC3Q,cAAF,GAAmB,UAAAC,IAAI;AACrB,QAAIA,IAAI,KAAK,MAAb,EAAqB;AACnB,aAAOD,cAAc,CAACC,IAAD,CAArB;AACD;;AAED,QAAI0Q,CAAC,CAAC1hB,SAAF,IAAemG,WAAK,CAACG,WAAN,CAAkBob,CAAC,CAAC1hB,SAApB,CAAnB,EAAmD;AACjD,UAAMgqB,gBAAgB,GAAG5oB,YAAM,CAACwK,KAAP,CAAa8V,CAAb,EAAgB;AACvCxjB,QAAAA,KAAK,EAAE,eAAAgB,CAAC;AAAA,iBAAIgC,aAAO,CAACC,SAAR,CAAkBjC,CAAlB,KAAwBkC,YAAM,CAACyK,OAAP,CAAe6V,CAAf,EAAkBxiB,CAAlB,CAA5B;AAAA,SAD+B;AAEvCgG,QAAAA,EAAE,EAAEwc,CAAC,CAAC1hB;AAFiC,OAAhB,CAAzB;;AAKA,UAAIgqB,gBAAJ,EAAsB;AACpB,+CAA4BA,gBAA5B;AAAA,YAASC,eAAT;;AACA,YAAMC,kBAAkB,GAAG9oB,YAAM,CAACc,KAAP,CACzBwf,CADyB,EAEzBuI,eAFyB,EAGzBvI,CAAC,CAAC1hB,SAAF,CAAYiE,MAHa,CAA3B;AAMA,YAAMkmB,gBAAgB,GAAGd,oBAAoB,CAAC3H,CAAD,EAAIwI,kBAAJ,CAA7C;;AAEA,YAAI,CAAC/jB,WAAK,CAACG,WAAN,CAAkB6jB,gBAAlB,CAAL,EAA0C;AACxC9pB,UAAAA,gBAAU,UAAV,CAAkBqhB,CAAlB,EAAqB;AAAExc,YAAAA,EAAE,EAAEilB;AAAN,WAArB;AACD;AACF;AACF;AACF,GA1BD;AA6BA;;;AACAzI,EAAAA,CAAC,CAACmI,KAAF,GAAU,UAAC3d,EAAD;AACR,QAAM1N,OAAO,GAAkB,EAA/B;AAEA,QAAM2N,YAAY,GAAGhS,uBAAuB,CAACmF,GAAxB,CAA4BoiB,CAA5B,CAArB;;AACA,QAAIvV,YAAJ,aAAIA,YAAJ,eAAIA,YAAY,CAAErQ,MAAlB,EAA0B;AACxB,UAAM0Q,WAAW,GAAGL,YAAY,CAC7B2W,GADiB,CACb,UAAAjZ,QAAQ;AAAA,eAAI8C,iBAAiB,CAAC9C,QAAD,EAAWqC,EAAX,CAArB;AAAA,OADK,EAEjB8C,MAFiB,CAEVob,OAFU,CAApB;AAIAjwB,MAAAA,uBAAuB,CAAC0F,GAAxB,CAA4B6hB,CAA5B,EAA+BlV,WAA/B;AACD;;AAED,QAAMqB,gBAAgB,GAAGxT,2BAA2B,CAACiF,GAA5B,CAAgCoiB,CAAhC,CAAzB;;AACA,QAAI7T,gBAAJ,EAAsB;AACpBxT,MAAAA,2BAA2B,CAACwF,GAA5B,CACE6hB,CADF,EAEEhV,qBAAqB,CAACgV,CAAD,EAAI7T,gBAAJ,EAAsB3B,EAAtB,CAFvB;AAID;;AAED,QAAMme,aAAa,GAAGjwB,wBAAwB,CAACkF,GAAzB,CAA6BoiB,CAA7B,CAAtB;;AACA,QAAI2I,aAAJ,aAAIA,aAAJ,eAAIA,aAAa,CAAEnlB,EAAnB,EAAuB;AACrB,UAAMA,EAAE,GAAGmH,WAAK,CAAC4B,OAAN,CAAcoc,aAAd,aAAcA,aAAd,uBAAcA,aAAa,CAAEnlB,EAA7B,IACP+G,qBAAqB,CAACyV,CAAD,EAAI2I,aAAa,CAACnlB,EAAlB,EAAsBgH,EAAtB,CADd,GAEPQ,qBAAqB,CAACgV,CAAD,EAAI2I,aAAa,CAACnlB,EAAlB,EAAsBgH,EAAtB,CAFzB;AAIA9R,MAAAA,wBAAwB,CAACyF,GAAzB,CAA6B6hB,CAA7B,EAAgCxc,EAAE,mCAAQmlB,aAAR;AAAuBnlB,QAAAA,EAAE,EAAFA;AAAvB,WAA8B,IAAhE;AACD;;AAED,YAAQgH,EAAE,CAACO,IAAX;AACE,WAAK,aAAL;AACA,WAAK,aAAL;AACA,WAAK,UAAL;AACA,WAAK,YAAL;AAAmB;AACjBjO,UAAAA,OAAO,CAACyR,IAAR,OAAAzR,OAAO,qBAAS8rB,UAAU,CAAC5I,CAAD,EAAIxV,EAAE,CAAClL,IAAP,CAAnB,EAAP;AACA;AACD;;AAED,WAAK,eAAL;AAAsB;AAAA;;AACpB;AACA,mCAAAlH,wBAAwB,CAACwF,GAAzB,CAA6BoiB,CAA7B,iFAAiCxS,KAAjC;AACApV,UAAAA,wBAAwB,UAAxB,CAAgC4nB,CAAhC;AACA;AACD;;AAED,WAAK,aAAL;AACA,WAAK,aAAL;AAAoB;AAClBljB,UAAAA,OAAO,CAACyR,IAAR,OAAAzR,OAAO,qBAAS8rB,UAAU,CAAC5I,CAAD,EAAIzX,UAAI,CAACtN,MAAL,CAAYuP,EAAE,CAAClL,IAAf,CAAJ,CAAnB,EAAP;AACA;AACD;;AAED,WAAK,YAAL;AAAmB;AACjB,cAAMupB,QAAQ,GAAGtgB,UAAI,CAACugB,QAAL,CAActe,EAAE,CAAClL,IAAjB,CAAjB;AACAxC,UAAAA,OAAO,CAACyR,IAAR,OAAAzR,OAAO,qBAAS8rB,UAAU,CAAC5I,CAAD,EAAI6I,QAAJ,CAAnB,EAAP;AACA;AACD;;AAED,WAAK,WAAL;AAAkB;AAChB,cAAME,UAAU,GAAGxgB,UAAI,CAACygB,MAAL,CACjBzgB,UAAI,CAACtN,MAAL,CAAYuP,EAAE,CAAClL,IAAf,CADiB,EAEjBiJ,UAAI,CAACtN,MAAL,CAAYuP,EAAE,CAACU,OAAf,CAFiB,CAAnB;AAIApO,UAAAA,OAAO,CAACyR,IAAR,OAAAzR,OAAO,qBAAS8rB,UAAU,CAAC5I,CAAD,EAAI+I,UAAJ,CAAnB,EAAP;AACA;AACD;AAnCH;;AAsCAZ,IAAAA,KAAK,CAAC3d,EAAD,CAAL;;AAEA,gCAA0B1N,OAA1B,8BAAmC;AAA9B;AAAA,UAAOwC,IAAP;AAAA,UAAa+B,GAAb;;AACH,yBAAe3B,YAAM,CAACnF,IAAP,CAAYylB,CAAZ,EAAe1gB,IAAf,CAAf;AAAA;AAAA,UAAO/E,IAAP;;AACAxC,MAAAA,WAAW,CAACoG,GAAZ,CAAgB5D,IAAhB,EAAsB8G,GAAtB;AACD;AACF,GAzED;;AA2EA2e,EAAAA,CAAC,CAAC5c,eAAF,GAAoB,UAACP,IAAD;AAClB,QAAQvE,SAAR,GAAsB0hB,CAAtB,CAAQ1hB,SAAR;;AAEA,QAAI,CAACA,SAAL,EAAgB;AACd;AACD;;AAED,uBAAqBmG,WAAK,CAACsK,KAAN,CAAYzQ,SAAZ,CAArB;AAAA;AAAA,QAAOsF,KAAP;AAAA,QAAcG,GAAd;;AACA,QAAMue,SAAS,GAAG5iB,YAAM,QAAN,CAAYsgB,CAAZ,EAAe;AAAExc,MAAAA,EAAE,EAAEI,KAAK,CAACtE;AAAZ,KAAf,CAAlB;AACA,QAAMijB,OAAO,GAAG7iB,YAAM,QAAN,CAAYsgB,CAAZ,EAAe;AAAExc,MAAAA,EAAE,EAAEO,GAAG,CAACzE;AAAV,KAAf,CAAhB;;AAEA,QAAImF,WAAK,CAACG,WAAN,CAAkBtG,SAAlB,KAAgC,CAACgkB,SAArC,EAAgD;AAC9C;AACD;AAGD;;;AACA,QAAM7hB,QAAQ,GAAG1D,WAAW,CAACwH,UAAZ,CAAuByb,CAAvB,EAA0B1hB,SAA1B,CAAjB;AACA,QAAIuH,QAAQ,GAAGpF,QAAQ,CAACqF,aAAT,EAAf;AACA,QAAImjB,MAAM,GAAGpjB,QAAQ,CAACpL,UAAT,CAAoB,CAApB,CAAb;;AAGAoL,IAAAA,QAAQ,CAACpL,UAAT,CAAoByL,OAApB,CAA4B,UAAA3L,IAAI;AAC9B,UAAIA,IAAI,CAACQ,WAAL,IAAoBR,IAAI,CAACQ,WAAL,CAAiBmuB,IAAjB,OAA4B,EAApD,EAAwD;AACtDD,QAAAA,MAAM,GAAG1uB,IAAT;AACD;AACF,KAJD;AAOA;AACA;;AACA,QAAIgoB,OAAJ,EAAa;AACX,oCAAmBA,OAAnB;AAAA,UAAO5c,QAAP;;AACA,UAAMwjB,CAAC,GAAG1oB,QAAQ,CAAC2oB,UAAT,EAAV;AACA,UAAM1tB,OAAO,GAAGqB,WAAW,CAACiB,SAAZ,CAAsBgiB,CAAtB,EAAyBra,QAAzB,CAAhB;AACAwjB,MAAAA,CAAC,CAACE,WAAF,CAAc3tB,OAAd;AACAmK,MAAAA,QAAQ,GAAGsjB,CAAC,CAACrjB,aAAF,EAAX;AACD;AAGD;AACA;AACA;;;AACA,QAAIwc,SAAJ,EAAe;AACb2G,MAAAA,MAAM,GAAGpjB,QAAQ,CAACW,aAAT,CAAuB,qBAAvB,CAAT;AACD;AAGD;;;AACA3K,IAAAA,KAAK,CAACC,IAAN,CAAW+J,QAAQ,CAAClC,gBAAT,CAA0B,yBAA1B,CAAX,EAAiEuC,OAAjE,CACE,UAAAojB,EAAE;AACA,UAAMC,SAAS,GAAGD,EAAE,CAAC9tB,YAAH,CAAgB,uBAAhB,MAA6C,GAA/D;AACA8tB,MAAAA,EAAE,CAACvuB,WAAH,GAAiBwuB,SAAS,GAAG,IAAH,GAAU,EAApC;AACD,KAJH;AAQA;AACA;;AACA,QAAIzvB,SAAS,CAACmvB,MAAD,CAAb,EAAuB;AACrB,UAAM/S,IAAI,GAAG+S,MAAM,CAAC7vB,aAAP,CAAqBnC,aAArB,CAAmC,MAAnC,CAAb,CADqB;AAGrB;;AACAif,MAAAA,IAAI,CAACjI,KAAL,CAAWwS,UAAX,GAAwB,KAAxB;AACAvK,MAAAA,IAAI,CAACsT,WAAL,CAAiBP,MAAjB;AACApjB,MAAAA,QAAQ,CAAC2jB,WAAT,CAAqBtT,IAArB;AACA+S,MAAAA,MAAM,GAAG/S,IAAT;AACD;;AAED,QAAMzZ,QAAQ,GAAGujB,CAAC,CAACyJ,WAAF,EAAjB;AACA,QAAMvW,MAAM,GAAGwW,IAAI,CAACjoB,SAAL,CAAehF,QAAf,CAAf;AACA,QAAMktB,OAAO,GAAG3zB,MAAM,CAAC4zB,IAAP,CAAYC,kBAAkB,CAAC3W,MAAD,CAA9B,CAAhB;AACA+V,IAAAA,MAAM,CAACa,YAAP,CAAoB,qBAApB,EAA2CH,OAA3C;AACA9mB,IAAAA,IAAI,CAACknB,OAAL,uBAA4B7B,kBAA5B,GAAkDyB,OAAlD;;AAGA,QAAMK,GAAG,GAAGnkB,QAAQ,CAACzM,aAAT,CAAuBnC,aAAvB,CAAqC,KAArC,CAAZ;AACA+yB,IAAAA,GAAG,CAACR,WAAJ,CAAgB3jB,QAAhB;AACAmkB,IAAAA,GAAG,CAACF,YAAJ,CAAiB,QAAjB,EAA2B,MAA3B;AACAjkB,IAAAA,QAAQ,CAACzM,aAAT,CAAuB6wB,IAAvB,CAA4BT,WAA5B,CAAwCQ,GAAxC;AACAnnB,IAAAA,IAAI,CAACknB,OAAL,CAAa,WAAb,EAA0BC,GAAG,CAACtiB,SAA9B;AACA7E,IAAAA,IAAI,CAACknB,OAAL,CAAa,YAAb,EAA2BtuB,YAAY,CAACuuB,GAAD,CAAvC;AACAnkB,IAAAA,QAAQ,CAACzM,aAAT,CAAuB6wB,IAAvB,CAA4B7jB,WAA5B,CAAwC4jB,GAAxC;AACA,WAAOnnB,IAAP;AACD,GApFD;;AAsFAmd,EAAAA,CAAC,CAACpd,UAAF,GAAe,UAACC,IAAD;AACb,QAAI,CAACmd,CAAC,CAACld,kBAAF,CAAqBD,IAArB,CAAL,EAAiC;AAC/Bmd,MAAAA,CAAC,CAACjd,cAAF,CAAiBF,IAAjB;AACD;AACF,GAJD;;AAMAmd,EAAAA,CAAC,CAACld,kBAAF,GAAuB,UAACD,IAAD;AACrB;;;AAGA,QAAMpG,QAAQ,GACZoG,IAAI,CAAC3I,OAAL,uBAA4BguB,kBAA5B,MACA7rB,yBAAyB,CAACwG,IAAD,CAF3B;;AAIA,QAAIpG,QAAJ,EAAc;AACZ,UAAMytB,OAAO,GAAGC,kBAAkB,CAACn0B,MAAM,CAACo0B,IAAP,CAAY3tB,QAAZ,CAAD,CAAlC;AACA,UAAM4tB,MAAM,GAAGX,IAAI,CAACY,KAAL,CAAWJ,OAAX,CAAf;AACAlK,MAAAA,CAAC,CAACuK,cAAF,CAAiBF,MAAjB;AACA,aAAO,IAAP;AACD;;AACD,WAAO,KAAP;AACD,GAfD;;AAiBArK,EAAAA,CAAC,CAACjd,cAAF,GAAmB,UAACF,IAAD;AACjB,QAAMlH,IAAI,GAAGkH,IAAI,CAAC3I,OAAL,CAAa,YAAb,CAAb;;AAEA,QAAIyB,IAAJ,EAAU;AACR,UAAM6uB,KAAK,GAAG7uB,IAAI,CAAC9F,KAAL,CAAW,YAAX,CAAd;AACA,UAAIA,KAAK,GAAG,KAAZ;;AAFQ,iDAIW20B,KAJX;AAAA;;AAAA;AAIR,4DAA0B;AAAA,cAAf7a,IAAe;;AACxB,cAAI9Z,KAAJ,EAAW;AACT8I,YAAAA,gBAAU,CAAC8rB,UAAX,CAAsBzK,CAAtB,EAAyB;AAAE0K,cAAAA,MAAM,EAAE;AAAV,aAAzB;AACD;;AAED1K,UAAAA,CAAC,CAAC5S,UAAF,CAAauC,IAAb;AACA9Z,UAAAA,KAAK,GAAG,IAAR;AACD;AAXO;AAAA;AAAA;AAAA;AAAA;;AAYR,aAAO,IAAP;AACD;;AACD,WAAO,KAAP;AACD,GAlBD;;AAoBAmqB,EAAAA,CAAC,CAACtS,QAAF,GAAa,UAAA9L,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA,QAAM+oB,iBAAiB,GACrBl1B,mBAAmB,GAAG,EAAtB,GACIm1B,4BAAQ,CAACC,uBADb,GAEI,UAAC5Z,QAAD;AAAA,aAA0BA,QAAQ,EAAlC;AAAA,KAHN;AAKA0Z,IAAAA,iBAAiB,CAAC;AAChB,UAAMhE,eAAe,GAAGtuB,mBAAmB,CAACuF,GAApB,CAAwBoiB,CAAxB,CAAxB;;AAEA,UAAI2G,eAAJ,EAAqB;AACnBA,QAAAA,eAAe;AAChB;;AAEDjZ,MAAAA,QAAQ,CAAC9L,OAAD,CAAR;AACD,KARgB,CAAjB;AASD,GApBD;;AAsBA,SAAOoe,CAAP;AACD;;AAED,IAAM4I,UAAU,GAAG,SAAbA,UAAa,CAAC5I,CAAD,EAAY1gB,IAAZ;AACjB,MAAMxC,OAAO,GAAkB,EAA/B;;8CACqB4C,YAAM,CAACorB,MAAP,CAAc9K,CAAd,EAAiB;AAAExc,IAAAA,EAAE,EAAElE;AAAN,GAAjB;;;;AAArB,2DAAqD;AAAA;AAAA,UAAzC9B,CAAyC;AAAA,UAAtC4Y,CAAsC;;AACnD,UAAM/U,GAAG,GAAGtE,WAAW,CAACqE,OAAZ,CAAoB4e,CAApB,EAAuBxiB,CAAvB,CAAZ;AACAV,MAAAA,OAAO,CAACyR,IAAR,CAAa,CAAC6H,CAAD,EAAI/U,GAAJ,CAAb;AACD;;;;;;;AACD,SAAOvE,OAAP;AACD,CAPD;;;;;;;;;;;;;;;;;;;"}