import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { message, session_id, file_context } = req.body

    if (!message) {
      return res.status(400).json({ error: 'Message is required' })
    }

    // Forward request to FastAPI backend
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
    
    const response = await fetch(`${backendUrl}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        session_id: session_id || 'default',
      }),
    })

    if (!response.ok) {
      throw new Error(`Backend request failed: ${response.status}`)
    }

    const data = await response.json()
    
    res.status(200).json({
      response: data.response,
      sources: data.sources,
      route: data.route_used,
    })
  } catch (error) {
    console.error('Chat API error:', error)
    res.status(500).json({ 
      response: 'I apologize, but I encountered an error. Please ensure the backend server is running on http://localhost:8000 and try again.',
      sources: [],
      route: 'error'
    })
  }
}
