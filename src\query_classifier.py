"""
Query classification module to route queries between RAG and web search
"""
import re
from typing import Dict, List
from langchain_openai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain.schema import HumanMessage, SystemMessage

from config import Config
from src.models import QueryClassification

class QueryClassifier:
    """
    Classifies user queries to determine routing between RAG and web search
    """
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model_name=Config.OPENAI_MODEL,
            openai_api_key=Config.OPENAI_API_KEY,
            temperature=0.1
        )
        self.domain_keywords = Config.DOMAIN_KEYWORDS
    
    def classify_query(self, query: str) -> QueryClassification:
        """
        Classify a user query to determine the appropriate routing
        
        Args:
            query: User's input query
            
        Returns:
            QueryClassification with route and confidence
        """
        # First, try keyword-based classification
        keyword_result = self._keyword_based_classification(query)
        
        # If keyword classification is confident, use it
        if keyword_result.confidence > 0.7:
            return keyword_result
        
        # Otherwise, use LLM-based classification
        return self._llm_based_classification(query)
    
    def _keyword_based_classification(self, query: str) -> QueryClassification:
        """
        Simple keyword-based classification
        """
        query_lower = query.lower()
        
        # Count domain keyword matches
        domain_matches = sum(1 for keyword in self.domain_keywords if keyword in query_lower)
        
        # Calculate confidence based on matches
        if domain_matches > 0:
            confidence = min(0.8, 0.3 + (domain_matches * 0.2))
            return QueryClassification(
                route="rag",
                confidence=confidence,
                reasoning=f"Found {domain_matches} domain keywords: {[kw for kw in self.domain_keywords if kw in query_lower]}"
            )
        
        # Check for web search indicators
        web_indicators = [
            "latest", "recent", "news", "current", "today", "yesterday",
            "what's happening", "trending", "update", "breaking"
        ]
        
        web_matches = sum(1 for indicator in web_indicators if indicator in query_lower)
        
        if web_matches > 0:
            return QueryClassification(
                route="web_search",
                confidence=0.6,
                reasoning=f"Found web search indicators: {[ind for ind in web_indicators if ind in query_lower]}"
            )
        
        # Default to low confidence RAG for domain-neutral queries
        return QueryClassification(
            route="rag",
            confidence=0.3,
            reasoning="No clear indicators found, defaulting to domain knowledge"
        )
    
    def _llm_based_classification(self, query: str) -> QueryClassification:
        """
        Use LLM to classify queries that are ambiguous
        """
        system_prompt = f"""
        You are a query classifier for a CA (Chartered Accountant) professional chatbot.
        
        Your task is to classify user queries into two categories:
        1. "rag" - Questions related to CA domain knowledge (tax, legal, compliance, business, accounting, etc.)
        2. "web_search" - Questions requiring current information, news, or general knowledge outside the CA domain
        
        Domain keywords include: {', '.join(self.domain_keywords)}
        
        Respond with ONLY a JSON object in this format:
        {{
            "route": "rag" or "web_search",
            "confidence": 0.0-1.0,
            "reasoning": "brief explanation"
        }}
        """
        
        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"Classify this query: {query}")
            ]
            
            response = self.llm(messages)
            
            # Parse the JSON response
            import json
            result = json.loads(response.content.strip())
            
            return QueryClassification(
                route=result["route"],
                confidence=result["confidence"],
                reasoning=result["reasoning"]
            )
            
        except Exception as e:
            # Fallback to keyword classification if LLM fails
            return self._keyword_based_classification(query)
    
    def should_use_web_search(self, query: str) -> bool:
        """
        Simple boolean check for web search routing
        """
        classification = self.classify_query(query)
        return classification.route == "web_search"