import { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, File, X, CheckCircle } from 'lucide-react'

interface FileUploadProps {
  onFileUpload: (file: File | null) => void
  uploadedFile: File | null
}

const SUPPORTED_TYPES = {
  'application/pdf': 'PDF',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCX',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'XLSX',
  'application/vnd.ms-excel': 'XLS',
  'text/csv': 'CSV',
  'text/plain': 'TXT',
  'image/jpeg': 'JPEG',
  'image/png': 'PNG',
  'image/bmp': 'BMP',
  'image/tiff': 'TIFF'
}

export default function FileUpload({ onFileUpload, uploadedFile }: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return

    const file = acceptedFiles[0]
    setError(null)
    setIsUploading(true)

    try {
      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('File size must be less than 10MB')
      }

      // Validate file type
      if (!Object.keys(SUPPORTED_TYPES).includes(file.type)) {
        throw new Error(`Unsupported file type. Supported types: ${Object.values(SUPPORTED_TYPES).join(', ')}`)
      }

      onFileUpload(file)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed')
      onFileUpload(null)
    } finally {
      setIsUploading(false)
    }
  }, [onFileUpload])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: Object.keys(SUPPORTED_TYPES).reduce((acc, type) => {
      acc[type] = []
      return acc
    }, {} as Record<string, string[]>),
    multiple: false,
    disabled: isUploading
  })

  const removeFile = () => {
    onFileUpload(null)
    setError(null)
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (uploadedFile) {
    return (
      <div className="flex items-center space-x-3 p-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl">
        <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
          <CheckCircle className="w-5 h-5 text-white" />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-white truncate">
            {uploadedFile.name}
          </p>
          <p className="text-xs text-blue-100">
            {formatFileSize(uploadedFile.size)} • {SUPPORTED_TYPES[uploadedFile.type as keyof typeof SUPPORTED_TYPES] || 'Unknown'}
          </p>
        </div>
        <button
          onClick={removeFile}
          className="p-1 text-white/80 hover:text-white transition-colors hover:bg-white/10 rounded-lg"
          title="Remove file"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    )
  }

  return (
    <div className="w-full max-w-xs">
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-xl p-4 text-center cursor-pointer transition-all duration-300
          ${isDragActive
            ? 'border-white/60 bg-white/10 backdrop-blur-sm scale-105'
            : 'border-white/30 hover:border-white/50 hover:bg-white/5'
          }
          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />

        <div className="space-y-2">
          <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center mx-auto">
            <Upload className={`w-5 h-5 ${isDragActive ? 'text-white' : 'text-white/80'}`} />
          </div>

          {isUploading ? (
            <div className="space-y-2">
              <p className="text-sm text-white">Uploading...</p>
              <div className="w-full bg-white/20 rounded-full h-2">
                <div className="bg-white/60 h-2 rounded-full animate-pulse-slow w-1/2"></div>
              </div>
            </div>
          ) : (
            <div className="space-y-1">
              <p className="text-sm font-medium text-white">
                {isDragActive ? 'Drop here' : 'Upload'}
              </p>
              <p className="text-xs text-blue-100">
                PDF, DOCX, XLSX, CSV, TXT, Images
              </p>
            </div>
          )}
        </div>
      </div>

      {error && (
        <div className="mt-2 p-2 bg-red-500/20 backdrop-blur-sm border border-red-400/30 rounded-lg text-sm text-red-100">
          {error}
        </div>
      )}
    </div>
  )
}