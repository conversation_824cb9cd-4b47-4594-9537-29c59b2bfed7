"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/CustomChatInterface.tsx":
/*!********************************************!*\
  !*** ./components/CustomChatInterface.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Paperclip,Send,User!=!lucide-react */ \"__barrel_optimize__?names=Bot,Paperclip,Send,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\nfunction CustomChatInterface(param) {\n    let { uploadedFile } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"Hello! I'm your CA professional assistant. I can help you with tax, legal, compliance, and business questions. You can also upload documents for analysis. How can I assist you today? \\uD83D\\uDE80\",\n            timestamp: new Date()\n        }\n    ]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleSend = async ()=>{\n        if (!inputValue.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: inputValue.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue(\"\");\n        setIsLoading(true);\n        try {\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    session_id: \"custom-chat-session\",\n                    file_context: uploadedFile ? {\n                        name: uploadedFile.name,\n                        type: uploadedFile.type,\n                        size: uploadedFile.size\n                    } : null\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: data.response || \"I apologize, but I encountered an error. Please try again.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Chat error:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"I apologize, but I encountered an error connecting to the backend. Please ensure the backend server is running and try again.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const formatTime = (date)=>{\n        // Use a consistent format to avoid hydration errors\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-[600px] bg-gradient-to-br from-slate-50/50 via-blue-50/30 to-purple-50/50 relative overflow-hidden rounded-3xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-10 right-10 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-2xl animate-pulse-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-10 left-10 w-24 h-24 bg-gradient-to-br from-green-400/10 to-blue-600/10 rounded-full blur-2xl animate-pulse-slow\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-full flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6 overflow-y-auto space-y-4 scrollbar-thin scrollbar-thumb-blue-200 scrollbar-track-transparent\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3 \".concat(message.role === \"user\" ? \"flex-row-reverse space-x-reverse\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-xl flex items-center justify-center shadow-lg flex-shrink-0 \".concat(message.role === \"assistant\" ? \"bg-gradient-to-br from-blue-500 to-purple-600\" : \"bg-gradient-to-br from-green-500 to-blue-500\"),\n                                            children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Bot, {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__.User, {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 max-w-[80%] \".concat(message.role === \"user\" ? \"flex justify-end\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-2xl p-4 shadow-lg border backdrop-blur-sm \".concat(message.role === \"assistant\" ? \"bg-white/80 border-white/20 text-gray-800\" : \"bg-gradient-to-r from-blue-600 to-purple-600 border-blue-500/20 text-white\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"leading-relaxed whitespace-pre-wrap\",\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-2 \".concat(message.role === \"assistant\" ? \"text-gray-500\" : \"text-blue-100\"),\n                                                        children: isClient ? formatTime(message.timestamp) : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, message.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Bot, {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-purple-500 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.1s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.2s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-white/20 backdrop-blur-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl border border-white/30 shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: textareaRef,\n                                        value: inputValue,\n                                        onChange: (e)=>setInputValue(e.target.value),\n                                        onKeyPress: handleKeyPress,\n                                        placeholder: \"Ask me anything about tax, legal, or business matters...\",\n                                        className: \"w-full p-4 bg-transparent border-none outline-none resize-none text-gray-800 placeholder-gray-500 min-h-[60px] max-h-32\",\n                                        disabled: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 pt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-500 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50\",\n                                                        title: \"Attach file\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Paperclip, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    uploadedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-green-600 bg-green-50 px-2 py-1 rounded-lg\",\n                                                        children: [\n                                                            \"\\uD83D\\uDCCE \",\n                                                            uploadedFile.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: \"Shift + Enter for new line\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSend,\n                                                disabled: !inputValue.trim() || isLoading,\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-xl hover:shadow-lg transition-all duration-300 hover:scale-105 font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Send, {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Send\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomChatInterface, \"Hv6XULwbAjjN1j1W++NNwHjtBWU=\");\n_c = CustomChatInterface;\nvar _c;\n$RefreshReg$(_c, \"CustomChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/CustomChatInterface.tsx\n"));

/***/ })

});