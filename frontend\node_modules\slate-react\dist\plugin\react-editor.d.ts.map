{"version": 3, "file": "react-editor.d.ts", "sourceRoot": "", "sources": ["../packages/slate-react/src/plugin/react-editor.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,UAAU,EACV,MAAM,EAEN,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EAGN,MAAM,OAAO,CAAA;AACd,OAAO,EAAE,QAAQ,EAAE,MAAM,oBAAoB,CAAA;AAC7C,OAAO,EAEL,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,cAAc,EAOf,MAAM,cAAc,CAAA;AAGrB,OAAO,EAAE,GAAG,EAAE,MAAM,cAAc,CAAA;AAgBlC;;GAEG;AAEH,MAAM,WAAW,WAAY,SAAQ,UAAU;IAC7C,iBAAiB,EAAE,CACjB,MAAM,EAAE,WAAW,EACnB,MAAM,EAAE,WAAW,GAAG,IAAI,KACvB,MAAM,IAAI,OAAO,CAAA;IACtB,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,KAAK,OAAO,CAAA;IACxD,mBAAmB,EAAE,CACnB,MAAM,EAAE,WAAW,EACnB,MAAM,EAAE,WAAW,GAAG,IAAI,KACvB,OAAO,CAAA;IACZ,SAAS,EAAE,CACT,MAAM,EAAE,WAAW,EACnB,MAAM,EAAE,WAAW,GAAG,IAAI,KACvB,MAAM,IAAI,OAAO,CAAA;IACtB,UAAU,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,IAAI,CAAA;IACxC,kBAAkB,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,OAAO,CAAA;IACnD,cAAc,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,OAAO,CAAA;IAC/C,6BAA6B,EAAE,CAC7B,MAAM,EAAE,WAAW,EACnB,MAAM,EAAE,WAAW,GAAG,IAAI,KACvB,OAAO,CAAA;IACZ,eAAe,EAAE,CACf,IAAI,EAAE,YAAY,EAClB,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,KAAK,KAClC,IAAI,CAAA;CACV;AAED,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,mBAAmB,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,QAAQ,EAAE,GAAG,SAAS,CAAA;IAE/D;;OAEG;IACH,oBAAoB,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAA;IAE9C;;OAEG;IACH,IAAI,EAAE,CAAC,MAAM,EAAE,WAAW,KAAK,IAAI,CAAA;IAEnC;;OAEG;IACH,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,KAAK,IAAI,CAAA;IAEvC;;OAEG;IACH,wBAAwB,EAAE,CAAC,MAAM,EAAE,WAAW,KAAK,QAAQ,GAAG,UAAU,CAAA;IAExE;;OAEG;IACH,cAAc,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,KAAK,KAAK,CAAA;IAE1D;;OAEG;IACH,OAAO,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,KAAK,GAAG,CAAA;IAEjD;;OAEG;IACH,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,KAAK,IAAI,CAAA;IAEnD;;OAEG;IACH,KAAK,EAAE,CAAC,MAAM,EAAE,WAAW,KAAK,IAAI,CAAA;IAEpC;;OAEG;IACH,SAAS,EAAE,CAAC,MAAM,EAAE,WAAW,KAAK,MAAM,CAAA;IAE1C;;OAEG;IACH,UAAU,EAAE,CACV,MAAM,EAAE,WAAW,EACnB,MAAM,EAAE,OAAO,EACf,OAAO,CAAC,EAAE;QAAE,QAAQ,CAAC,EAAE,OAAO,CAAA;KAAE,KAC7B,OAAO,CAAA;IAEZ;;OAEG;IACH,iBAAiB,EAAE,CACjB,MAAM,EAAE,WAAW,EACnB,MAAM,EAAE,WAAW,GAAG,IAAI,KACvB,MAAM,IAAI,OAAO,CAAA;IAEtB;;OAEG;IACH,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,KAAK,OAAO,CAAA;IAExD;;OAEG;IACH,mBAAmB,EAAE,CACnB,MAAM,EAAE,WAAW,EACnB,MAAM,EAAE,WAAW,GAAG,IAAI,KACvB,OAAO,CAAA;IAEZ;;OAEG;IACH,SAAS,EAAE,CACT,MAAM,EAAE,WAAW,EACnB,MAAM,EAAE,WAAW,GAAG,IAAI,KACvB,MAAM,IAAI,OAAO,CAAA;IAEtB;;OAEG;IACH,UAAU,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,KAAK,IAAI,CAAA;IAE7D;;OAEG;IACH,kBAAkB,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,KAAK,OAAO,CAAA;IAExE;;OAEG;IACH,cAAc,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,KAAK,OAAO,CAAA;IAEpE;;OAEG;IACH,WAAW,EAAE,CAAC,MAAM,EAAE,WAAW,KAAK,OAAO,CAAA;IAE7C;;OAEG;IACH,SAAS,EAAE,CAAC,MAAM,EAAE,WAAW,KAAK,OAAO,CAAA;IAE3C;;OAEG;IACH,UAAU,EAAE,CAAC,MAAM,EAAE,WAAW,KAAK,OAAO,CAAA;IAE5C;;OAEG;IACH,6BAA6B,EAAE,CAC7B,MAAM,EAAE,WAAW,EACnB,MAAM,EAAE,WAAW,GAAG,IAAI,KACvB,OAAO,CAAA;IAEZ;;OAEG;IACH,eAAe,EAAE,CACf,MAAM,EAAE,WAAW,EACnB,IAAI,EAAE,YAAY,EAClB,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,KAAK,KAClC,IAAI,CAAA;IAET;;OAEG;IACH,SAAS,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,KAAK,WAAW,CAAA;IAE3D;;OAEG;IACH,UAAU,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,KAAK,QAAQ,CAAA;IAE3D;;;;;;;OAOG;IACH,UAAU,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,KAAK,QAAQ,CAAA;IAE3D;;OAEG;IACH,WAAW,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,CAAA;IAE5D;;OAEG;IACH,YAAY,EAAE,CAAC,CAAC,SAAS,OAAO,EAC9B,MAAM,EAAE,WAAW,EACnB,QAAQ,EAAE,QAAQ,EAClB,OAAO,EAAE;QACP,UAAU,EAAE,OAAO,CAAA;QACnB,aAAa,EAAE,CAAC,CAAA;KACjB,KACE,CAAC,SAAS,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAA;IAE1C;;OAEG;IACH,YAAY,EAAE,CAAC,CAAC,SAAS,OAAO,EAC9B,MAAM,EAAE,WAAW,EACnB,QAAQ,EAAE,QAAQ,GAAG,cAAc,GAAG,YAAY,EAClD,OAAO,EAAE;QACP,UAAU,EAAE,OAAO,CAAA;QACnB,aAAa,EAAE,CAAC,CAAA;KACjB,KACE,CAAC,SAAS,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAA;CAC3C;AAGD,eAAO,MAAM,WAAW,EAAE,oBAstBzB,CAAA"}