declare const _default: {
    isBold: (event: KeyboardEvent) => boolean;
    isCompose: (event: KeyboardEvent) => boolean;
    isMoveBackward: (event: KeyboardEvent) => boolean;
    isMoveForward: (event: KeyboardEvent) => boolean;
    isDeleteBackward: (event: KeyboardEvent) => boolean;
    isDeleteForward: (event: KeyboardEvent) => boolean;
    isDeleteLineBackward: (event: KeyboardEvent) => boolean;
    isDeleteLineForward: (event: KeyboardEvent) => boolean;
    isDeleteWordBackward: (event: KeyboardEvent) => boolean;
    isDeleteWordForward: (event: KeyboardEvent) => boolean;
    isExtendBackward: (event: KeyboardEvent) => boolean;
    isExtendForward: (event: KeyboardEvent) => boolean;
    isExtendLineBackward: (event: KeyboardEvent) => boolean;
    isExtendLineForward: (event: KeyboardEvent) => boolean;
    isItalic: (event: KeyboardEvent) => boolean;
    isMoveLineBackward: (event: KeyboardEvent) => boolean;
    isMoveLineForward: (event: KeyboardEvent) => boolean;
    isMoveWordBackward: (event: KeyboardEvent) => boolean;
    isMoveWordForward: (event: KeyboardEvent) => boolean;
    isRedo: (event: KeyboardEvent) => boolean;
    isSoftBreak: (event: KeyboardEvent) => boolean;
    isSplitBlock: (event: KeyboardEvent) => boolean;
    isTransposeCharacter: (event: KeyboardEvent) => boolean;
    isUndo: (event: KeyboardEvent) => boolean;
};
/**
 * Hotkeys.
 */
export default _default;
//# sourceMappingURL=hotkeys.d.ts.map