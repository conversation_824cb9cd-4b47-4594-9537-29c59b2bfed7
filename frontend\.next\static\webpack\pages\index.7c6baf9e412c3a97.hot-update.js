/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/CustomChatInterface.tsx":
/*!********************************************!*\
  !*** ./components/CustomChatInterface.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Paperclip,Send,User!=!lucide-react */ \"__barrel_optimize__?names=Bot,Paperclip,Send,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\n\nfunction CustomChatInterface(param) {\n    let { uploadedFile } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"Hello! I'm your CA professional assistant. I can help you with tax, legal, compliance, and business questions. You can also upload documents for analysis. How can I assist you today? \\uD83D\\uDE80\",\n            timestamp: new Date()\n        }\n    ]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleSend = async ()=>{\n        if (!inputValue.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: inputValue.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue(\"\");\n        setIsLoading(true);\n        try {\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    session_id: \"custom-chat-session\",\n                    file_context: uploadedFile ? {\n                        name: uploadedFile.name,\n                        type: uploadedFile.type,\n                        size: uploadedFile.size\n                    } : null\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: data.response || \"I apologize, but I encountered an error. Please try again.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Chat error:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"I apologize, but I encountered an error connecting to the backend. Please ensure the backend server is running and try again.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const formatTime = (date)=>{\n        // Use a consistent format to avoid hydration errors\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-[600px] bg-gradient-to-br from-slate-50/50 via-blue-50/30 to-purple-50/50 relative overflow-hidden rounded-3xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-10 right-10 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-2xl animate-pulse-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-10 left-10 w-24 h-24 bg-gradient-to-br from-green-400/10 to-blue-600/10 rounded-full blur-2xl animate-pulse-slow\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-full flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6 overflow-y-auto space-y-4 scrollbar-thin scrollbar-thumb-blue-200 scrollbar-track-transparent\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3 \".concat(message.role === \"user\" ? \"flex-row-reverse space-x-reverse\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-xl flex items-center justify-center shadow-lg flex-shrink-0 \".concat(message.role === \"assistant\" ? \"bg-gradient-to-br from-blue-500 to-purple-600\" : \"bg-gradient-to-br from-green-500 to-blue-500\"),\n                                            children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Bot, {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.User, {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 max-w-[80%] \".concat(message.role === \"user\" ? \"flex justify-end\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-2xl p-4 shadow-lg border backdrop-blur-sm \".concat(message.role === \"assistant\" ? \"bg-white/80 border-white/20 text-gray-800\" : \"bg-gradient-to-r from-blue-600 to-purple-600 border-blue-500/20 text-white\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"leading-relaxed whitespace-pre-wrap\",\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-2 \".concat(message.role === \"assistant\" ? \"text-gray-500\" : \"text-blue-100\"),\n                                                        children: isClient ? formatTime(message.timestamp) : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, message.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Bot, {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-purple-500 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.1s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.2s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-white/20 backdrop-blur-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl border border-white/30 shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: textareaRef,\n                                        value: inputValue,\n                                        onChange: (e)=>setInputValue(e.target.value),\n                                        onKeyPress: handleKeyPress,\n                                        placeholder: \"Ask me anything about tax, legal, or business matters...\",\n                                        className: \"w-full p-4 bg-transparent border-none outline-none resize-none text-gray-800 placeholder-gray-500 min-h-[60px] max-h-32\",\n                                        disabled: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 pt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-500 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50\",\n                                                        title: \"Attach file\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Paperclip, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    uploadedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-green-600 bg-green-50 px-2 py-1 rounded-lg\",\n                                                        children: [\n                                                            \"\\uD83D\\uDCCE \",\n                                                            uploadedFile.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: \"Shift + Enter for new line\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSend,\n                                                disabled: !inputValue.trim() || isLoading,\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-xl hover:shadow-lg transition-all duration-300 hover:scale-105 font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Send, {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Send\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomChatInterface, \"Hv6XULwbAjjN1j1W++NNwHjtBWU=\");\n_c = CustomChatInterface;\n// Export as dynamic component to avoid hydration issues\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c2 = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c1 = ()=>Promise.resolve(CustomChatInterface), {\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-[600px] bg-gradient-to-br from-slate-50/50 via-blue-50/30 to-purple-50/50 relative overflow-hidden rounded-3xl flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Bot, {\n                            className: \"w-6 h-6 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading chat interface...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n            lineNumber: 230,\n            columnNumber: 5\n        }, undefined)\n}));\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CustomChatInterface\");\n$RefreshReg$(_c1, \"%default%$dynamic\");\n$RefreshReg$(_c2, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/CustomChatInterface.tsx\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/dynamic.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/dynamic.js ***!
  \******************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    noSSR: function() {\n        return noSSR;\n    },\n    default: function() {\n        return dynamic;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _loadablesharedruntime = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./loadable.shared-runtime */ \"./node_modules/next/dist/shared/lib/loadable.shared-runtime.js\"));\nconst isServerSide = \"object\" === \"undefined\";\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    return {\n        default: (mod == null ? void 0 : mod.default) || mod\n    };\n}\nfunction noSSR(LoadableInitializer, loadableOptions) {\n    // Removing webpack and modules means react-loadable won't try preloading\n    delete loadableOptions.webpack;\n    delete loadableOptions.modules;\n    // This check is necessary to prevent react-loadable from initializing on the server\n    if (!isServerSide) {\n        return LoadableInitializer(loadableOptions);\n    }\n    const Loading = loadableOptions.loading;\n    // This will only be rendered on the server side\n    return ()=>/*#__PURE__*/ _react.default.createElement(Loading, {\n            error: null,\n            isLoading: true,\n            pastDelay: false,\n            timedOut: false\n        });\n}\nfunction dynamic(dynamicOptions, options) {\n    let loadableFn = _loadablesharedruntime.default;\n    let loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ _react.default.createElement(\"p\", null, error.message, /*#__PURE__*/ _react.default.createElement(\"br\", null), error.stack);\n                }\n            }\n            return null;\n        }\n    };\n    // Support for direct import(), eg: dynamic(import('../hello-world'))\n    // Note that this is only kept for the edge case where someone is passing in a promise as first argument\n    // The react-loadable babel plugin will turn dynamic(import('../hello-world')) into dynamic(() => import('../hello-world'))\n    // To make sure we don't execute the import without rendering first\n    if (dynamicOptions instanceof Promise) {\n        loadableOptions.loader = ()=>dynamicOptions;\n    // Support for having import as a function, eg: dynamic(() => import('../hello-world'))\n    } else if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    // Support for having first argument being options, eg: dynamic({loader: import('../hello-world')})\n    } else if (typeof dynamicOptions === \"object\") {\n        loadableOptions = {\n            ...loadableOptions,\n            ...dynamicOptions\n        };\n    }\n    // Support for passing options, eg: dynamic(import('../hello-world'), {loading: () => <p>Loading something</p>})\n    loadableOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    const loaderFn = loadableOptions.loader;\n    const loader = ()=>loaderFn != null ? loaderFn().then(convertModule) : Promise.resolve(convertModule(()=>null));\n    // coming from build/babel/plugins/react-loadable-plugin.js\n    if (loadableOptions.loadableGenerated) {\n        loadableOptions = {\n            ...loadableOptions,\n            ...loadableOptions.loadableGenerated\n        };\n        delete loadableOptions.loadableGenerated;\n    }\n    // support for disabling server side rendering, eg: dynamic(() => import('../hello-world'), {ssr: false}).\n    if (typeof loadableOptions.ssr === \"boolean\" && !loadableOptions.ssr) {\n        delete loadableOptions.webpack;\n        delete loadableOptions.modules;\n        return noSSR(loadableFn, loadableOptions);\n    }\n    return loadableFn({\n        ...loadableOptions,\n        loader: loader\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/dynamic.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js ***!
  \******************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"LoadableContext\", ({\n    enumerable: true,\n    get: function() {\n        return LoadableContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst LoadableContext = _react.default.createContext(null);\nif (true) {\n    LoadableContext.displayName = \"LoadableContext\";\n} //# sourceMappingURL=loadable-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbG9hZGFibGUtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoicURBRWE7QUFDYkEsOENBQTZDO0lBQ3pDRyxPQUFPO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILG1EQUFrRDtJQUM5Q0ksWUFBWTtJQUNaQyxLQUFLO1FBQ0QsT0FBT0M7SUFDWDtBQUNKLENBQUMsRUFBQztBQUNGLE1BQU1DLDJCQUEyQkMsbUJBQU9BLENBQUMsNEdBQXlDO0FBQ2xGLE1BQU1DLFNBQVMsV0FBVyxHQUFHRix5QkFBeUJHLENBQUMsQ0FBQ0YsbUJBQU9BLENBQUMsNENBQU87QUFDdkUsTUFBTUYsa0JBQWtCRyxPQUFPRSxPQUFPLENBQUNDLGFBQWEsQ0FBQztBQUNyRCxJQUFJQyxJQUFxQyxFQUFFO0lBQ3ZDUCxnQkFBZ0JRLFdBQVcsR0FBRztBQUNsQyxFQUVBLDJEQUEyRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbG9hZGFibGUtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcz9iYTEwIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5cInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIkxvYWRhYmxlQ29udGV4dFwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gTG9hZGFibGVDb250ZXh0O1xuICAgIH1cbn0pO1xuY29uc3QgX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0ID0gcmVxdWlyZShcIkBzd2MvaGVscGVycy9fL19pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdFwiKTtcbmNvbnN0IF9yZWFjdCA9IC8qI19fUFVSRV9fKi8gX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0Ll8ocmVxdWlyZShcInJlYWN0XCIpKTtcbmNvbnN0IExvYWRhYmxlQ29udGV4dCA9IF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUNvbnRleHQobnVsbCk7XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgTG9hZGFibGVDb250ZXh0LmRpc3BsYXlOYW1lID0gXCJMb2FkYWJsZUNvbnRleHRcIjtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9hZGFibGUtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiTG9hZGFibGVDb250ZXh0IiwiX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0IiwicmVxdWlyZSIsIl9yZWFjdCIsIl8iLCJkZWZhdWx0IiwiY3JlYXRlQ29udGV4dCIsInByb2Nlc3MiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/loadable.shared-runtime.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/loadable.shared-runtime.js ***!
  \**********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("// TODO: Remove use of `any` type.\n/**\n@copyright (c) 2017-present James Kyle <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE\n*/ // https://github.com/jamiebuilds/react-loadable/blob/v5.5.0/src/index.js\n// Modified to be compatible with webpack 4 / Next.js\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _loadablecontextsharedruntime = __webpack_require__(/*! ./loadable-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js\");\nfunction resolve(obj) {\n    return obj && obj.default ? obj.default : obj;\n}\nconst ALL_INITIALIZERS = [];\nconst READY_INITIALIZERS = [];\nlet initialized = false;\nfunction load(loader) {\n    let promise = loader();\n    let state = {\n        loading: true,\n        loaded: null,\n        error: null\n    };\n    state.promise = promise.then((loaded)=>{\n        state.loading = false;\n        state.loaded = loaded;\n        return loaded;\n    }).catch((err)=>{\n        state.loading = false;\n        state.error = err;\n        throw err;\n    });\n    return state;\n}\nfunction createLoadableComponent(loadFn, options) {\n    var _s = $RefreshSig$(), _s1 = $RefreshSig$();\n    let opts = Object.assign({\n        loader: null,\n        loading: null,\n        delay: 200,\n        timeout: null,\n        webpack: null,\n        modules: null\n    }, options);\n    /** @type LoadableSubscription */ let subscription = null;\n    function init() {\n        if (!subscription) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            const sub = new LoadableSubscription(loadFn, opts);\n            subscription = {\n                getCurrentValue: sub.getCurrentValue.bind(sub),\n                subscribe: sub.subscribe.bind(sub),\n                retry: sub.retry.bind(sub),\n                promise: sub.promise.bind(sub)\n            };\n        }\n        return subscription.promise();\n    }\n    // Server only\n    if (false) {}\n    // Client only\n    if (!initialized && \"object\" !== \"undefined\") {\n        // require.resolveWeak check is needed for environments that don't have it available like Jest\n        const moduleIds = opts.webpack && \"function\" === \"function\" ? opts.webpack() : opts.modules;\n        if (moduleIds) {\n            READY_INITIALIZERS.push((ids)=>{\n                for (const moduleId of moduleIds){\n                    if (ids.includes(moduleId)) {\n                        return init();\n                    }\n                }\n            });\n        }\n    }\n    function useLoadableModule() {\n        _s();\n        init();\n        const context = _react.default.useContext(_loadablecontextsharedruntime.LoadableContext);\n        if (context && Array.isArray(opts.modules)) {\n            opts.modules.forEach((moduleName)=>{\n                context(moduleName);\n            });\n        }\n    }\n    _s(useLoadableModule, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n    function LoadableComponent(props, ref) {\n        _s1();\n        useLoadableModule();\n        const state = _react.default.useSyncExternalStore(subscription.subscribe, subscription.getCurrentValue, subscription.getCurrentValue);\n        _react.default.useImperativeHandle(ref, ()=>({\n                retry: subscription.retry\n            }), []);\n        return _react.default.useMemo(()=>{\n            if (state.loading || state.error) {\n                return /*#__PURE__*/ _react.default.createElement(opts.loading, {\n                    isLoading: state.loading,\n                    pastDelay: state.pastDelay,\n                    timedOut: state.timedOut,\n                    error: state.error,\n                    retry: subscription.retry\n                });\n            } else if (state.loaded) {\n                return /*#__PURE__*/ _react.default.createElement(resolve(state.loaded), props);\n            } else {\n                return null;\n            }\n        }, [\n            props,\n            state\n        ]);\n    }\n    _s1(LoadableComponent, \"FetqI339RA+IfltT8VNzX8RMZ2Q=\", false, function() {\n        return [\n            useLoadableModule\n        ];\n    });\n    LoadableComponent.preload = ()=>init();\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return /*#__PURE__*/ _react.default.forwardRef(LoadableComponent);\n}\nclass LoadableSubscription {\n    promise() {\n        return this._res.promise;\n    }\n    retry() {\n        this._clearTimeouts();\n        this._res = this._loadFn(this._opts.loader);\n        this._state = {\n            pastDelay: false,\n            timedOut: false\n        };\n        const { _res: res, _opts: opts } = this;\n        if (res.loading) {\n            if (typeof opts.delay === \"number\") {\n                if (opts.delay === 0) {\n                    this._state.pastDelay = true;\n                } else {\n                    this._delay = setTimeout(()=>{\n                        this._update({\n                            pastDelay: true\n                        });\n                    }, opts.delay);\n                }\n            }\n            if (typeof opts.timeout === \"number\") {\n                this._timeout = setTimeout(()=>{\n                    this._update({\n                        timedOut: true\n                    });\n                }, opts.timeout);\n            }\n        }\n        this._res.promise.then(()=>{\n            this._update({});\n            this._clearTimeouts();\n        }).catch((_err)=>{\n            this._update({});\n            this._clearTimeouts();\n        });\n        this._update({});\n    }\n    _update(partial) {\n        this._state = {\n            ...this._state,\n            error: this._res.error,\n            loaded: this._res.loaded,\n            loading: this._res.loading,\n            ...partial\n        };\n        this._callbacks.forEach((callback)=>callback());\n    }\n    _clearTimeouts() {\n        clearTimeout(this._delay);\n        clearTimeout(this._timeout);\n    }\n    getCurrentValue() {\n        return this._state;\n    }\n    subscribe(callback) {\n        this._callbacks.add(callback);\n        return ()=>{\n            this._callbacks.delete(callback);\n        };\n    }\n    constructor(loadFn, opts){\n        this._loadFn = loadFn;\n        this._opts = opts;\n        this._callbacks = new Set();\n        this._delay = null;\n        this._timeout = null;\n        this.retry();\n    }\n}\nfunction Loadable(opts) {\n    return createLoadableComponent(load, opts);\n}\n_c = Loadable;\nfunction flushInitializers(initializers, ids) {\n    let promises = [];\n    while(initializers.length){\n        let init = initializers.pop();\n        promises.push(init(ids));\n    }\n    return Promise.all(promises).then(()=>{\n        if (initializers.length) {\n            return flushInitializers(initializers, ids);\n        }\n    });\n}\nLoadable.preloadAll = ()=>{\n    return new Promise((resolveInitializers, reject)=>{\n        flushInitializers(ALL_INITIALIZERS).then(resolveInitializers, reject);\n    });\n};\nLoadable.preloadReady = (ids)=>{\n    if (ids === void 0) ids = [];\n    return new Promise((resolvePreload)=>{\n        const res = ()=>{\n            initialized = true;\n            return resolvePreload();\n        };\n        // We always will resolve, errors should be handled within loading UIs.\n        flushInitializers(READY_INITIALIZERS, ids).then(res, res);\n    });\n};\nif (true) {\n    window.__NEXT_PRELOADREADY = Loadable.preloadReady;\n}\nconst _default = Loadable; //# sourceMappingURL=loadable.shared-runtime.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/loadable.shared-runtime.js\n"));

/***/ }),

/***/ "./node_modules/next/dynamic.js":
/*!**************************************!*\
  !*** ./node_modules/next/dynamic.js ***!
  \**************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/dynamic */ \"./node_modules/next/dist/shared/lib/dynamic.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9keW5hbWljLmpzIiwibWFwcGluZ3MiOiJBQUFBLHVIQUFxRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9keW5hbWljLmpzPzczZDQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9keW5hbWljJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dynamic.js\n"));

/***/ })

});