@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
}

@layer components {
  .chat-message {
    @apply max-w-3xl rounded-2xl p-4 shadow-sm;
  }

  .chat-message-user {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white ml-auto;
  }

  .chat-message-assistant {
    @apply bg-white/80 backdrop-blur-sm text-gray-900 mr-auto border border-gray-200/50;
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent;
  }
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar,
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track,
.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb,
.scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover,
.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

.scrollbar-thumb-blue-200::-webkit-scrollbar-thumb {
  background: rgba(191, 219, 254, 0.6);
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}

/* Animation for typing indicator */
@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.animate-bounce {
  animation: bounce 1.4s infinite ease-in-out both;
}

/* File upload drag and drop styles */
.drag-active {
  @apply border-blue-400 bg-blue-50;
}

/* Loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Pulse animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Floating animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Gradient animation */
@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

/* Glow effect */
.glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.glow-purple {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
}

/* Smooth transitions */
.transition-all-300 {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}