/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Upload: () => (/* reexport safe */ _icons_upload_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _icons_upload_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/upload.js */ \"./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVja0NpcmNsZSxVcGxvYWQsWCE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2dFO0FBQ1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tdWx0aW1vZGFsLXJhZy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2ZjYmIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMjk0LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVja0NpcmNsZSB9IGZyb20gXCIuL2ljb25zL2NoZWNrLWNpcmNsZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVwbG9hZCB9IGZyb20gXCIuL2ljb25zL3VwbG9hZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"./pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/FileUpload.tsx":
/*!***********************************!*\
  !*** ./components/FileUpload.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FileUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"react-dropzone\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Upload,X!=!lucide-react */ \"__barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_dropzone__WEBPACK_IMPORTED_MODULE_2__]);\nreact_dropzone__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst SUPPORTED_TYPES = {\n    \"application/pdf\": \"PDF\",\n    \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": \"DOCX\",\n    \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\": \"XLSX\",\n    \"application/vnd.ms-excel\": \"XLS\",\n    \"text/csv\": \"CSV\",\n    \"text/plain\": \"TXT\",\n    \"image/jpeg\": \"JPEG\",\n    \"image/png\": \"PNG\",\n    \"image/bmp\": \"BMP\",\n    \"image/tiff\": \"TIFF\"\n};\nfunction FileUpload({ onFileUpload, uploadedFile }) {\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (acceptedFiles)=>{\n        if (acceptedFiles.length === 0) return;\n        const file = acceptedFiles[0];\n        setError(null);\n        setIsUploading(true);\n        try {\n            // Validate file size (10MB limit)\n            if (file.size > 10 * 1024 * 1024) {\n                throw new Error(\"File size must be less than 10MB\");\n            }\n            // Validate file type\n            if (!Object.keys(SUPPORTED_TYPES).includes(file.type)) {\n                throw new Error(`Unsupported file type. Supported types: ${Object.values(SUPPORTED_TYPES).join(\", \")}`);\n            }\n            onFileUpload(file);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Upload failed\");\n            onFileUpload(null);\n        } finally{\n            setIsUploading(false);\n        }\n    }, [\n        onFileUpload\n    ]);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: Object.keys(SUPPORTED_TYPES).reduce((acc, type)=>{\n            acc[type] = [];\n            return acc;\n        }, {}),\n        multiple: false,\n        disabled: isUploading\n    });\n    const removeFile = ()=>{\n        onFileUpload(null);\n        setError(null);\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    if (uploadedFile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-3 p-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.CheckCircle, {\n                        className: \"w-5 h-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-white truncate\",\n                            children: uploadedFile.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-blue-100\",\n                            children: [\n                                formatFileSize(uploadedFile.size),\n                                \" • \",\n                                SUPPORTED_TYPES[uploadedFile.type] || \"Unknown\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: removeFile,\n                    className: \"p-1 text-white/80 hover:text-white transition-colors hover:bg-white/10 rounded-lg\",\n                    title: \"Remove file\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-xs\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ...getRootProps(),\n                className: `\r\n          relative border-2 border-dashed rounded-xl p-4 text-center cursor-pointer transition-all duration-300\r\n          ${isDragActive ? \"border-white/60 bg-white/10 backdrop-blur-sm scale-105\" : \"border-white/30 hover:border-white/50 hover:bg-white/5\"}\r\n          ${isUploading ? \"opacity-50 cursor-not-allowed\" : \"\"}\r\n        `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Upload, {\n                                    className: `w-5 h-5 ${isDragActive ? \"text-white\" : \"text-white/80\"}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-white\",\n                                        children: \"Uploading...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-white/20 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/60 h-2 rounded-full animate-pulse-slow w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: isDragActive ? \"Drop here\" : \"Upload\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-100\",\n                                        children: \"PDF, DOCX, XLSX, CSV, TXT, Images\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 p-2 bg-red-500/20 backdrop-blur-sm border border-red-400/30 rounded-lg text-sm text-red-100\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/FileUpload.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @copilotkit/react-core */ \"@copilotkit/react-core\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_1__]);\n_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_1__.CopilotKit, {\n        runtimeUrl: \"/api/copilotkit\",\n        showDevConsole: \"development\" === \"development\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQ21EO0FBQ3RCO0FBRWQsU0FBU0MsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBWTtJQUM1RCxxQkFDRSw4REFBQ0gsOERBQVVBO1FBQ1RJLFlBQVc7UUFDWEMsZ0JBQWdCQyxrQkFBeUI7a0JBRXpDLDRFQUFDSjtZQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7O0FBRzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXVsdGltb2RhbC1yYWctZnJvbnRlbmQvLi9wYWdlcy9fYXBwLnRzeD8yZmJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCdcclxuaW1wb3J0IHsgQ29waWxvdEtpdCB9IGZyb20gJ0Bjb3BpbG90a2l0L3JlYWN0LWNvcmUnXHJcbmltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xyXG4gIHJldHVybiAoXHJcbiAgICA8Q29waWxvdEtpdCBcclxuICAgICAgcnVudGltZVVybD1cIi9hcGkvY29waWxvdGtpdFwiXHJcbiAgICAgIHNob3dEZXZDb25zb2xlPXtwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50J31cclxuICAgID5cclxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxyXG4gICAgPC9Db3BpbG90S2l0PlxyXG4gIClcclxufSJdLCJuYW1lcyI6WyJDb3BpbG90S2l0IiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwicnVudGltZVVybCIsInNob3dEZXZDb25zb2xlIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/react-ui */ \"@copilotkit/react-ui\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @copilotkit/react-core */ \"@copilotkit/react-core\");\n/* harmony import */ var _components_FileUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/FileUpload */ \"./components/FileUpload.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_3__, _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__, _components_FileUpload__WEBPACK_IMPORTED_MODULE_5__]);\n([_copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_3__, _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__, _components_FileUpload__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction Home() {\n    const [uploadedFile, setUploadedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionId, setSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Make file context available to Copilot\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__.useCopilotReadable)({\n        description: \"Information about uploaded file\",\n        value: uploadedFile ? {\n            name: uploadedFile.name,\n            type: uploadedFile.type,\n            size: uploadedFile.size\n        } : null\n    });\n    // Action for handling file uploads\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__.useCopilotAction)({\n        name: \"uploadDocument\",\n        description: \"Upload and analyze a document\",\n        parameters: [\n            {\n                name: \"file\",\n                type: \"object\",\n                description: \"The file to upload and analyze\"\n            }\n        ],\n        handler: async ({ file })=>{\n            setUploadedFile(file);\n            return `File ${file.name} uploaded successfully. You can now ask questions about this document.`;\n        }\n    });\n    // Action for asking CA questions\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__.useCopilotAction)({\n        name: \"askCAQuestion\",\n        description: \"Ask a question to the CA professional assistant\",\n        parameters: [\n            {\n                name: \"question\",\n                type: \"string\",\n                description: \"The question to ask\"\n            }\n        ],\n        handler: async ({ question })=>{\n            // This will be handled by the backend through the CopilotKit runtime\n            return `Processing your question: ${question}`;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"CA Professional AI Assistant\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"AI-powered assistant for CA professionals with document analysis and web search capabilities\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse-slow\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-green-400/20 to-blue-600/20 rounded-full blur-3xl animate-pulse-slow\",\n                                style: {\n                                    animationDelay: \"1s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative container mx-auto px-4 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl mb-6 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6\",\n                                        children: \"CA Professional AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Your intelligent assistant for tax, legal, compliance, and business queries. Upload documents for analysis or ask questions to get expert-level answers.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-8 mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl\",\n                                                    children: \"\\uD83E\\uDDE0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-3 text-gray-900\",\n                                                children: \"Domain Expertise\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"Specialized knowledge in CA, tax, legal, and compliance matters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-3 text-gray-900\",\n                                                children: \"Document Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"Upload PDFs, Excel files, images, and more for intelligent analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl\",\n                                                    children: \"\\uD83C\\uDF10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-3 text-gray-900\",\n                                                children: \"Live Web Search\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"Get current information and latest updates from the web\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl overflow-hidden border border-white/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xl\",\n                                                                    children: \"\\uD83E\\uDD16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                        className: \"text-xl font-semibold text-white\",\n                                                                        children: \"Chat Assistant\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 128,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-blue-100 text-sm\",\n                                                                        children: \"Powered by AI • Always ready to help\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 131,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        onFileUpload: setUploadedFile,\n                                                        uploadedFile: uploadedFile\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this),\n                                            uploadedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"\\uD83D\\uDCCE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: [\n                                                                        \"Document loaded: \",\n                                                                        uploadedFile.name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 149,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-100 text-sm\",\n                                                                    children: \"You can now ask questions about this document\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-[600px] bg-gradient-to-b from-gray-50 to-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_3__.CopilotChat, {\n                                            labels: {\n                                                title: \"CA Assistant\",\n                                                initial: \"Hello! I'm your CA professional assistant. I can help you with tax, legal, compliance, and business questions. You can also upload documents for analysis. How can I assist you today? \\uD83D\\uDE80\"\n                                            },\n                                            className: \"h-full\",\n                                            showInlineCitations: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-12 bg-gradient-to-r from-white/80 to-blue-50/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-white/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                children: \"How to Use\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Get started with your AI assistant in seconds\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white text-lg\",\n                                                                    children: \"\\uD83D\\uDCAC\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: \"Ask Questions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 ml-13\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 190,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Tax regulations and compliance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Company law and legal matters\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 195,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 198,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Business and accounting queries\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Current news and updates\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 203,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white text-lg\",\n                                                                    children: \"\\uD83D\\uDCCE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: \"Upload Documents\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 ml-13\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"PDF documents and reports\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 218,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Excel spreadsheets and CSV files\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Images with text (OCR supported)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Word documents and text files\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@copilotkit/react-core":
/*!*****************************************!*\
  !*** external "@copilotkit/react-core" ***!
  \*****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@copilotkit/react-core");;

/***/ }),

/***/ "@copilotkit/react-ui":
/*!***************************************!*\
  !*** external "@copilotkit/react-ui" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@copilotkit/react-ui");;

/***/ }),

/***/ "react-dropzone":
/*!*********************************!*\
  !*** external "react-dropzone" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-dropzone");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();