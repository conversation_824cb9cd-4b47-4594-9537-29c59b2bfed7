/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Upload: () => (/* reexport safe */ _icons_upload_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _icons_upload_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/upload.js */ \"./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVja0NpcmNsZSxVcGxvYWQsWCE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2dFO0FBQ1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tdWx0aW1vZGFsLXJhZy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2ZjYmIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMjk0LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVja0NpcmNsZSB9IGZyb20gXCIuL2ljb25zL2NoZWNrLWNpcmNsZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVwbG9hZCB9IGZyb20gXCIuL2ljb25zL3VwbG9hZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"./pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/FileUpload.tsx":
/*!***********************************!*\
  !*** ./components/FileUpload.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FileUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"react-dropzone\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Upload,X!=!lucide-react */ \"__barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_dropzone__WEBPACK_IMPORTED_MODULE_2__]);\nreact_dropzone__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst SUPPORTED_TYPES = {\n    \"application/pdf\": \"PDF\",\n    \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": \"DOCX\",\n    \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\": \"XLSX\",\n    \"application/vnd.ms-excel\": \"XLS\",\n    \"text/csv\": \"CSV\",\n    \"text/plain\": \"TXT\",\n    \"image/jpeg\": \"JPEG\",\n    \"image/png\": \"PNG\",\n    \"image/bmp\": \"BMP\",\n    \"image/tiff\": \"TIFF\"\n};\nfunction FileUpload({ onFileUpload, uploadedFile }) {\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (acceptedFiles)=>{\n        if (acceptedFiles.length === 0) return;\n        const file = acceptedFiles[0];\n        setError(null);\n        setIsUploading(true);\n        try {\n            // Validate file size (10MB limit)\n            if (file.size > 10 * 1024 * 1024) {\n                throw new Error(\"File size must be less than 10MB\");\n            }\n            // Validate file type\n            if (!Object.keys(SUPPORTED_TYPES).includes(file.type)) {\n                throw new Error(`Unsupported file type. Supported types: ${Object.values(SUPPORTED_TYPES).join(\", \")}`);\n            }\n            onFileUpload(file);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Upload failed\");\n            onFileUpload(null);\n        } finally{\n            setIsUploading(false);\n        }\n    }, [\n        onFileUpload\n    ]);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: Object.keys(SUPPORTED_TYPES).reduce((acc, type)=>{\n            acc[type] = [];\n            return acc;\n        }, {}),\n        multiple: false,\n        disabled: isUploading\n    });\n    const removeFile = ()=>{\n        onFileUpload(null);\n        setError(null);\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    if (uploadedFile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.CheckCircle, {\n                    className: \"w-5 h-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-green-900 truncate\",\n                            children: uploadedFile.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-green-600\",\n                            children: [\n                                formatFileSize(uploadedFile.size),\n                                \" • \",\n                                SUPPORTED_TYPES[uploadedFile.type] || \"Unknown\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: removeFile,\n                    className: \"p-1 text-green-600 hover:text-green-800 transition-colors\",\n                    title: \"Remove file\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ...getRootProps(),\n                className: `\r\n          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors\r\n          ${isDragActive ? \"border-blue-400 bg-blue-50\" : \"border-gray-300 hover:border-gray-400\"}\r\n          ${isUploading ? \"opacity-50 cursor-not-allowed\" : \"\"}\r\n        `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Upload, {\n                                className: `w-8 h-8 mx-auto ${isDragActive ? \"text-blue-500\" : \"text-gray-400\"}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Uploading...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-600 h-2 rounded-full animate-pulse-slow w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: isDragActive ? \"Drop your file here\" : \"Upload a document\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"PDF, DOCX, XLSX, CSV, TXT, or images (max 10MB)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/FileUpload.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @copilotkit/react-core */ \"@copilotkit/react-core\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_1__]);\n_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_1__.CopilotKit, {\n        runtimeUrl: \"/api/copilotkit\",\n        showDevConsole: \"development\" === \"development\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQ21EO0FBQ3RCO0FBRWQsU0FBU0MsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBWTtJQUM1RCxxQkFDRSw4REFBQ0gsOERBQVVBO1FBQ1RJLFlBQVc7UUFDWEMsZ0JBQWdCQyxrQkFBeUI7a0JBRXpDLDRFQUFDSjtZQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7O0FBRzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXVsdGltb2RhbC1yYWctZnJvbnRlbmQvLi9wYWdlcy9fYXBwLnRzeD8yZmJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCdcclxuaW1wb3J0IHsgQ29waWxvdEtpdCB9IGZyb20gJ0Bjb3BpbG90a2l0L3JlYWN0LWNvcmUnXHJcbmltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xyXG4gIHJldHVybiAoXHJcbiAgICA8Q29waWxvdEtpdCBcclxuICAgICAgcnVudGltZVVybD1cIi9hcGkvY29waWxvdGtpdFwiXHJcbiAgICAgIHNob3dEZXZDb25zb2xlPXtwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50J31cclxuICAgID5cclxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxyXG4gICAgPC9Db3BpbG90S2l0PlxyXG4gIClcclxufSJdLCJuYW1lcyI6WyJDb3BpbG90S2l0IiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwicnVudGltZVVybCIsInNob3dEZXZDb25zb2xlIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/react-ui */ \"@copilotkit/react-ui\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @copilotkit/react-core */ \"@copilotkit/react-core\");\n/* harmony import */ var _components_FileUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/FileUpload */ \"./components/FileUpload.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_3__, _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__, _components_FileUpload__WEBPACK_IMPORTED_MODULE_5__]);\n([_copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_3__, _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__, _components_FileUpload__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction Home() {\n    const [uploadedFile, setUploadedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionId, setSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Make file context available to Copilot\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__.useCopilotReadable)({\n        description: \"Information about uploaded file\",\n        value: uploadedFile ? {\n            name: uploadedFile.name,\n            type: uploadedFile.type,\n            size: uploadedFile.size\n        } : null\n    });\n    // Action for handling file uploads\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__.useCopilotAction)({\n        name: \"uploadDocument\",\n        description: \"Upload and analyze a document\",\n        parameters: [\n            {\n                name: \"file\",\n                type: \"object\",\n                description: \"The file to upload and analyze\"\n            }\n        ],\n        handler: async ({ file })=>{\n            setUploadedFile(file);\n            return `File ${file.name} uploaded successfully. You can now ask questions about this document.`;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"CA Professional AI Assistant\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"AI-powered assistant for CA professionals with document analysis and web search capabilities\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"\\uD83D\\uDE80 CA Professional AI Assistant\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Your intelligent assistant for tax, legal, compliance, and business queries. Upload documents for analysis or ask questions to get expert-level answers.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-6 shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl mb-3\",\n                                            children: \"\\uD83E\\uDDE0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Domain Expertise\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Specialized knowledge in CA, tax, legal, and compliance matters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-6 shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl mb-3\",\n                                            children: \"\\uD83D\\uDCC4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Document Analysis\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Upload PDFs, Excel files, images, and more for intelligent analysis\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-6 shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl mb-3\",\n                                            children: \"\\uD83C\\uDF10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Live Web Search\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Get current information and latest updates from the web\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-200 p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                    children: \"Chat Assistant\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    onFileUpload: setUploadedFile,\n                                                    uploadedFile: uploadedFile\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        uploadedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-800\",\n                                                    children: [\n                                                        \"\\uD83D\\uDCCE Document loaded: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: uploadedFile.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: \"You can now ask questions about this document\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-96\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_3__.CopilotChat, {\n                                        labels: {\n                                            title: \"CA Assistant\",\n                                            initial: \"Hello! I'm your CA professional assistant. I can help you with tax, legal, compliance, and business questions. You can also upload documents for analysis. How can I assist you today?\"\n                                        },\n                                        className: \"h-full\",\n                                        showInlineCitations: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 bg-white rounded-lg p-6 shadow-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"How to Use\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"\\uD83D\\uDCAC Ask Questions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Tax regulations and compliance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Company law and legal matters\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Business and accounting queries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Current news and updates\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                    children: \"\\uD83D\\uDCCE Upload Documents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-gray-600 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• PDF documents and reports\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Excel spreadsheets and CSV files\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Images with text (OCR supported)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Word documents and text files\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@copilotkit/react-core":
/*!*****************************************!*\
  !*** external "@copilotkit/react-core" ***!
  \*****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@copilotkit/react-core");;

/***/ }),

/***/ "@copilotkit/react-ui":
/*!***************************************!*\
  !*** external "@copilotkit/react-ui" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@copilotkit/react-ui");;

/***/ }),

/***/ "react-dropzone":
/*!*********************************!*\
  !*** external "react-dropzone" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-dropzone");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();