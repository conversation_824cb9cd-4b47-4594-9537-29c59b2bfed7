/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=Bot,Paperclip,Send,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bot,Paperclip,Send,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bot: () => (/* reexport safe */ _icons_bot_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Paperclip: () => (/* reexport safe */ _icons_paperclip_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Send: () => (/* reexport safe */ _icons_send_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bot_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bot.js */ \"./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _icons_paperclip_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/paperclip.js */ \"./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _icons_send_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/send.js */ \"./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Cb3QsUGFwZXJjbGlwLFNlbmQsVXNlciE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDK0M7QUFDWTtBQUNWIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXVsdGltb2RhbC1yYWctZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9mNzliIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjI5NC4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqLyBcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQm90IH0gZnJvbSBcIi4vaWNvbnMvYm90LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFwZXJjbGlwIH0gZnJvbSBcIi4vaWNvbnMvcGFwZXJjbGlwLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2VuZCB9IGZyb20gXCIuL2ljb25zL3NlbmQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyIH0gZnJvbSBcIi4vaWNvbnMvdXNlci5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Bot,Paperclip,Send,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Upload: () => (/* reexport safe */ _icons_upload_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _icons_upload_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/upload.js */ \"./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVja0NpcmNsZSxVcGxvYWQsWCE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2dFO0FBQ1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tdWx0aW1vZGFsLXJhZy1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2ZjYmIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMjk0LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVja0NpcmNsZSB9IGZyb20gXCIuL2ljb25zL2NoZWNrLWNpcmNsZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVwbG9hZCB9IGZyb20gXCIuL2ljb25zL3VwbG9hZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"./pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/CustomChatInterface.tsx":
/*!********************************************!*\
  !*** ./components/CustomChatInterface.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Paperclip,Send,User!=!lucide-react */ \"__barrel_optimize__?names=Bot,Paperclip,Send,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction CustomChatInterface({ uploadedFile }) {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"Hello! I'm your CA professional assistant. I can help you with tax, legal, compliance, and business questions. You can also upload documents for analysis. How can I assist you today? \\uD83D\\uDE80\",\n            timestamp: new Date()\n        }\n    ]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleSend = async ()=>{\n        if (!inputValue.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: inputValue.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue(\"\");\n        setIsLoading(true);\n        try {\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    session_id: \"custom-chat-session\",\n                    file_context: uploadedFile ? {\n                        name: uploadedFile.name,\n                        type: uploadedFile.type,\n                        size: uploadedFile.size\n                    } : null\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: data.response || \"I apologize, but I encountered an error. Please try again.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Chat error:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"I apologize, but I encountered an error connecting to the backend. Please ensure the backend server is running and try again.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const formatTime = (date)=>{\n        // Use a consistent format to avoid hydration errors\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-[600px] bg-gradient-to-br from-slate-50/50 via-blue-50/30 to-purple-50/50 relative overflow-hidden rounded-3xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-10 right-10 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-2xl animate-pulse-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-10 left-10 w-24 h-24 bg-gradient-to-br from-green-400/10 to-blue-600/10 rounded-full blur-2xl animate-pulse-slow\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-full flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6 overflow-y-auto space-y-4 scrollbar-thin scrollbar-thumb-blue-200 scrollbar-track-transparent\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex items-start space-x-3 ${message.role === \"user\" ? \"flex-row-reverse space-x-reverse\" : \"\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-10 h-10 rounded-xl flex items-center justify-center shadow-lg flex-shrink-0 ${message.role === \"assistant\" ? \"bg-gradient-to-br from-blue-500 to-purple-600\" : \"bg-gradient-to-br from-green-500 to-blue-500\"}`,\n                                            children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Bot, {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.User, {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `flex-1 max-w-[80%] ${message.role === \"user\" ? \"flex justify-end\" : \"\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `rounded-2xl p-4 shadow-lg border backdrop-blur-sm ${message.role === \"assistant\" ? \"bg-white/80 border-white/20 text-gray-800\" : \"bg-gradient-to-r from-blue-600 to-purple-600 border-blue-500/20 text-white\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"leading-relaxed whitespace-pre-wrap\",\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `text-xs mt-2 ${message.role === \"assistant\" ? \"text-gray-500\" : \"text-blue-100\"}`,\n                                                        children: isClient ? formatTime(message.timestamp) : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, message.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Bot, {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-purple-500 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.1s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.2s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-white/20 backdrop-blur-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-2xl border border-white/30 shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: textareaRef,\n                                        value: inputValue,\n                                        onChange: (e)=>setInputValue(e.target.value),\n                                        onKeyPress: handleKeyPress,\n                                        placeholder: \"Ask me anything about tax, legal, or business matters...\",\n                                        className: \"w-full p-4 bg-transparent border-none outline-none resize-none text-gray-800 placeholder-gray-500 min-h-[60px] max-h-32\",\n                                        disabled: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 pt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-500 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50\",\n                                                        title: \"Attach file\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Paperclip, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    uploadedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-green-600 bg-green-50 px-2 py-1 rounded-lg\",\n                                                        children: [\n                                                            \"\\uD83D\\uDCCE \",\n                                                            uploadedFile.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: \"Shift + Enter for new line\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSend,\n                                                disabled: !inputValue.trim() || isLoading,\n                                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-xl hover:shadow-lg transition-all duration-300 hover:scale-105 font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Send, {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Send\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n// Export as dynamic component to avoid hydration issues\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>Promise.resolve(CustomChatInterface), {\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-[600px] bg-gradient-to-br from-slate-50/50 via-blue-50/30 to-purple-50/50 relative overflow-hidden rounded-3xl flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 animate-pulse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Paperclip_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Bot, {\n                            className: \"w-6 h-6 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading chat interface...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\CustomChatInterface.tsx\",\n            lineNumber: 230,\n            columnNumber: 5\n        }, undefined)\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/CustomChatInterface.tsx\n");

/***/ }),

/***/ "./components/FileUpload.tsx":
/*!***********************************!*\
  !*** ./components/FileUpload.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FileUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"react-dropzone\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Upload,X!=!lucide-react */ \"__barrel_optimize__?names=CheckCircle,Upload,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_dropzone__WEBPACK_IMPORTED_MODULE_2__]);\nreact_dropzone__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst SUPPORTED_TYPES = {\n    \"application/pdf\": \"PDF\",\n    \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": \"DOCX\",\n    \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\": \"XLSX\",\n    \"application/vnd.ms-excel\": \"XLS\",\n    \"text/csv\": \"CSV\",\n    \"text/plain\": \"TXT\",\n    \"image/jpeg\": \"JPEG\",\n    \"image/png\": \"PNG\",\n    \"image/bmp\": \"BMP\",\n    \"image/tiff\": \"TIFF\"\n};\nfunction FileUpload({ onFileUpload, uploadedFile }) {\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (acceptedFiles)=>{\n        if (acceptedFiles.length === 0) return;\n        const file = acceptedFiles[0];\n        setError(null);\n        setIsUploading(true);\n        try {\n            // Validate file size (10MB limit)\n            if (file.size > 10 * 1024 * 1024) {\n                throw new Error(\"File size must be less than 10MB\");\n            }\n            // Validate file type\n            if (!Object.keys(SUPPORTED_TYPES).includes(file.type)) {\n                throw new Error(`Unsupported file type. Supported types: ${Object.values(SUPPORTED_TYPES).join(\", \")}`);\n            }\n            onFileUpload(file);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Upload failed\");\n            onFileUpload(null);\n        } finally{\n            setIsUploading(false);\n        }\n    }, [\n        onFileUpload\n    ]);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: Object.keys(SUPPORTED_TYPES).reduce((acc, type)=>{\n            acc[type] = [];\n            return acc;\n        }, {}),\n        multiple: false,\n        disabled: isUploading\n    });\n    const removeFile = ()=>{\n        onFileUpload(null);\n        setError(null);\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    if (uploadedFile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-3 p-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.CheckCircle, {\n                        className: \"w-5 h-5 text-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-white truncate\",\n                            children: uploadedFile.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-blue-100\",\n                            children: [\n                                formatFileSize(uploadedFile.size),\n                                \" • \",\n                                SUPPORTED_TYPES[uploadedFile.type] || \"Unknown\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: removeFile,\n                    className: \"p-1 text-white/80 hover:text-white transition-colors hover:bg-white/10 rounded-lg\",\n                    title: \"Remove file\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-xs\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ...getRootProps(),\n                className: `\r\n          relative border-2 border-dashed rounded-xl p-4 text-center cursor-pointer transition-all duration-300\r\n          ${isDragActive ? \"border-white/60 bg-white/10 backdrop-blur-sm scale-105\" : \"border-white/30 hover:border-white/50 hover:bg-white/5\"}\r\n          ${isUploading ? \"opacity-50 cursor-not-allowed\" : \"\"}\r\n        `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Upload, {\n                                    className: `w-5 h-5 ${isDragActive ? \"text-white\" : \"text-white/80\"}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-white\",\n                                        children: \"Uploading...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-white/20 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/60 h-2 rounded-full animate-pulse-slow w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: isDragActive ? \"Drop here\" : \"Upload\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-100\",\n                                        children: \"PDF, DOCX, XLSX, CSV, TXT, Images\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 p-2 bg-red-500/20 backdrop-blur-sm border border-red-400/30 rounded-lg text-sm text-red-100\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\components\\\\FileUpload.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/FileUpload.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDNkI7QUFFZCxTQUFTQSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzVELHFCQUFPLDhEQUFDRDtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUNqQyIsInNvdXJjZXMiOlsid2VicGFjazovL211bHRpbW9kYWwtcmFnLWZyb250ZW5kLy4vcGFnZXMvX2FwcC50c3g/MmZiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnXHJcbmltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xyXG4gIHJldHVybiA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XHJcbn0iXSwibmFtZXMiOlsiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_FileUpload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FileUpload */ \"./components/FileUpload.tsx\");\n/* harmony import */ var _components_CustomChatInterface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CustomChatInterface */ \"./components/CustomChatInterface.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_FileUpload__WEBPACK_IMPORTED_MODULE_3__]);\n_components_FileUpload__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction Home() {\n    const [uploadedFile, setUploadedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"CA Professional AI Assistant\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"AI-powered assistant for CA professionals with document analysis and web search capabilities\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse-slow\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-green-400/20 to-blue-600/20 rounded-full blur-3xl animate-pulse-slow\",\n                                style: {\n                                    animationDelay: \"1s\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative container mx-auto px-4 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl mb-6 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6\",\n                                        children: \"CA Professional AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"Your intelligent assistant for tax, legal, compliance, and business queries. Upload documents for analysis or ask questions to get expert-level answers.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-8 mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl\",\n                                                    children: \"\\uD83E\\uDDE0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-3 text-gray-900\",\n                                                children: \"Domain Expertise\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"Specialized knowledge in CA, tax, legal, and compliance matters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-3 text-gray-900\",\n                                                children: \"Document Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"Upload PDFs, Excel files, images, and more for intelligent analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl\",\n                                                    children: \"\\uD83C\\uDF10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-3 text-gray-900\",\n                                                children: \"Live Web Search\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"Get current information and latest updates from the web\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl overflow-hidden border border-white/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xl\",\n                                                                    children: \"\\uD83E\\uDD16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 79,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 78,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                        className: \"text-xl font-semibold text-white\",\n                                                                        children: \"Chat Assistant\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 82,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-blue-100 text-sm\",\n                                                                        children: \"Powered by AI • Always ready to help\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 85,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 81,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        onFileUpload: setUploadedFile,\n                                                        uploadedFile: uploadedFile\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, this),\n                                            uploadedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"\\uD83D\\uDCCE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 100,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: [\n                                                                        \"Document loaded: \",\n                                                                        uploadedFile.name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 103,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-100 text-sm\",\n                                                                    children: \"You can now ask questions about this document\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 106,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomChatInterface__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        uploadedFile: uploadedFile\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-12 bg-gradient-to-r from-white/80 to-blue-50/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-white/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                children: \"How to Use\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Get started with your AI assistant in seconds\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white text-lg\",\n                                                                    children: \"\\uD83D\\uDCAC\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 129,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: \"Ask Questions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 ml-13\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 135,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Tax regulations and compliance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 136,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 139,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Company law and legal matters\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 140,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 143,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Business and accounting queries\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 144,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 147,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Current news and updates\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 148,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white text-lg\",\n                                                                    children: \"\\uD83D\\uDCCE\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: \"Upload Documents\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 ml-13\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 162,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"PDF documents and reports\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 163,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Excel spreadsheets and CSV files\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 167,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Images with text (OCR supported)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 p-3 bg-white/60 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 174,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: \"Word documents and text files\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                        lineNumber: 175,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\THE_DEGREE_SHIT\\\\SAURABH_SIR\\\\ROVODEV MULTIMODAL RAG\\\\frontend\\\\pages\\\\index.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react-dropzone":
/*!*********************************!*\
  !*** external "react-dropzone" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-dropzone");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();