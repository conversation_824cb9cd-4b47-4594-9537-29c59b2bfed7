"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\r\\n  html {\\r\\n    font-family: system-ui, sans-serif;\\r\\n  }\\r\\n.container {\\n  width: 100%;\\n}\\r\\n@media (min-width: 640px) {\\n\\n  .container {\\n    max-width: 640px;\\n  }\\n}\\r\\n@media (min-width: 768px) {\\n\\n  .container {\\n    max-width: 768px;\\n  }\\n}\\r\\n@media (min-width: 1024px) {\\n\\n  .container {\\n    max-width: 1024px;\\n  }\\n}\\r\\n@media (min-width: 1280px) {\\n\\n  .container {\\n    max-width: 1280px;\\n  }\\n}\\r\\n@media (min-width: 1536px) {\\n\\n  .container {\\n    max-width: 1536px;\\n  }\\n}\\r\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\r\\n.absolute {\\n  position: absolute;\\n}\\r\\n.relative {\\n  position: relative;\\n}\\r\\n.inset-0 {\\n  inset: 0px;\\n}\\r\\n.-bottom-40 {\\n  bottom: -10rem;\\n}\\r\\n.-left-40 {\\n  left: -10rem;\\n}\\r\\n.-right-40 {\\n  right: -10rem;\\n}\\r\\n.-top-40 {\\n  top: -10rem;\\n}\\r\\n.bottom-10 {\\n  bottom: 2.5rem;\\n}\\r\\n.left-10 {\\n  left: 2.5rem;\\n}\\r\\n.right-10 {\\n  right: 2.5rem;\\n}\\r\\n.top-10 {\\n  top: 2.5rem;\\n}\\r\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-12 {\\n  margin-bottom: 3rem;\\n}\\r\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\r\\n.mt-12 {\\n  margin-top: 3rem;\\n}\\r\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\r\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\r\\n.flex {\\n  display: flex;\\n}\\r\\n.inline-flex {\\n  display: inline-flex;\\n}\\r\\n.grid {\\n  display: grid;\\n}\\r\\n.h-10 {\\n  height: 2.5rem;\\n}\\r\\n.h-12 {\\n  height: 3rem;\\n}\\r\\n.h-16 {\\n  height: 4rem;\\n}\\r\\n.h-2 {\\n  height: 0.5rem;\\n}\\r\\n.h-24 {\\n  height: 6rem;\\n}\\r\\n.h-32 {\\n  height: 8rem;\\n}\\r\\n.h-4 {\\n  height: 1rem;\\n}\\r\\n.h-5 {\\n  height: 1.25rem;\\n}\\r\\n.h-8 {\\n  height: 2rem;\\n}\\r\\n.h-80 {\\n  height: 20rem;\\n}\\r\\n.h-\\\\[600px\\\\] {\\n  height: 600px;\\n}\\r\\n.h-full {\\n  height: 100%;\\n}\\r\\n.max-h-32 {\\n  max-height: 8rem;\\n}\\r\\n.min-h-\\\\[60px\\\\] {\\n  min-height: 60px;\\n}\\r\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\r\\n.w-1\\\\/2 {\\n  width: 50%;\\n}\\r\\n.w-10 {\\n  width: 2.5rem;\\n}\\r\\n.w-12 {\\n  width: 3rem;\\n}\\r\\n.w-16 {\\n  width: 4rem;\\n}\\r\\n.w-2 {\\n  width: 0.5rem;\\n}\\r\\n.w-24 {\\n  width: 6rem;\\n}\\r\\n.w-32 {\\n  width: 8rem;\\n}\\r\\n.w-4 {\\n  width: 1rem;\\n}\\r\\n.w-5 {\\n  width: 1.25rem;\\n}\\r\\n.w-8 {\\n  width: 2rem;\\n}\\r\\n.w-80 {\\n  width: 20rem;\\n}\\r\\n.w-full {\\n  width: 100%;\\n}\\r\\n.min-w-0 {\\n  min-width: 0px;\\n}\\r\\n.max-w-3xl {\\n  max-width: 48rem;\\n}\\r\\n.max-w-\\\\[80\\\\%\\\\] {\\n  max-width: 80%;\\n}\\r\\n.max-w-xs {\\n  max-width: 20rem;\\n}\\r\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\r\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\r\\n.scale-105 {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n@keyframes bounce {\\n\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8,0,1,1);\\n  }\\n\\n  50% {\\n    transform: none;\\n    animation-timing-function: cubic-bezier(0,0,0.2,1);\\n  }\\n}\\r\\n.animate-bounce {\\n  animation: bounce 1s infinite;\\n}\\r\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\r\\n.animate-pulse-slow {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\r\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\r\\n.resize-none {\\n  resize: none;\\n}\\r\\n.list-inside {\\n  list-style-position: inside;\\n}\\r\\n.list-disc {\\n  list-style-type: disc;\\n}\\r\\n.flex-row-reverse {\\n  flex-direction: row-reverse;\\n}\\r\\n.flex-col {\\n  flex-direction: column;\\n}\\r\\n.items-start {\\n  align-items: flex-start;\\n}\\r\\n.items-center {\\n  align-items: center;\\n}\\r\\n.justify-start {\\n  justify-content: flex-start;\\n}\\r\\n.justify-end {\\n  justify-content: flex-end;\\n}\\r\\n.justify-center {\\n  justify-content: center;\\n}\\r\\n.justify-between {\\n  justify-content: space-between;\\n}\\r\\n.gap-8 {\\n  gap: 2rem;\\n}\\r\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-x-reverse > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 1;\\n}\\r\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\r\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\r\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\r\\n.whitespace-pre-wrap {\\n  white-space: pre-wrap;\\n}\\r\\n.break-words {\\n  overflow-wrap: break-word;\\n}\\r\\n.rounded-2xl {\\n  border-radius: 1rem;\\n}\\r\\n.rounded-3xl {\\n  border-radius: 1.5rem;\\n}\\r\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\r\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\r\\n.border {\\n  border-width: 1px;\\n}\\r\\n.border-2 {\\n  border-width: 2px;\\n}\\r\\n.border-t {\\n  border-top-width: 1px;\\n}\\r\\n.border-dashed {\\n  border-style: dashed;\\n}\\r\\n.border-none {\\n  border-style: none;\\n}\\r\\n.border-blue-500\\\\/20 {\\n  border-color: rgb(59 130 246 / 0.2);\\n}\\r\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-red-400\\\\/30 {\\n  border-color: rgb(248 113 113 / 0.3);\\n}\\r\\n.border-white\\\\/20 {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n.border-white\\\\/30 {\\n  border-color: rgb(255 255 255 / 0.3);\\n}\\r\\n.border-white\\\\/60 {\\n  border-color: rgb(255 255 255 / 0.6);\\n}\\r\\n.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-orange-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-purple-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-red-500\\\\/20 {\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\r\\n.bg-transparent {\\n  background-color: transparent;\\n}\\r\\n.bg-white\\\\/10 {\\n  background-color: rgb(255 255 255 / 0.1);\\n}\\r\\n.bg-white\\\\/20 {\\n  background-color: rgb(255 255 255 / 0.2);\\n}\\r\\n.bg-white\\\\/60 {\\n  background-color: rgb(255 255 255 / 0.6);\\n}\\r\\n.bg-white\\\\/80 {\\n  background-color: rgb(255 255 255 / 0.8);\\n}\\r\\n.bg-white\\\\/90 {\\n  background-color: rgb(255 255 255 / 0.9);\\n}\\r\\n.bg-gradient-to-br {\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\r\\n.from-blue-400\\\\/10 {\\n  --tw-gradient-from: rgb(96 165 250 / 0.1) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-400\\\\/20 {\\n  --tw-gradient-from: rgb(96 165 250 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-500 {\\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-blue-600 {\\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-900 {\\n  --tw-gradient-from: #111827 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-green-400\\\\/10 {\\n  --tw-gradient-from: rgb(74 222 128 / 0.1) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-green-400\\\\/20 {\\n  --tw-gradient-from: rgb(74 222 128 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-green-500 {\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-purple-500 {\\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-slate-50 {\\n  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-slate-50\\\\/50 {\\n  --tw-gradient-from: rgb(248 250 252 / 0.5) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-white\\\\/80 {\\n  --tw-gradient-from: rgb(255 255 255 / 0.8) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.via-blue-50 {\\n  --tw-gradient-to: rgb(239 246 255 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #eff6ff var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.via-blue-50\\\\/30 {\\n  --tw-gradient-to: rgb(239 246 255 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(239 246 255 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.via-blue-800 {\\n  --tw-gradient-to: rgb(30 64 175 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #1e40af var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.to-blue-50\\\\/80 {\\n  --tw-gradient-to: rgb(239 246 255 / 0.8) var(--tw-gradient-to-position);\\n}\\r\\n.to-blue-500 {\\n  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);\\n}\\r\\n.to-blue-600\\\\/10 {\\n  --tw-gradient-to: rgb(37 99 235 / 0.1) var(--tw-gradient-to-position);\\n}\\r\\n.to-blue-600\\\\/20 {\\n  --tw-gradient-to: rgb(37 99 235 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-cyan-500 {\\n  --tw-gradient-to: #06b6d4 var(--tw-gradient-to-position);\\n}\\r\\n.to-emerald-500 {\\n  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);\\n}\\r\\n.to-indigo-100 {\\n  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);\\n}\\r\\n.to-pink-500 {\\n  --tw-gradient-to: #ec4899 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-50\\\\/50 {\\n  --tw-gradient-to: rgb(250 245 255 / 0.5) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500 {\\n  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-600 {\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-600\\\\/10 {\\n  --tw-gradient-to: rgb(147 51 234 / 0.1) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-600\\\\/20 {\\n  --tw-gradient-to: rgb(147 51 234 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-800 {\\n  --tw-gradient-to: #6b21a8 var(--tw-gradient-to-position);\\n}\\r\\n.bg-clip-text {\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n}\\r\\n.p-1 {\\n  padding: 0.25rem;\\n}\\r\\n.p-2 {\\n  padding: 0.5rem;\\n}\\r\\n.p-3 {\\n  padding: 0.75rem;\\n}\\r\\n.p-4 {\\n  padding: 1rem;\\n}\\r\\n.p-6 {\\n  padding: 1.5rem;\\n}\\r\\n.p-8 {\\n  padding: 2rem;\\n}\\r\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\r\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\r\\n.pt-0 {\\n  padding-top: 0px;\\n}\\r\\n.pt-3 {\\n  padding-top: 0.75rem;\\n}\\r\\n.text-center {\\n  text-align: center;\\n}\\r\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-5xl {\\n  font-size: 3rem;\\n  line-height: 1;\\n}\\r\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.font-bold {\\n  font-weight: 700;\\n}\\r\\n.font-medium {\\n  font-weight: 500;\\n}\\r\\n.font-semibold {\\n  font-weight: 600;\\n}\\r\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\r\\n.text-blue-100 {\\n  --tw-text-opacity: 1;\\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-blue-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-purple-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-red-100 {\\n  --tw-text-opacity: 1;\\n  color: rgb(254 226 226 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-transparent {\\n  color: transparent;\\n}\\r\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-white\\\\/80 {\\n  color: rgb(255 255 255 / 0.8);\\n}\\r\\n.placeholder-gray-500::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\r\\n.placeholder-gray-500::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\r\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\r\\n.shadow-2xl {\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n.blur-2xl {\\n  --tw-blur: blur(40px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.blur-3xl {\\n  --tw-blur: blur(64px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-shadow {\\n  transition-property: box-shadow;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n.overflow-y-auto::-webkit-scrollbar,\\r\\n.scrollbar-thin::-webkit-scrollbar {\\r\\n  width: 6px;\\r\\n}\\r\\n\\r\\n.overflow-y-auto::-webkit-scrollbar-track,\\r\\n.scrollbar-thin::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.overflow-y-auto::-webkit-scrollbar-thumb,\\r\\n.scrollbar-thin::-webkit-scrollbar-thumb {\\r\\n  background: rgba(59, 130, 246, 0.3);\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.overflow-y-auto::-webkit-scrollbar-thumb:hover,\\r\\n.scrollbar-thin::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(59, 130, 246, 0.5);\\r\\n}\\r\\n\\r\\n.scrollbar-thumb-blue-200::-webkit-scrollbar-thumb {\\r\\n  background: rgba(191, 219, 254, 0.6);\\r\\n}\\r\\n\\r\\n.scrollbar-track-transparent::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\n/* Animation for typing indicator */\\r\\n@keyframes bounce {\\r\\n  0%, 80%, 100% {\\r\\n    transform: scale(0);\\r\\n  }\\r\\n  40% {\\r\\n    transform: scale(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-bounce {\\r\\n  animation: bounce 1.4s infinite ease-in-out both;\\r\\n}\\r\\n\\r\\n/* File upload drag and drop styles */\\r\\n.drag-active {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n/* Loading spinner */\\r\\n@keyframes spin {\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-spin {\\r\\n  animation: spin 1s linear infinite;\\r\\n}\\r\\n\\r\\n/* Pulse animation for loading states */\\r\\n@keyframes pulse {\\r\\n  0%, 100% {\\r\\n    opacity: 1;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 0.5;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-pulse-slow {\\r\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\r\\n}\\r\\n\\r\\n/* Floating animation */\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-10px);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 6s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n/* Gradient animation */\\r\\n@keyframes gradient {\\r\\n  0%, 100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-gradient {\\r\\n  background-size: 200% 200%;\\r\\n  animation: gradient 3s ease infinite;\\r\\n}\\r\\n\\r\\n/* Glow effect */\\r\\n.glow {\\r\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\r\\n}\\r\\n\\r\\n.glow-purple {\\r\\n  box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);\\r\\n}\\r\\n\\r\\n/* Smooth transitions */\\r\\n.transition-all-300 {\\r\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n}\\r\\n.hover\\\\:-translate-y-1:hover {\\n  --tw-translate-y: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.hover\\\\:scale-105:hover {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.hover\\\\:border-white\\\\/50:hover {\\n  border-color: rgb(255 255 255 / 0.5);\\n}\\r\\n.hover\\\\:bg-blue-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\r\\n.hover\\\\:bg-blue-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\\n}\\r\\n.hover\\\\:bg-white\\\\/10:hover {\\n  background-color: rgb(255 255 255 / 0.1);\\n}\\r\\n.hover\\\\:bg-white\\\\/5:hover {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n.hover\\\\:text-blue-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\r\\n.hover\\\\:text-white:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n.hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\r\\n.hover\\\\:shadow-lg:hover {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.hover\\\\:shadow-xl:hover {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.focus\\\\:border-transparent:focus {\\n  border-color: transparent;\\n}\\r\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n.focus\\\\:ring-blue-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\r\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\r\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\r\\n.disabled\\\\:hover\\\\:scale-100:hover:disabled {\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.group:hover .group-hover\\\\:scale-110 {\\n  --tw-scale-x: 1.1;\\n  --tw-scale-y: 1.1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n@media (min-width: 768px) {\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;EAAd;IAAA,kCAAc;EAAA;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;;EAAA;IAAA,2BAAmB;IAAnB;EAAmB;;EAAnB;IAAA,eAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,sEAAmB;EAAnB;AAAmB;AAAnB;EAAA,sEAAmB;EAAnB;AAAmB;AAAnB;EAAA,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;;AA8BnB,qBAAqB;AACrB;;EAEE,UAAU;AACZ;;AAEA;;EAEE,uBAAuB;EACvB,kBAAkB;AACpB;;AAEA;;EAEE,mCAAmC;EACnC,kBAAkB;AACpB;;AAEA;;EAEE,mCAAmC;AACrC;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,uBAAuB;AACzB;;AAEA,mCAAmC;AACnC;EACE;IACE,mBAAmB;EACrB;EACA;IACE,mBAAmB;EACrB;AACF;;AAEA;EACE,gDAAgD;AAClD;;AAEA,qCAAqC;AAEnC;EAAA,sBAAiC;EAAjC,2DAAiC;EAAjC,kBAAiC;EAAjC;AAAiC;;AAGnC,oBAAoB;AACpB;EACE;IACE,yBAAyB;EAC3B;AACF;;AAEA;EACE,kCAAkC;AACpC;;AAEA,uCAAuC;AACvC;EACE;IACE,UAAU;EACZ;EACA;IACE,YAAY;EACd;AACF;;AAEA;EACE,yDAAyD;AAC3D;;AAEA,uBAAuB;AACvB;EACE;IACE,0BAA0B;EAC5B;EACA;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE,wCAAwC;AAC1C;;AAEA,uBAAuB;AACvB;EACE;IACE,2BAA2B;EAC7B;EACA;IACE,6BAA6B;EAC/B;AACF;;AAEA;EACE,0BAA0B;EAC1B,oCAAoC;AACtC;;AAEA,gBAAgB;AAChB;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA,uBAAuB;AACvB;EACE,iDAAiD;AACnD;AApJA;EAAA,0BAoJC;EApJD;AAoJC;AApJD;EAAA,kBAoJC;EApJD,kBAoJC;EApJD;AAoJC;AApJD;EAAA;AAoJC;AApJD;EAAA,kBAoJC;EApJD;AAoJC;AApJD;EAAA,kBAoJC;EApJD;AAoJC;AApJD;EAAA;AAoJC;AApJD;EAAA;AAoJC;AApJD;EAAA,oBAoJC;EApJD;AAoJC;AApJD;EAAA,oBAoJC;EApJD;AAoJC;AApJD;EAAA;AAoJC;AApJD;EAAA,+EAoJC;EApJD,mGAoJC;EApJD;AAoJC;AApJD;EAAA,gFAoJC;EApJD,oGAoJC;EApJD;AAoJC;AApJD;EAAA;AAoJC;AApJD;EAAA,2GAoJC;EApJD,yGAoJC;EApJD;AAoJC;AApJD;EAAA,oBAoJC;EApJD;AAoJC;AApJD;EAAA;AAoJC;AApJD;EAAA;AAoJC;AApJD;EAAA,eAoJC;EApJD,eAoJC;EApJD;AAoJC;AApJD;EAAA,iBAoJC;EApJD,iBAoJC;EApJD;AAoJC;AApJD;;EAAA;IAAA;EAoJC;;EApJD;IAAA;EAoJC;AAAA\",\"sourcesContent\":[\"@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n\\r\\n@layer base {\\r\\n  html {\\r\\n    font-family: system-ui, sans-serif;\\r\\n  }\\r\\n}\\r\\n\\r\\n@layer components {\\r\\n  .chat-message {\\r\\n    @apply max-w-3xl rounded-2xl p-4 shadow-sm;\\r\\n  }\\r\\n\\r\\n  .chat-message-user {\\r\\n    @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white ml-auto;\\r\\n  }\\r\\n\\r\\n  .chat-message-assistant {\\r\\n    @apply bg-white/80 backdrop-blur-sm text-gray-900 mr-auto border border-gray-200/50;\\r\\n  }\\r\\n\\r\\n  .glass-effect {\\r\\n    @apply bg-white/10 backdrop-blur-md border border-white/20;\\r\\n  }\\r\\n\\r\\n  .gradient-text {\\r\\n    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n.overflow-y-auto::-webkit-scrollbar,\\r\\n.scrollbar-thin::-webkit-scrollbar {\\r\\n  width: 6px;\\r\\n}\\r\\n\\r\\n.overflow-y-auto::-webkit-scrollbar-track,\\r\\n.scrollbar-thin::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.overflow-y-auto::-webkit-scrollbar-thumb,\\r\\n.scrollbar-thin::-webkit-scrollbar-thumb {\\r\\n  background: rgba(59, 130, 246, 0.3);\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.overflow-y-auto::-webkit-scrollbar-thumb:hover,\\r\\n.scrollbar-thin::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(59, 130, 246, 0.5);\\r\\n}\\r\\n\\r\\n.scrollbar-thumb-blue-200::-webkit-scrollbar-thumb {\\r\\n  background: rgba(191, 219, 254, 0.6);\\r\\n}\\r\\n\\r\\n.scrollbar-track-transparent::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\n/* Animation for typing indicator */\\r\\n@keyframes bounce {\\r\\n  0%, 80%, 100% {\\r\\n    transform: scale(0);\\r\\n  }\\r\\n  40% {\\r\\n    transform: scale(1);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-bounce {\\r\\n  animation: bounce 1.4s infinite ease-in-out both;\\r\\n}\\r\\n\\r\\n/* File upload drag and drop styles */\\r\\n.drag-active {\\r\\n  @apply border-blue-400 bg-blue-50;\\r\\n}\\r\\n\\r\\n/* Loading spinner */\\r\\n@keyframes spin {\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-spin {\\r\\n  animation: spin 1s linear infinite;\\r\\n}\\r\\n\\r\\n/* Pulse animation for loading states */\\r\\n@keyframes pulse {\\r\\n  0%, 100% {\\r\\n    opacity: 1;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 0.5;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-pulse-slow {\\r\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\r\\n}\\r\\n\\r\\n/* Floating animation */\\r\\n@keyframes float {\\r\\n  0%, 100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-10px);\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-float {\\r\\n  animation: float 6s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n/* Gradient animation */\\r\\n@keyframes gradient {\\r\\n  0%, 100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-gradient {\\r\\n  background-size: 200% 200%;\\r\\n  animation: gradient 3s ease infinite;\\r\\n}\\r\\n\\r\\n/* Glow effect */\\r\\n.glow {\\r\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\r\\n}\\r\\n\\r\\n.glow-purple {\\r\\n  box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);\\r\\n}\\r\\n\\r\\n/* Smooth transitions */\\r\\n.transition-all-300 {\\r\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\r\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});