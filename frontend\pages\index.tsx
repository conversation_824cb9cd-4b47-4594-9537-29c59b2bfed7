import { useState } from 'react'
import Head from 'next/head'
import FileUpload from '@/components/FileUpload'
import CustomChatInterface from '@/components/CustomChatInterface'

export default function Home() {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)

  return (
    <>
      <Head>
        <title>CA Professional AI Assistant</title>
        <meta name="description" content="AI-powered assistant for CA professionals with document analysis and web search capabilities" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse-slow"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-green-400/20 to-blue-600/20 rounded-full blur-3xl animate-pulse-slow" style={{animationDelay: '1s'}}></div>
        </div>

        <div className="relative container mx-auto px-4 py-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl mb-6 shadow-lg">
              <span className="text-2xl">🚀</span>
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6">
              CA Professional AI Assistant
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Your intelligent assistant for tax, legal, compliance, and business queries.
              Upload documents for analysis or ask questions to get expert-level answers.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-white/20">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <span className="text-xl">🧠</span>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">Domain Expertise</h3>
              <p className="text-gray-600 leading-relaxed">
                Specialized knowledge in CA, tax, legal, and compliance matters
              </p>
            </div>

            <div className="group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-white/20">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <span className="text-xl">📄</span>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">Document Analysis</h3>
              <p className="text-gray-600 leading-relaxed">
                Upload PDFs, Excel files, images, and more for intelligent analysis
              </p>
            </div>

            <div className="group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-white/20">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <span className="text-xl">🌐</span>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">Live Web Search</h3>
              <p className="text-gray-600 leading-relaxed">
                Get current information and latest updates from the web
              </p>
            </div>
          </div>

          {/* Main Chat Interface */}
          <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl overflow-hidden border border-white/20">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                    <span className="text-xl">🤖</span>
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-white">
                      Chat Assistant
                    </h2>
                    <p className="text-blue-100 text-sm">
                      Powered by AI • Always ready to help
                    </p>
                  </div>
                </div>
                <FileUpload
                  onFileUpload={setUploadedFile}
                  uploadedFile={uploadedFile}
                />
              </div>

              {uploadedFile && (
                <div className="mt-4 p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <span className="text-sm">📎</span>
                    </div>
                    <div>
                      <p className="text-white font-medium">
                        Document loaded: {uploadedFile.name}
                      </p>
                      <p className="text-blue-100 text-sm">
                        You can now ask questions about this document
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <CustomChatInterface uploadedFile={uploadedFile} />
          </div>

          {/* Usage Instructions */}
          <div className="mt-12 bg-gradient-to-r from-white/80 to-blue-50/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-white/20">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">How to Use</h3>
              <p className="text-gray-600">Get started with your AI assistant in seconds</p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                    <span className="text-white text-lg">💬</span>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900">Ask Questions</h4>
                </div>
                <div className="space-y-3 ml-13">
                  <div className="flex items-center space-x-3 p-3 bg-white/60 rounded-xl">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700">Tax regulations and compliance</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-white/60 rounded-xl">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">Company law and legal matters</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-white/60 rounded-xl">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-gray-700">Business and accounting queries</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-white/60 rounded-xl">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-gray-700">Current news and updates</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
                    <span className="text-white text-lg">📎</span>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900">Upload Documents</h4>
                </div>
                <div className="space-y-3 ml-13">
                  <div className="flex items-center space-x-3 p-3 bg-white/60 rounded-xl">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-gray-700">PDF documents and reports</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-white/60 rounded-xl">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-gray-700">Excel spreadsheets and CSV files</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-white/60 rounded-xl">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-700">Images with text (OCR supported)</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-white/60 rounded-xl">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">Word documents and text files</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}