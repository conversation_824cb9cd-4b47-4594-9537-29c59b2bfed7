import { useState } from 'react'
import Head from 'next/head'
import { CopilotChat } from '@copilotkit/react-ui'
import { useCopilotAction, useCopilotReadable } from '@copilotkit/react-core'
import FileUpload from '@/components/FileUpload'
import ChatInterface from '@/components/ChatInterface'

export default function Home() {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [sessionId, setSessionId] = useState<string>('')

  // Make file context available to Copilot
  useCopilotReadable({
    description: 'Information about uploaded file',
    value: uploadedFile ? {
      name: uploadedFile.name,
      type: uploadedFile.type,
      size: uploadedFile.size
    } : null
  })

  // Action for handling file uploads
  useCopilotAction({
    name: 'uploadDocument',
    description: 'Upload and analyze a document',
    parameters: [
      {
        name: 'file',
        type: 'object',
        description: 'The file to upload and analyze'
      }
    ],
    handler: async ({ file }) => {
      setUploadedFile(file)
      return `File ${file.name} uploaded successfully. You can now ask questions about this document.`
    }
  })

  return (
    <>
      <Head>
        <title>CA Professional AI Assistant</title>
        <meta name="description" content="AI-powered assistant for CA professionals with document analysis and web search capabilities" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              🚀 CA Professional AI Assistant
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Your intelligent assistant for tax, legal, compliance, and business queries. 
              Upload documents for analysis or ask questions to get expert-level answers.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg p-6 shadow-md">
              <div className="text-2xl mb-3">🧠</div>
              <h3 className="text-lg font-semibold mb-2">Domain Expertise</h3>
              <p className="text-gray-600">
                Specialized knowledge in CA, tax, legal, and compliance matters
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-md">
              <div className="text-2xl mb-3">📄</div>
              <h3 className="text-lg font-semibold mb-2">Document Analysis</h3>
              <p className="text-gray-600">
                Upload PDFs, Excel files, images, and more for intelligent analysis
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-md">
              <div className="text-2xl mb-3">🌐</div>
              <h3 className="text-lg font-semibold mb-2">Live Web Search</h3>
              <p className="text-gray-600">
                Get current information and latest updates from the web
              </p>
            </div>
          </div>

          {/* Main Chat Interface */}
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="border-b border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">
                  Chat Assistant
                </h2>
                <FileUpload 
                  onFileUpload={setUploadedFile}
                  uploadedFile={uploadedFile}
                />
              </div>
              
              {uploadedFile && (
                <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    📎 Document loaded: <strong>{uploadedFile.name}</strong>
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    You can now ask questions about this document
                  </p>
                </div>
              )}
            </div>

            <div className="h-96">
              <CopilotChat
                labels={{
                  title: "CA Assistant",
                  initial: "Hello! I'm your CA professional assistant. I can help you with tax, legal, compliance, and business questions. You can also upload documents for analysis. How can I assist you today?",
                }}
                className="h-full"
                showInlineCitations={true}
              />
            </div>
          </div>

          {/* Usage Instructions */}
          <div className="mt-8 bg-white rounded-lg p-6 shadow-md">
            <h3 className="text-lg font-semibold mb-4">How to Use</h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">💬 Ask Questions</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Tax regulations and compliance</li>
                  <li>• Company law and legal matters</li>
                  <li>• Business and accounting queries</li>
                  <li>• Current news and updates</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">📎 Upload Documents</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• PDF documents and reports</li>
                  <li>• Excel spreadsheets and CSV files</li>
                  <li>• Images with text (OCR supported)</li>
                  <li>• Word documents and text files</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}